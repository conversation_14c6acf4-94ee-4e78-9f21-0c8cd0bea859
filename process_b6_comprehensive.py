import pandas as pd
import os
import logging
import math
from datetime import datetime
from urllib.parse import urlparse
import re

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('b6_comprehensive_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def extract_domain(url):
    """Extract main domain from URL, removing subdomains and www."""
    if pd.isna(url) or url == '':
        return None

    try:
        # Clean the URL
        url = str(url).strip()

        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # Remove www prefix
        if domain.startswith('www.'):
            domain = domain[4:]

        # Remove common subdomains (but keep main domain)
        domain_parts = domain.split('.')
        if len(domain_parts) > 2:
            # Keep only the last two parts (domain.tld)
            domain = '.'.join(domain_parts[-2:])

        return domain if domain else None

    except Exception as e:
        logger.debug(f"Error extracting domain from {url}: {e}")
        return None

def create_info_email(domain):
    """Create info@ email from domain."""
    if pd.isna(domain) or domain == '':
        return None
    return f"info@{domain}"

def categorize_b6_business(filename, subfolder):
    """Categorize B6 business type based on filename and subfolder."""
    filename_lower = filename.lower()

    if subfolder == "Health & Medical Services":
        # Healthcare & Medical categories
        if any(word in filename_lower for word in ['clinic', 'medical', 'health', 'hospital', 'physician', 'doctor']):
            if any(word in filename_lower for word in ['dental', 'dentist', 'denture']):
                return 'Dental Services'
            elif any(word in filename_lower for word in ['eye', 'ophthalmology', 'vision']):
                return 'Eye Care Services'
            elif any(word in filename_lower for word in ['child', 'children', 'pediatric', 'neonatal']):
                return 'Pediatric Services'
            elif any(word in filename_lower for word in ['mental', 'psycho']):
                return 'Mental Health Services'
            elif any(word in filename_lower for word in ['surgery', 'surgeon']):
                return 'Surgical Services'
            else:
                return 'General Healthcare'
        elif any(word in filename_lower for word in ['care', 'day care', 'foster']):
            return 'Care Services'
        elif any(word in filename_lower for word in ['pharmacy', 'medicine']):
            return 'Pharmacy & Medicine'
        elif any(word in filename_lower for word in ['acupuncture', 'holistic', 'homeopathic', 'ayurvedic', 'chinese medicine', 'oriental']):
            return 'Alternative Medicine'
        else:
            return 'Healthcare & Medical'

    elif subfolder == "Retail & Consumer Services":
        # Retail & Consumer categories
        if any(word in filename_lower for word in ['dealer', 'auto', 'car', 'vehicle', 'bmw', 'audi', 'acura', 'bentley']):
            return 'Automotive Sales & Service'
        elif any(word in filename_lower for word in ['store', 'shop', 'retail']):
            if any(word in filename_lower for word in ['clothing', 'apparel', 'fashion', 'bridal']):
                return 'Clothing & Fashion'
            elif any(word in filename_lower for word in ['food', 'grocery', 'beverage', 'restaurant', 'bagel']):
                return 'Food & Beverage'
            elif any(word in filename_lower for word in ['furniture', 'bed', 'bedroom']):
                return 'Furniture & Home'
            elif any(word in filename_lower for word in ['beauty', 'cosmetic', 'salon']):
                return 'Beauty & Personal Care'
            elif any(word in filename_lower for word in ['book', 'media', 'entertainment']):
                return 'Books & Entertainment'
            elif any(word in filename_lower for word in ['pet', 'animal', 'bird', 'dog']):
                return 'Pet & Animal Supplies'
            elif any(word in filename_lower for word in ['sport', 'athletic', 'outdoor', 'archery', 'baseball']):
                return 'Sports & Recreation'
            else:
                return 'General Retail'
        elif any(word in filename_lower for word in ['wholesaler', 'distributor', 'supplier']):
            return 'Wholesale & Distribution'
        elif any(word in filename_lower for word in ['repair', 'service', 'maintenance']):
            return 'Repair & Maintenance Services'
        else:
            return 'Retail & Consumer Services'

    return 'Other'

def load_excel_files(folder_path):
    """Load and combine all Excel files from B6 folder and subfolders."""
    logger.info(f"Loading Excel files from: {folder_path}")

    all_dataframes = []
    file_stats = {}

    # Get all Excel files recursively
    excel_files = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.xlsx'):
                full_path = os.path.join(root, file)
                subfolder = os.path.basename(root) if root != folder_path else ""
                excel_files.append((full_path, file, subfolder))

    logger.info(f"Found {len(excel_files)} Excel files to process")

    for i, (file_path, filename, subfolder) in enumerate(excel_files, 1):
        try:
            logger.info(f"Processing: {i}. {filename} (from {subfolder})")

            # Read Excel file
            df = pd.read_excel(file_path, engine='openpyxl')

            if len(df) > 0:
                # Add metadata columns
                df['source_file'] = filename
                df['source_category'] = filename.replace('.xlsx', '')
                df['subfolder'] = subfolder
                df['business_type'] = categorize_b6_business(filename, subfolder)
                df['created_date'] = datetime.now().strftime('%Y-%m-%d')

                all_dataframes.append(df)
                file_stats[filename] = len(df)

                logger.info(f"  - Loaded {len(df)} records from {filename}")
            else:
                logger.warning(f"  - Empty file: {filename}")
                file_stats[filename] = 0

        except Exception as e:
            logger.error(f"  - Error loading {filename}: {e}")
            file_stats[filename] = 0

    if all_dataframes:
        combined_df = pd.concat(all_dataframes, ignore_index=True)
        logger.info(f"Combined dataset shape: {combined_df.shape}")
        return combined_df, file_stats
    else:
        logger.error("No data loaded from any files")
        return pd.DataFrame(), file_stats

def process_domains_and_emails(df):
    """Process domains and create info@ emails."""
    logger.info("Processing domains and emails...")

    # Find website column (could be 'website', 'Website', 'url', etc.)
    website_cols = [col for col in df.columns if 'website' in col.lower() or 'url' in col.lower()]

    if not website_cols:
        logger.warning("No website column found, checking for other URL-like columns")
        # Look for columns that might contain URLs
        for col in df.columns:
            if df[col].dtype == 'object':
                sample_values = df[col].dropna().head(10).astype(str)
                if any('.' in str(val) and ('http' in str(val) or 'www' in str(val) or '.com' in str(val)) for val in sample_values):
                    website_cols = [col]
                    logger.info(f"Found potential website column: {col}")
                    break

    if website_cols:
        website_col = website_cols[0]
        logger.info(f"Using website column: {website_col}")

        # Extract domains
        df['domain'] = None
        mask = df[website_col].notna()
        df.loc[mask, 'domain'] = df.loc[mask, website_col].apply(extract_domain)

        # Create info@ emails
        df['info@ email'] = df['domain'].apply(create_info_email)
    else:
        logger.warning("No website column found - creating empty domain and email columns")
        df['domain'] = None
        df['info@ email'] = None

    # Log results
    total_records = len(df)
    records_with_domain = df['domain'].notna().sum()
    records_with_email = df['info@ email'].notna().sum()

    logger.info("Domain processing results:")
    logger.info(f"  - Total records: {total_records}")
    logger.info(f"  - Records with domain: {records_with_domain} ({records_with_domain/total_records*100:.1f}%)")
    logger.info(f"  - Records with info@ email: {records_with_email} ({records_with_email/total_records*100:.1f}%)")

    return df

def load_master_database(master_db_path):
    """Load the master database for deduplication."""
    logger.info(f"Loading master database from: {master_db_path}")

    if not os.path.exists(master_db_path):
        logger.error(f"Master database not found: {master_db_path}")
        return pd.DataFrame()

    try:
        # Try to load in chunks due to large file size
        chunk_list = []
        chunk_size = 50000

        for chunk in pd.read_csv(master_db_path, chunksize=chunk_size, low_memory=False):
            chunk_list.append(chunk)

        master_df = pd.concat(chunk_list, ignore_index=True)
        logger.info(f"Loaded master database with {len(master_df)} records")
        return master_df
    except Exception as e:
        logger.error(f"Error loading master database: {e}")
        return pd.DataFrame()

def load_already_processed_domains(file_paths):
    """Load domains from already processed B6 files."""
    logger.info("Loading already processed domains...")

    processed_domains = set()

    for file_path in file_paths:
        if os.path.exists(file_path):
            try:
                # Load in chunks to handle large files
                chunk_list = []
                chunk_size = 50000

                for chunk in pd.read_csv(file_path, chunksize=chunk_size, low_memory=False):
                    if 'domain' in chunk.columns:
                        domains = chunk['domain'].dropna().unique()
                        processed_domains.update(domains)

                logger.info(f"Loaded {len(processed_domains)} domains from {file_path}")
            except Exception as e:
                logger.error(f"Error loading {file_path}: {e}")
        else:
            logger.warning(f"File not found: {file_path}")

    logger.info(f"Total already processed domains: {len(processed_domains)}")
    return processed_domains

def deduplicate_against_master(df, master_df):
    """Remove records that already exist in master database by domain."""
    logger.info("Deduplicating against master database...")

    if master_df.empty:
        logger.warning("Master database is empty - skipping master deduplication")
        return df, 0, len(df)

    # Get unique domains from master database
    if 'domain' in master_df.columns:
        master_domains = set(master_df['domain'].dropna().unique())
        logger.info(f"Master database contains {len(master_domains)} unique domains")
    else:
        logger.warning("No domain column in master database")
        master_domains = set()

    # Filter out records with domains that exist in master
    initial_count = len(df)
    df_filtered = df[~df['domain'].isin(master_domains)].copy()
    final_count = len(df_filtered)

    duplicates_removed = initial_count - final_count

    logger.info("Master deduplication results:")
    logger.info(f"  - Initial records: {initial_count}")
    logger.info(f"  - Duplicates removed: {duplicates_removed}")
    logger.info(f"  - Unique records: {final_count}")
    logger.info(f"  - Deduplication rate: {duplicates_removed/initial_count*100:.1f}%")

    return df_filtered, duplicates_removed, final_count

def deduplicate_against_processed(df, processed_domains):
    """Remove records with domains that were already processed."""
    logger.info("Deduplicating against already processed domains...")

    initial_count = len(df)

    # Filter out records with domains that were already processed
    df_filtered = df[~df['domain'].isin(processed_domains)].copy()
    final_count = len(df_filtered)

    duplicates_removed = initial_count - final_count

    logger.info("Already processed deduplication results:")
    logger.info(f"  - Initial records: {initial_count}")
    logger.info(f"  - Already processed removed: {duplicates_removed}")
    logger.info(f"  - New records: {final_count}")
    logger.info(f"  - Already processed rate: {duplicates_removed/initial_count*100:.1f}%")

    return df_filtered, duplicates_removed, final_count

def deduplicate_by_domain(df):
    """Remove duplicate records by domain within the dataset."""
    logger.info("Deduplicating records by domain within dataset...")

    initial_count = len(df)

    # Separate records with and without domains
    df_with_domain = df[df['domain'].notna()].copy()
    df_without_domain = df[df['domain'].isna()].copy()

    logger.info(f"  - Initial records: {initial_count}")
    logger.info(f"  - Records without domain: {len(df_without_domain)}")
    logger.info(f"  - Records with domain: {len(df_with_domain)}")

    # Deduplicate records with domains (keep first occurrence)
    df_deduped = df_with_domain.drop_duplicates(subset=['domain'], keep='first')

    # Combine back with records without domains
    df_final = pd.concat([df_deduped, df_without_domain], ignore_index=True)

    final_count = len(df_final)
    duplicates_removed = len(df_with_domain) - len(df_deduped)

    logger.info(f"  - Duplicates removed: {duplicates_removed}")
    logger.info(f"  - Final unique records: {final_count}")
    if len(df_with_domain) > 0:
        logger.info(f"  - Deduplication rate: {duplicates_removed/len(df_with_domain)*100:.1f}%")
    else:
        logger.info(f"  - Deduplication rate: 0.0% (no records with domains)")

    return df_final

def get_file_size_mb(file_path):
    """Get file size in MB."""
    if os.path.exists(file_path):
        return os.path.getsize(file_path) / (1024 * 1024)
    return 0

def estimate_rows_per_50mb(df, sample_size=1000):
    """Estimate how many rows fit in 50MB based on a sample."""
    logger.info("Estimating optimal file split size...")

    # Take a sample and save it to estimate size
    sample_df = df.head(min(sample_size, len(df)))
    temp_file = "temp_sample_b6_comprehensive.csv"
    sample_df.to_csv(temp_file, index=False)

    sample_size_mb = get_file_size_mb(temp_file)
    os.remove(temp_file)

    # Calculate rows per MB
    rows_per_mb = sample_size / sample_size_mb

    # Target 45MB to leave some buffer for 50MB limit
    target_mb = 45
    estimated_rows = int(rows_per_mb * target_mb)

    logger.info(f"  - Sample size: {sample_size} rows = {sample_size_mb:.2f} MB")
    logger.info(f"  - Estimated rows per MB: {rows_per_mb:.0f}")
    logger.info(f"  - Target rows per file (45MB): {estimated_rows:,}")

    return estimated_rows

def split_dataframe(df, rows_per_file, base_filename):
    """Split dataframe into multiple files."""
    logger.info(f"Splitting dataframe into files of {rows_per_file:,} rows each...")

    total_rows = len(df)
    num_files = math.ceil(total_rows / rows_per_file)

    logger.info(f"  - Total rows: {total_rows:,}")
    logger.info(f"  - Rows per file: {rows_per_file:,}")
    logger.info(f"  - Number of files: {num_files}")

    created_files = []

    for i in range(num_files):
        start_idx = i * rows_per_file
        end_idx = min((i + 1) * rows_per_file, total_rows)

        # Create chunk
        chunk_df = df.iloc[start_idx:end_idx].copy()

        # Generate filename
        filename = f"{base_filename}_part{i+1:02d}.csv"

        # Save chunk
        chunk_df.to_csv(filename, index=False)

        # Check file size
        file_size_mb = get_file_size_mb(filename)

        logger.info(f"  - Created {filename}: {len(chunk_df):,} rows, {file_size_mb:.1f} MB")

        created_files.append({
            'filename': filename,
            'rows': len(chunk_df),
            'size_mb': file_size_mb
        })

    return created_files

def create_new_master_database(master_df, new_df, output_filename):
    """Create new master database combining existing and new records."""
    logger.info("Creating new master database...")

    # Combine the dataframes
    combined_df = pd.concat([master_df, new_df], ignore_index=True)

    logger.info(f"  - Existing master records: {len(master_df):,}")
    logger.info(f"  - New records to add: {len(new_df):,}")
    logger.info(f"  - Combined total records: {len(combined_df):,}")

    # Save the new master database
    combined_df.to_csv(output_filename, index=False)
    file_size_mb = get_file_size_mb(output_filename)

    logger.info(f"  - New master database saved: {output_filename}")
    logger.info(f"  - File size: {file_size_mb:.1f} MB")

    return combined_df, file_size_mb

def generate_processing_report(file_stats, processing_stats, split_files, master_info, output_dir):
    """Generate a detailed processing report."""
    report_path = os.path.join(output_dir, 'b6_comprehensive_processing_report.txt')

    with open(report_path, 'w') as f:
        f.write("B6 Comprehensive Processing Report\n")
        f.write("=" * 35 + "\n")
        f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("File Loading Statistics:\n")
        f.write("-" * 25 + "\n")
        total_files = len(file_stats)
        total_records = sum(file_stats.values())
        f.write(f"Total files processed: {total_files}\n")
        f.write(f"Total records loaded: {total_records:,}\n\n")

        f.write("Files processed (showing first 30):\n")
        for i, (filename, count) in enumerate(list(file_stats.items())[:30], 1):
            f.write(f"  {i:2d}. {filename}: {count:,} records\n")
        if len(file_stats) > 30:
            f.write(f"  ... and {len(file_stats) - 30} more files\n")
        f.write("\n")

        f.write("Processing Statistics:\n")
        f.write("-" * 22 + "\n")
        for key, value in processing_stats.items():
            if isinstance(value, (int, float)):
                f.write(f"{key}: {value:,}\n")
            else:
                f.write(f"{key}: {value}\n")
        f.write("\n")

        f.write("Split Files Created:\n")
        f.write("-" * 20 + "\n")
        total_size = 0
        for i, file_info in enumerate(split_files, 1):
            f.write(f"{i:2d}. {file_info['filename']}: {file_info['rows']:,} rows, {file_info['size_mb']:.1f} MB\n")
            total_size += file_info['size_mb']

        f.write(f"\nTotal split files: {len(split_files)}\n")
        f.write(f"Total size: {total_size:.1f} MB\n")
        f.write(f"Average file size: {total_size/len(split_files):.1f} MB\n")
        f.write(f"All files under 50MB: {'Yes' if all(f['size_mb'] < 50 for f in split_files) else 'No'}\n\n")

        f.write("Master Database Update:\n")
        f.write("-" * 23 + "\n")
        f.write(f"New master database: {master_info['filename']}\n")
        f.write(f"Total records: {master_info['total_records']:,}\n")
        f.write(f"File size: {master_info['file_size_mb']:.1f} MB\n")

    logger.info(f"Processing report saved to: {report_path}")

def main():
    """Main function to process B6 dataset comprehensively."""
    logger.info("Starting B6 comprehensive processing...")

    # Configuration
    folder_path = "B6"
    master_db_path = "master_business_database_2025-06-26_v2.csv"
    already_processed_files = [
        "b6_deduped_by_domain_part01.csv",
        "b6_deduped_by_domain_part02.csv"
    ]
    base_output_name = "b6_new_deduped_by_domain"
    today_date = datetime.now().strftime('%Y-%m-%d')
    new_master_filename = f"master_business_database_{today_date}.csv"

    try:
        # Step 1: Load and merge all Excel files
        logger.info("Step 1: Loading and merging Excel files...")
        combined_df, file_stats = load_excel_files(folder_path)

        if combined_df.empty:
            logger.error("No data loaded. Exiting.")
            return

        # Step 2: Process domains and emails
        logger.info("Step 2: Processing domains and emails...")
        processed_df = process_domains_and_emails(combined_df)

        # Step 3: Load master database
        logger.info("Step 3: Loading master database...")
        master_df = load_master_database(master_db_path)

        # Step 4: Load already processed domains
        logger.info("Step 4: Loading already processed domains...")
        processed_domains = load_already_processed_domains(already_processed_files)

        # Step 5: Deduplicate against master
        logger.info("Step 5: Deduplicating against master database...")
        deduplicated_df, master_duplicate_count, unique_after_master = deduplicate_against_master(processed_df, master_df)

        # Step 6: Deduplicate against already processed
        logger.info("Step 6: Deduplicating against already processed domains...")
        deduplicated_df2, processed_duplicate_count, unique_after_processed = deduplicate_against_processed(deduplicated_df, processed_domains)

        # Step 7: Deduplicate by domain within dataset
        logger.info("Step 7: Deduplicating by domain within dataset...")
        final_df = deduplicate_by_domain(deduplicated_df2)

        # Step 8: Estimate optimal split size and split
        logger.info("Step 8: Estimating optimal split size...")
        rows_per_file = estimate_rows_per_50mb(final_df)

        logger.info("Step 9: Splitting data into files...")
        split_files = split_dataframe(final_df, rows_per_file, base_output_name)

        # Step 10: Create new master database
        logger.info("Step 10: Creating new master database...")
        new_master_df, master_file_size = create_new_master_database(master_df, final_df, new_master_filename)

        # Collect processing statistics
        processing_stats = {
            'total_files_processed': len(file_stats),
            'total_records_loaded': sum(file_stats.values()),
            'records_after_domain_processing': len(processed_df),
            'records_with_domains': processed_df['domain'].notna().sum(),
            'records_with_info_emails': processed_df['info@ email'].notna().sum(),
            'master_duplicates_removed': master_duplicate_count,
            'records_after_master_dedup': unique_after_master,
            'already_processed_duplicates_removed': processed_duplicate_count,
            'records_after_processed_dedup': unique_after_processed,
            'internal_duplicates_removed': len(deduplicated_df2) - len(final_df),
            'final_unique_records': len(final_df),
            'total_deduplication_rate_percent': round((sum(file_stats.values()) - len(final_df))/sum(file_stats.values())*100, 1),
            'files_created': len(split_files)
        }

        master_info = {
            'filename': new_master_filename,
            'total_records': len(new_master_df),
            'file_size_mb': master_file_size
        }

        # Step 11: Generate processing report
        logger.info("Step 11: Generating processing report...")
        generate_processing_report(file_stats, processing_stats, split_files, master_info, ".")

        logger.info("Processing completed successfully!")
        logger.info(f"Created {len(split_files)} split files, all under 50MB")
        logger.info(f"Created new master database: {new_master_filename}")

        # Summary
        total_size = sum(f['size_mb'] for f in split_files)
        logger.info(f"\nSUMMARY:")
        logger.info(f"- Original records: {sum(file_stats.values()):,}")
        logger.info(f"- Final unique records: {len(final_df):,}")
        logger.info(f"- Total deduplication rate: {processing_stats['total_deduplication_rate_percent']}%")
        logger.info(f"- Split files created: {len(split_files)}")
        logger.info(f"- Total split files size: {total_size:.1f} MB")
        logger.info(f"- New master database size: {master_file_size:.1f} MB")

    except Exception as e:
        logger.error(f"Processing failed: {e}")
        raise

if __name__ == "__main__":
    main()
