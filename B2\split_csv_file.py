#!/usr/bin/env python3
"""
<PERSON>ript to split the final CSV file into chunks of 50MB each.
"""

import pandas as pd
import os
import math

def get_file_size_mb(filepath):
    """Get file size in MB."""
    return os.path.getsize(filepath) / (1024 * 1024)

def split_csv_file():
    """
    Split merged_data_final.csv into chunks of 50MB each.
    """
    input_file = "merged_data_final.csv"
    max_size_mb = 50
    
    print(f"Splitting {input_file} into {max_size_mb}MB chunks...")
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: {input_file} not found!")
        return
    
    # Get original file info
    original_size = get_file_size_mb(input_file)
    print(f"Original file size: {original_size:.2f} MB")
    
    # Load the data
    print("Loading data...")
    df = pd.read_csv(input_file, low_memory=False)
    
    print(f"Data loaded:")
    print(f"  - Total rows: {len(df):,}")
    print(f"  - Total columns: {len(df.columns)}")
    
    # Estimate rows per chunk
    # Calculate average bytes per row
    avg_bytes_per_row = (original_size * 1024 * 1024) / len(df)
    target_bytes = max_size_mb * 1024 * 1024
    estimated_rows_per_chunk = int(target_bytes / avg_bytes_per_row)
    
    print(f"\nEstimated rows per {max_size_mb}MB chunk: {estimated_rows_per_chunk:,}")
    
    # Calculate number of chunks needed
    total_chunks = math.ceil(len(df) / estimated_rows_per_chunk)
    print(f"Estimated number of chunks: {total_chunks}")
    
    # Create chunks and save them
    chunks_created = []
    
    for i in range(total_chunks):
        start_idx = i * estimated_rows_per_chunk
        end_idx = min((i + 1) * estimated_rows_per_chunk, len(df))
        
        chunk_df = df.iloc[start_idx:end_idx].copy()
        
        # Create filename
        chunk_filename = f"merged_data_final_part_{i+1:02d}.csv"
        
        print(f"\nCreating chunk {i+1}/{total_chunks}: {chunk_filename}")
        print(f"  - Rows: {len(chunk_df):,} (from {start_idx:,} to {end_idx-1:,})")
        
        # Save chunk
        chunk_df.to_csv(chunk_filename, index=False, encoding='utf-8')
        
        # Check actual file size
        actual_size = get_file_size_mb(chunk_filename)
        print(f"  - File size: {actual_size:.2f} MB")
        
        # If file is too large, we need to adjust
        if actual_size > max_size_mb * 1.1:  # Allow 10% tolerance
            print(f"  - Warning: File exceeds {max_size_mb}MB limit!")
            
            # Recalculate with smaller chunk size
            adjustment_factor = max_size_mb / actual_size
            new_chunk_size = int(len(chunk_df) * adjustment_factor * 0.9)  # 90% to be safe
            
            print(f"  - Adjusting chunk size to {new_chunk_size:,} rows")
            
            # Re-create the chunk with adjusted size
            adjusted_chunk = df.iloc[start_idx:start_idx + new_chunk_size].copy()
            adjusted_chunk.to_csv(chunk_filename, index=False, encoding='utf-8')
            
            actual_size = get_file_size_mb(chunk_filename)
            print(f"  - Adjusted file size: {actual_size:.2f} MB")
            
            # Update the estimated rows per chunk for next iterations
            estimated_rows_per_chunk = new_chunk_size
            
            # Recalculate total chunks needed
            remaining_rows = len(df) - (start_idx + new_chunk_size)
            if remaining_rows > 0:
                additional_chunks = math.ceil(remaining_rows / estimated_rows_per_chunk)
                total_chunks = i + 1 + additional_chunks
                print(f"  - Updated total chunks estimate: {total_chunks}")
        
        chunks_created.append({
            'filename': chunk_filename,
            'rows': len(chunk_df),
            'size_mb': actual_size,
            'start_row': start_idx,
            'end_row': end_idx - 1
        })
        
        # Break if we've processed all rows
        if end_idx >= len(df):
            break
    
    # Summary
    print(f"\n" + "="*60)
    print(f"SPLITTING COMPLETED SUCCESSFULLY!")
    print(f"="*60)
    
    print(f"\nOriginal file:")
    print(f"  - {input_file}: {original_size:.2f} MB, {len(df):,} rows")
    
    print(f"\nCreated {len(chunks_created)} chunk files:")
    total_chunk_size = 0
    total_chunk_rows = 0
    
    for i, chunk in enumerate(chunks_created, 1):
        print(f"  {i}. {chunk['filename']}")
        print(f"     - Size: {chunk['size_mb']:.2f} MB")
        print(f"     - Rows: {chunk['rows']:,} (rows {chunk['start_row']:,} to {chunk['end_row']:,})")
        print(f"     - Within limit: {'✓' if chunk['size_mb'] <= max_size_mb else '✗'}")
        
        total_chunk_size += chunk['size_mb']
        total_chunk_rows += chunk['rows']
    
    print(f"\nVerification:")
    print(f"  - Total chunk size: {total_chunk_size:.2f} MB")
    print(f"  - Total chunk rows: {total_chunk_rows:,}")
    print(f"  - Size difference: {abs(original_size - total_chunk_size):.2f} MB")
    print(f"  - Row count match: {'✓' if total_chunk_rows == len(df) else '✗'}")
    
    # Check if all chunks are within size limit
    oversized_chunks = [c for c in chunks_created if c['size_mb'] > max_size_mb]
    if oversized_chunks:
        print(f"\n⚠️  Warning: {len(oversized_chunks)} chunks exceed {max_size_mb}MB:")
        for chunk in oversized_chunks:
            print(f"  - {chunk['filename']}: {chunk['size_mb']:.2f} MB")
    else:
        print(f"\n✅ All chunks are within the {max_size_mb}MB limit!")
    
    # Create a manifest file
    manifest_filename = "file_split_manifest.txt"
    print(f"\nCreating manifest file: {manifest_filename}")
    
    with open(manifest_filename, 'w') as f:
        f.write("FILE SPLIT MANIFEST\n")
        f.write("==================\n\n")
        f.write(f"Original file: {input_file}\n")
        f.write(f"Original size: {original_size:.2f} MB\n")
        f.write(f"Original rows: {len(df):,}\n")
        f.write(f"Split date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Target chunk size: {max_size_mb} MB\n")
        f.write(f"Total chunks created: {len(chunks_created)}\n\n")
        
        f.write("CHUNK DETAILS:\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'Filename':<35} {'Size (MB)':<10} {'Rows':<10} {'Row Range':<20}\n")
        f.write("-" * 80 + "\n")
        
        for chunk in chunks_created:
            row_range = f"{chunk['start_row']:,}-{chunk['end_row']:,}"
            f.write(f"{chunk['filename']:<35} {chunk['size_mb']:<10.2f} {chunk['rows']:<10,} {row_range:<20}\n")
        
        f.write("-" * 80 + "\n")
        f.write(f"{'TOTAL':<35} {total_chunk_size:<10.2f} {total_chunk_rows:<10,}\n")
    
    print(f"Manifest file created successfully!")
    
    return chunks_created

if __name__ == "__main__":
    split_csv_file()
