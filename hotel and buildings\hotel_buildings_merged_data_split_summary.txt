FILE SPLIT SUMMARY
==================

Original file: hotel and buildings/hotel_buildings_merged_data.csv
Original size: 610.76 MB
Original rows: 2,438,570

Split into 13 parts:
- hotel_buildings_merged_data_part1.csv: 51.96 MB (187,583 rows)
- hotel_buildings_merged_data_part2.csv: 43.08 MB (187,583 rows)
- hotel_buildings_merged_data_part3.csv: 49.29 MB (187,583 rows)
- hotel_buildings_merged_data_part4.csv: 50.91 MB (187,583 rows)
- hotel_buildings_merged_data_part5.csv: 45.50 MB (187,583 rows)
- hotel_buildings_merged_data_part6.csv: 43.31 MB (187,583 rows)
- hotel_buildings_merged_data_part7.csv: 45.63 MB (187,583 rows)
- hotel_buildings_merged_data_part8.csv: 45.58 MB (187,583 rows)
- hotel_buildings_merged_data_part9.csv: 45.69 MB (187,583 rows)
- hotel_buildings_merged_data_part10.csv: 47.33 MB (187,583 rows)
- hotel_buildings_merged_data_part11.csv: 46.24 MB (187,583 rows)
- hotel_buildings_merged_data_part12.csv: 47.18 MB (187,583 rows)
- hotel_buildings_merged_data_part13.csv: 42.23 MB (187,574 rows)

To recombine files:
```python
import pandas as pd
parts = []
parts.append(pd.read_csv('hotel_buildings_merged_data_part1.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part2.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part3.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part4.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part5.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part6.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part7.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part8.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part9.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part10.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part11.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part12.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_data_part13.csv'))
combined = pd.concat(parts, ignore_index=True)
combined.to_csv('hotel_buildings_merged_data_recombined.csv', index=False)
```
