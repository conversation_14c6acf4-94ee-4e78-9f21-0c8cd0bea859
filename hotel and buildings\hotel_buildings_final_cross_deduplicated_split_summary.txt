FILE SPLIT SUMMARY
==================

Original file: hotel and buildings/hotel_buildings_final_cross_deduplicated.csv
Original size: 129.42 MB
Original rows: 467,162

Split into 3 parts:
- hotel_buildings_final_cross_deduplicated_part1.csv: 43.21 MB (155,721 rows)
- hotel_buildings_final_cross_deduplicated_part2.csv: 42.90 MB (155,721 rows)
- hotel_buildings_final_cross_deduplicated_part3.csv: 43.30 MB (155,720 rows)

To recombine files:
```python
import pandas as pd
parts = []
parts.append(pd.read_csv('hotel_buildings_final_cross_deduplicated_part1.csv'))
parts.append(pd.read_csv('hotel_buildings_final_cross_deduplicated_part2.csv'))
parts.append(pd.read_csv('hotel_buildings_final_cross_deduplicated_part3.csv'))
combined = pd.concat(parts, ignore_index=True)
combined.to_csv('hotel_buildings_final_cross_deduplicated_recombined.csv', index=False)
```
