#!/usr/bin/env python3
"""
Deduplicate B6 combined file against master business database by domain.
Removes records from B6 that already exist in the master database.
"""

import pandas as pd
import logging
import os
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('b6_master_dedup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_file_size_mb(filename):
    """Get file size in MB."""
    return os.path.getsize(filename) / (1024 * 1024)

def load_master_database(filename):
    """Load the master database and extract domains."""
    logger.info(f"Loading master database: {filename}")
    
    try:
        # Load master database
        master_df = pd.read_csv(filename, low_memory=False)
        logger.info(f"Loaded master database with {len(master_df):,} records")
        
        # Extract domains and clean them
        if 'domain' in master_df.columns:
            master_domains = set(master_df['domain'].dropna().str.strip().str.lower())
            logger.info(f"Extracted {len(master_domains):,} unique domains from master database")
            return master_domains
        else:
            logger.error("No 'domain' column found in master database")
            return set()
            
    except Exception as e:
        logger.error(f"Error loading master database: {e}")
        raise

def load_b6_file_in_chunks(filename, master_domains, chunk_size=50000):
    """Load B6 file in chunks and filter out duplicates."""
    logger.info(f"Loading B6 file: {filename}")
    
    try:
        # Get total rows for progress tracking
        total_rows = sum(1 for line in open(filename, 'r', encoding='utf-8')) - 1  # -1 for header
        logger.info(f"Total rows in B6 file: {total_rows:,}")
        
        unique_records = []
        duplicate_count = 0
        processed_count = 0
        
        # Process file in chunks
        for chunk_num, chunk_df in enumerate(pd.read_csv(filename, chunksize=chunk_size, low_memory=False), 1):
            logger.info(f"Processing chunk {chunk_num}: {len(chunk_df):,} rows")
            
            # Clean domains in chunk
            if 'domain' in chunk_df.columns:
                chunk_df['domain_clean'] = chunk_df['domain'].fillna('').str.strip().str.lower()
                
                # Filter out records that exist in master database
                before_count = len(chunk_df)
                chunk_df_filtered = chunk_df[~chunk_df['domain_clean'].isin(master_domains)]
                after_count = len(chunk_df_filtered)
                
                chunk_duplicates = before_count - after_count
                duplicate_count += chunk_duplicates
                
                # Remove the temporary clean domain column
                chunk_df_filtered = chunk_df_filtered.drop('domain_clean', axis=1)
                
                # Add to unique records
                if not chunk_df_filtered.empty:
                    unique_records.append(chunk_df_filtered)
                
                processed_count += before_count
                logger.info(f"  - Chunk {chunk_num}: {chunk_duplicates:,} duplicates removed, {after_count:,} unique records kept")
                logger.info(f"  - Progress: {processed_count:,}/{total_rows:,} ({processed_count/total_rows*100:.1f}%)")
            else:
                logger.error("No 'domain' column found in B6 file")
                return None, 0, 0
        
        # Combine all unique records
        if unique_records:
            final_df = pd.concat(unique_records, ignore_index=True)
            logger.info(f"Final unique records: {len(final_df):,}")
        else:
            final_df = pd.DataFrame()
            logger.info("No unique records found")
        
        return final_df, duplicate_count, processed_count
        
    except Exception as e:
        logger.error(f"Error processing B6 file: {e}")
        raise

def save_deduplicated_file(df, output_filename):
    """Save the deduplicated dataframe to CSV."""
    logger.info(f"Saving deduplicated file: {output_filename}")
    
    try:
        df.to_csv(output_filename, index=False)
        file_size_mb = get_file_size_mb(output_filename)
        
        logger.info(f"Deduplicated file saved: {output_filename}")
        logger.info(f"File size: {file_size_mb:.1f} MB")
        logger.info(f"Total records: {len(df):,}")
        
        return file_size_mb
        
    except Exception as e:
        logger.error(f"Error saving deduplicated file: {e}")
        raise

def generate_deduplication_report(original_count, duplicate_count, final_count, output_filename, file_size_mb):
    """Generate a detailed deduplication report."""
    report_path = 'b6_master_dedup_report.txt'
    
    logger.info(f"Generating deduplication report: {report_path}")
    
    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("B6 vs Master Database Deduplication Report\n")
            f.write("=" * 45 + "\n")
            f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("Deduplication Statistics:\n")
            f.write("-" * 26 + "\n")
            f.write(f"Initial B6 records: {original_count:,}\n")
            f.write(f"Duplicates found in master: {duplicate_count:,}\n")
            f.write(f"Final unique records: {final_count:,}\n")
            f.write(f"Deduplication rate: {duplicate_count/original_count*100:.1f}%\n")
            f.write(f"Data retention rate: {final_count/original_count*100:.1f}%\n\n")
            
            f.write("Output File Information:\n")
            f.write("-" * 25 + "\n")
            f.write(f"Output file: {output_filename}\n")
            f.write(f"File size: {file_size_mb:.1f} MB\n")
            f.write(f"Total records: {final_count:,}\n\n")
            
            f.write("Processing Details:\n")
            f.write("-" * 19 + "\n")
            f.write(f"Master database file: master_business_database_2025-06-26_v2.csv\n")
            f.write(f"B6 input file: b6_combined_deduped_by_domain.csv\n")
            f.write(f"Deduplication method: Domain-based exact match\n")
            f.write(f"Case sensitivity: Ignored (converted to lowercase)\n")
            f.write(f"Processing method: Chunked processing for memory efficiency\n")
        
        logger.info(f"Deduplication report saved to: {report_path}")
        
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        raise

def main():
    """Main function to deduplicate B6 against master database."""
    logger.info("Starting B6 vs Master Database deduplication...")
    
    # Configuration
    master_file = "master_business_database_2025-06-26_v2.csv"
    b6_file = "b6_combined_deduped_by_domain.csv"
    output_file = "b6_deduplicated_against_master.csv"
    
    try:
        # Step 1: Load master database domains
        logger.info("Step 1: Loading master database domains...")
        master_domains = load_master_database(master_file)
        
        if not master_domains:
            logger.error("Failed to load master domains. Exiting.")
            return
        
        # Step 2: Process B6 file and remove duplicates
        logger.info("Step 2: Processing B6 file and removing duplicates...")
        deduplicated_df, duplicate_count, original_count = load_b6_file_in_chunks(b6_file, master_domains)
        
        if deduplicated_df is None:
            logger.error("Failed to process B6 file. Exiting.")
            return
        
        # Step 3: Save deduplicated file
        logger.info("Step 3: Saving deduplicated file...")
        file_size_mb = save_deduplicated_file(deduplicated_df, output_file)
        
        # Step 4: Generate report
        logger.info("Step 4: Generating deduplication report...")
        generate_deduplication_report(original_count, duplicate_count, len(deduplicated_df), output_file, file_size_mb)
        
        # Final summary
        logger.info("Deduplication completed successfully!")
        logger.info(f"Original B6 records: {original_count:,}")
        logger.info(f"Duplicates removed: {duplicate_count:,}")
        logger.info(f"Final unique records: {len(deduplicated_df):,}")
        logger.info(f"Output file: {output_file}")
        logger.info(f"File size: {file_size_mb:.1f} MB")
        
    except Exception as e:
        logger.error(f"Deduplication failed: {e}")
        raise

if __name__ == "__main__":
    main()
