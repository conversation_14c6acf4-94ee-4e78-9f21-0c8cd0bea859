// ENHANCED MAPS API SEARCH QUERIES - Minimal structure for Serper.dev node with pagination
// Generates clean query objects with 'q', 'll', and optional 'page' parameters
// Designed for use with dedicated Serper.dev node that handles API calls and headers
// Covers 1000 major US cities with comprehensive business type searches
// Includes pagination support - typically 16-20 results per page

// Import cities data - you can choose between:
// 1. Top 100 cities (current default for testing)
// 2. Full 1000 cities dataset (see instructions below)

// TO USE ALL 1000 CITIES:
// 1. Run: powershell -ExecutionPolicy Bypass -File download-cities.ps1
// 2. This downloads cities-1000-data.json with all 1000 cities
// 3. Uncomment the line below to load from JSON file:
// const cities = require('./cities-1000-data.json');
const businessTypes = [
  'contractor',
  // 'home builder',
  // 'construction company',
  // 'remodeling contractor',
  // 'renovation contractor',
  // 'custom home builder',
  // 'residential contractor',
  // 'commercial contractor',
  // 'electrician',
  // 'plumber',
  // 'hvac contractor',
  // 'heating contractor',
  // 'air conditioning contractor',
  // 'mechanical contractor',
  // 'refrigeration contractor',
  // 'ventilation contractor',
  // 'insulation contractor',
  // 'roofer',
  // 'roofing contractor',
  // 'siding contractor',
  // 'window contractor',
  // 'door contractor',
  // 'deck builder',
  // 'fence contractor',
  // 'foundation contractor',
  // 'concrete contractor',
  // 'masonry contractor',
  // 'stucco contractor',
  // 'gutter contractor',
  // 'drywall contractor',
  // 'painter',
  // 'flooring contractor',
  // 'tile contractor',
  // 'cabinet installer',
  // 'carpenter',
  // 'millwork contractor',
  // 'bathroom remodeler',
  // 'kitchen remodeler',
  // 'basement remodeler',
  // 'garage builder',
  // 'pool contractor',
  // 'spa contractor',
  // 'solar contractor',
  // 'security system installer',
  // 'low voltage contractor',
  // 'landscaper',
  // 'landscape contractor',
  // 'hardscape contractor',
  // 'irrigation contractor',
  // 'paving contractor',
  // 'asphalt contractor',
  // 'excavation contractor',
  // 'grading contractor',
  // 'septic contractor',
  // 'handyman service',
  // 'demolition contractor',
  // 'welding contractor',
  // 'scaffolding contractor',
  // 'waterproofing contractor',
  // 'restoration contractor',
  // 'mold remediation contractor',
  // 'fire damage contractor',
  // 'environmental contractor',
  // 'asbestos contractor'
];

// OPTION 1: Load all 1000 cities from downloaded JSON (recommended for production)
// Uncomment the line below after running download-cities.ps1:
// const cities = require('./cities-1000-data.json');

// OPTION 2: Use top 100 cities for testing (current active)
const cities = [
  { "city": "New York", "latitude": 40.7127837, "longitude": -74.0059413, "state": "New York" }
];

// OPTION 3: Fetch complete 1000 cities from GitHub Gist (uncomment to use)
/*
async function fetchAllCities() {
  try {
    const response = await fetch('https://gist.githubusercontent.com/Miserlou/c5cd8364bf9b2420bb29/raw/2bf258763cdddd704f8ffd3ea9a3e81d25e2c6f6/cities.json');
    const allCities = await response.json();
    return allCities;
  } catch (error) {
    console.error('Failed to fetch cities data:', error);
    return cities; // fallback to default cities
  }
}
*/

// Maps API pagination settings
const maxPages = 5;  // Number of pages to generate per city/business type
// Note: Maps API supports pagination - typically 16-20 results per page
// Stop when you get fewer results than expected (indicates last page)

const queries = [];

businessTypes.forEach(type => {
  cities.forEach(city => {
    // Generate queries for multiple pages
    for (let page = 1; page <= maxPages; page++) {
      const queryObj = {
        q: type,  // Search query (e.g., "contractor")
        ll: `${city.latitude},${city.longitude},11`,  // latitude,longitude,zoom format

        // Optional metadata for tracking (can be removed if not needed)
        city_name: city.city,
        state: city.state,
        page_number: page,
        query_id: `${type.replace(/\s+/g, '_')}_${city.city.replace(/\s+/g, '_')}_${city.state.replace(/\s+/g, '_')}_page_${page}`
      };

      // Add page parameter for pages 2 and beyond (page 1 doesn't need page parameter)
      if (page > 1) {
        queryObj.page = page;
      }

      queries.push(queryObj);
    }
  });
});

console.log(`Generated ${queries.length} minimal query objects for Serper.dev node`);
console.log(`Business types: ${businessTypes.length}`);
console.log(`Cities: ${cities.length} (currently using top ${cities.length} cities)`);
console.log(`Pages per city/type: ${maxPages}`);
console.log(`Query structure: Minimal with 'q', 'll', and optional 'page' parameters`);

if (cities.length < 1000) {
  console.log(`\n🔄 To use all 1000 cities:`);
  console.log(`   1. Run: powershell -ExecutionPolicy Bypass -File download-cities.ps1`);
  console.log(`   2. Uncomment: const cities = require('./cities-1000-data.json');`);
  console.log(`   3. Comment out the current cities array`);
}

console.log(`\n📄 Pagination Logic:`);
console.log(`   - Page 1: No 'page' parameter needed`);
console.log(`   - Page 2+: Include 'page' parameter`);
console.log(`   - Stop when results < 16-20 (indicates last page)`);

console.log(`\nSample query objects:`);
console.log(`Page 1:`, JSON.stringify(queries[0], null, 2));
if (queries.length > 1) {
  console.log(`Page 2:`, JSON.stringify(queries[1], null, 2));
}

return queries.map(query => ({ json: query }));