import pandas as pd
import os
import logging
import math
from datetime import datetime
from urllib.parse import urlparse
import re

# Set up logging
def setup_logging(log_filename):
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def extract_domain(url):
    """Extract main domain from URL, removing subdomains and www."""
    if pd.isna(url) or url == '':
        return None
    
    try:
        # Clean the URL
        url = str(url).strip()
        
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove www prefix
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # Remove common subdomains (but keep main domain)
        domain_parts = domain.split('.')
        if len(domain_parts) > 2:
            # Keep only the last two parts (domain.tld)
            domain = '.'.join(domain_parts[-2:])
        
        return domain if domain else None
        
    except Exception as e:
        return None

def create_info_email(domain):
    """Create info@ email from domain."""
    if pd.isna(domain) or domain == '':
        return None
    return f"info@{domain}"

def process_domains_and_emails(df, logger):
    """Process domains and create info@ emails."""
    logger.info("Processing domains and emails...")
    
    # Find website column (could be 'website', 'Website', 'url', etc.)
    website_cols = [col for col in df.columns if 'website' in col.lower() or 'url' in col.lower()]
    
    if not website_cols:
        logger.warning("No website column found, checking for other URL-like columns")
        # Look for columns that might contain URLs
        for col in df.columns:
            if df[col].dtype == 'object':
                sample_values = df[col].dropna().head(10).astype(str)
                if any('.' in str(val) and ('http' in str(val) or 'www' in str(val) or '.com' in str(val)) for val in sample_values):
                    website_cols = [col]
                    logger.info(f"Found potential website column: {col}")
                    break
    
    if website_cols:
        website_col = website_cols[0]
        logger.info(f"Using website column: {website_col}")
        
        # Extract domains
        df['domain'] = None
        mask = df[website_col].notna()
        df.loc[mask, 'domain'] = df.loc[mask, website_col].apply(extract_domain)
        
        # Create info@ emails
        df['info@ email'] = df['domain'].apply(create_info_email)
    else:
        logger.warning("No website column found - creating empty domain and email columns")
        df['domain'] = None
        df['info@ email'] = None
    
    # Log results
    total_records = len(df)
    records_with_website = df[website_col].notna().sum() if website_cols else 0
    records_with_domain = df['domain'].notna().sum()
    records_with_email = df['info@ email'].notna().sum()
    
    logger.info("Domain processing results:")
    logger.info(f"  - Total records: {total_records}")
    logger.info(f"  - Records with website: {records_with_website} ({records_with_website/total_records*100:.1f}%)")
    logger.info(f"  - Records with domain: {records_with_domain} ({records_with_domain/total_records*100:.1f}%)")
    logger.info(f"  - Records with info@ email: {records_with_email} ({records_with_email/total_records*100:.1f}%)")
    
    return df

def load_master_database(master_db_path, logger):
    """Load the master database for deduplication."""
    logger.info(f"Loading master database from: {master_db_path}")
    
    if not os.path.exists(master_db_path):
        logger.error(f"Master database not found: {master_db_path}")
        return pd.DataFrame()
    
    master_df = pd.read_csv(master_db_path, low_memory=False)
    logger.info(f"Loaded master database with {len(master_df)} records")
    
    return master_df

def deduplicate_against_master(df, master_df, logger):
    """Remove records that already exist in master database by domain."""
    logger.info("Deduplicating against master database...")
    
    if master_df.empty:
        logger.warning("Master database is empty - skipping master deduplication")
        return df, 0, len(df)
    
    # Get unique domains from master database
    if 'domain' in master_df.columns:
        master_domains = set(master_df['domain'].dropna().unique())
        logger.info(f"Master database contains {len(master_domains)} unique domains")
    else:
        logger.warning("No domain column in master database")
        master_domains = set()
    
    # Filter out records with domains that exist in master
    initial_count = len(df)
    df_filtered = df[~df['domain'].isin(master_domains)].copy()
    final_count = len(df_filtered)
    
    duplicates_removed = initial_count - final_count
    
    logger.info("Master deduplication results:")
    logger.info(f"  - Initial records: {initial_count}")
    logger.info(f"  - Duplicates removed: {duplicates_removed}")
    logger.info(f"  - Unique records: {final_count}")
    logger.info(f"  - Deduplication rate: {duplicates_removed/initial_count*100:.1f}%")
    
    return df_filtered, duplicates_removed, final_count

def deduplicate_by_domain(df, logger):
    """Remove duplicate records by domain within the dataset."""
    logger.info("Deduplicating records by domain within dataset...")
    
    initial_count = len(df)
    
    # Separate records with and without domains
    df_with_domain = df[df['domain'].notna()].copy()
    df_without_domain = df[df['domain'].isna()].copy()
    
    logger.info(f"  - Initial records: {initial_count}")
    logger.info(f"  - Records without domain: {len(df_without_domain)}")
    logger.info(f"  - Records with domain: {len(df_with_domain)}")
    
    # Deduplicate records with domains (keep first occurrence)
    df_deduped = df_with_domain.drop_duplicates(subset=['domain'], keep='first')
    
    # Combine back with records without domains
    df_final = pd.concat([df_deduped, df_without_domain], ignore_index=True)
    
    final_count = len(df_final)
    duplicates_removed = len(df_with_domain) - len(df_deduped)
    
    logger.info(f"  - Duplicates removed: {duplicates_removed}")
    logger.info(f"  - Final unique records: {final_count}")
    logger.info(f"  - Deduplication rate: {duplicates_removed/len(df_with_domain)*100:.1f}%")
    
    return df_final

def filter_records_with_domains_only(df, logger):
    """CORRECTED: Filter to keep only records with valid domains."""
    logger.info("Filtering to keep only records with valid domains...")
    
    initial_count = len(df)
    df_filtered = df[df['domain'].notna()].copy()
    final_count = len(df_filtered)
    
    removed_count = initial_count - final_count
    
    logger.info(f"  - Initial records: {initial_count}")
    logger.info(f"  - Records with domains: {final_count}")
    logger.info(f"  - Records without domains removed: {removed_count}")
    logger.info(f"  - Domain coverage: {final_count/initial_count*100:.1f}%")
    
    return df_filtered

def get_file_size_mb(file_path):
    """Get file size in MB."""
    if os.path.exists(file_path):
        return os.path.getsize(file_path) / (1024 * 1024)
    return 0

def estimate_rows_per_50mb(df, sample_size=1000, temp_prefix="temp_sample"):
    """Estimate how many rows fit in 50MB based on a sample."""
    # Take a sample and save it to estimate size
    sample_df = df.head(min(sample_size, len(df)))
    temp_file = f"{temp_prefix}.csv"
    sample_df.to_csv(temp_file, index=False)
    
    sample_size_mb = get_file_size_mb(temp_file)
    os.remove(temp_file)
    
    # Calculate rows per MB
    rows_per_mb = sample_size / sample_size_mb
    
    # Target 45MB to leave some buffer for 50MB limit
    target_mb = 45
    estimated_rows = int(rows_per_mb * target_mb)
    
    return estimated_rows, rows_per_mb, sample_size_mb

def split_dataframe(df, rows_per_file, base_filename, logger):
    """Split dataframe into multiple files."""
    logger.info(f"Splitting dataframe into files of {rows_per_file:,} rows each...")
    
    total_rows = len(df)
    num_files = math.ceil(total_rows / rows_per_file)
    
    logger.info(f"  - Total rows: {total_rows:,}")
    logger.info(f"  - Rows per file: {rows_per_file:,}")
    logger.info(f"  - Number of files: {num_files}")
    
    created_files = []
    
    for i in range(num_files):
        start_idx = i * rows_per_file
        end_idx = min((i + 1) * rows_per_file, total_rows)
        
        # Create chunk
        chunk_df = df.iloc[start_idx:end_idx].copy()
        
        # Generate filename
        filename = f"{base_filename}_part{i+1:02d}.csv"
        
        # Save chunk
        chunk_df.to_csv(filename, index=False)
        
        # Check file size
        file_size_mb = get_file_size_mb(filename)
        
        # Verify domain coverage in this chunk
        chunk_with_domains = chunk_df['domain'].notna().sum()
        domain_coverage = chunk_with_domains / len(chunk_df) * 100
        
        logger.info(f"  - Created {filename}: {len(chunk_df):,} rows, {file_size_mb:.1f} MB, {domain_coverage:.1f}% with domains")
        
        created_files.append({
            'filename': filename,
            'rows': len(chunk_df),
            'size_mb': file_size_mb,
            'domain_coverage': domain_coverage
        })
    
    return created_files

def generate_processing_report(file_stats, processing_stats, split_files, output_dir, report_name):
    """Generate a detailed processing report."""
    report_path = os.path.join(output_dir, report_name)
    
    with open(report_path, 'w') as f:
        f.write(f"{report_name.replace('_', ' ').replace('.txt', '').title()}\n")
        f.write("=" * len(report_name.replace('_', ' ').replace('.txt', '').title()) + "\n")
        f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("File Loading Statistics:\n")
        f.write("-" * 25 + "\n")
        total_files = len(file_stats)
        total_records = sum(file_stats.values())
        f.write(f"Total files processed: {total_files}\n")
        f.write(f"Total records loaded: {total_records:,}\n\n")
        
        f.write("Files processed (showing first 30):\n")
        for i, (filename, count) in enumerate(list(file_stats.items())[:30], 1):
            f.write(f"  {i:2d}. {filename}: {count:,} records\n")
        if len(file_stats) > 30:
            f.write(f"  ... and {len(file_stats) - 30} more files\n")
        f.write("\n")
        
        f.write("Processing Statistics:\n")
        f.write("-" * 22 + "\n")
        for key, value in processing_stats.items():
            if isinstance(value, (int, float)):
                f.write(f"{key}: {value:,}\n")
            else:
                f.write(f"{key}: {value}\n")
        f.write("\n")
        
        f.write("Split Files Created:\n")
        f.write("-" * 20 + "\n")
        total_size = 0
        for i, file_info in enumerate(split_files, 1):
            f.write(f"{i:2d}. {file_info['filename']}: {file_info['rows']:,} rows, {file_info['size_mb']:.1f} MB, {file_info['domain_coverage']:.1f}% domains\n")
            total_size += file_info['size_mb']
        
        f.write(f"\nTotal split files: {len(split_files)}\n")
        f.write(f"Total size: {total_size:.1f} MB\n")
        f.write(f"Average file size: {total_size/len(split_files):.1f} MB\n")
        f.write(f"All files under 50MB: {'Yes' if all(f['size_mb'] < 50 for f in split_files) else 'No'}\n")
        f.write(f"All files have 100% domain coverage: {'Yes' if all(f['domain_coverage'] == 100.0 for f in split_files) else 'No'}\n")
    
    return report_path
