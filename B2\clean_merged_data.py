#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to clean the merged CSV file by:
1. Removing rows that don't have a website
2. Removing duplicates based on 'cid' column
3. Extract domain from website and populate domain column
4. Create info@{domain} emails for info@ email column
"""

import pandas as pd
import numpy as np
import re
from urllib.parse import urlparse

def extract_domain(website_url):
    """
    Extract domain from website URL.
    """
    if pd.isna(website_url) or website_url == '':
        return None

    try:
        # Clean the URL - remove extra spaces and ensure it has a protocol
        url = str(website_url).strip()

        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # Remove www. prefix if present
        if domain.startswith('www.'):
            domain = domain[4:]

        # Remove any trailing slashes or paths
        domain = domain.split('/')[0]

        # Basic validation - domain should contain at least one dot
        if '.' not in domain or len(domain) < 3:
            return None

        return domain
    except:
        return None

def clean_merged_data():
    """
    Clean the merged_data.csv file by removing rows without websites and duplicates by cid.
    """
    print("Loading merged_data.csv...")

    # Read the CSV file
    df = pd.read_csv('merged_data.csv')

    print(f"Original data shape: {df.shape}")
    print(f"Original rows: {len(df):,}")

    # Check the website column for missing values
    print(f"\nWebsite column analysis:")
    print(f"  - Total rows: {len(df):,}")
    print(f"  - Rows with website (not null): {df['website'].notna().sum():,}")
    print(f"  - Rows without website (null/empty): {df['website'].isna().sum():,}")

    # Check for empty strings in website column
    empty_websites = (df['website'] == '') | (df['website'].isna())
    print(f"  - Rows with empty/null websites: {empty_websites.sum():,}")

    # Step 1: Remove rows without websites
    print(f"\nStep 1: Removing rows without websites...")
    df_with_website = df[df['website'].notna() & (df['website'] != '')]

    print(f"After removing rows without websites:")
    print(f"  - Remaining rows: {len(df_with_website):,}")
    print(f"  - Removed rows: {len(df) - len(df_with_website):,}")

    # Check the cid column for duplicates
    print(f"\nCID column analysis:")
    print(f"  - Total rows with website: {len(df_with_website):,}")
    print(f"  - Unique CIDs: {df_with_website['cid'].nunique():,}")
    print(f"  - Duplicate CIDs: {len(df_with_website) - df_with_website['cid'].nunique():,}")

    # Check for null CIDs
    null_cids = df_with_website['cid'].isna().sum()
    print(f"  - Rows with null CID: {null_cids:,}")

    # Step 2: Remove duplicates based on 'cid', keeping the first occurrence
    print(f"\nStep 2: Removing duplicates based on 'cid'...")

    # First, let's see which source files contribute to duplicates
    if len(df_with_website) > df_with_website['cid'].nunique():
        print("\nAnalyzing duplicates by source file...")
        duplicate_cids = df_with_website[df_with_website.duplicated(subset=['cid'], keep=False)]
        if len(duplicate_cids) > 0:
            duplicate_sources = duplicate_cids['source_file'].value_counts()
            print("Source files with duplicate CIDs:")
            for source, count in duplicate_sources.head(10).items():
                print(f"  - {source}: {count} duplicate entries")

    # Remove duplicates, keeping the first occurrence
    df_cleaned = df_with_website.drop_duplicates(subset=['cid'], keep='first')

    print(f"\nAfter removing duplicates:")
    print(f"  - Remaining rows: {len(df_cleaned):,}")
    print(f"  - Removed duplicate rows: {len(df_with_website) - len(df_cleaned):,}")

    # Final summary
    print(f"\nFinal cleaning summary:")
    print(f"  - Original rows: {len(df):,}")
    print(f"  - Rows removed (no website): {len(df) - len(df_with_website):,}")
    print(f"  - Rows removed (duplicates): {len(df_with_website) - len(df_cleaned):,}")
    print(f"  - Final rows: {len(df_cleaned):,}")
    print(f"  - Total rows removed: {len(df) - len(df_cleaned):,}")
    print(f"  - Retention rate: {(len(df_cleaned) / len(df) * 100):.2f}%")

    # Step 3: Extract domains and create info emails
    print(f"\nStep 3: Extracting domains and creating info emails...")

    # Extract domains from website column
    print("Extracting domains from websites...")
    df_cleaned['domain'] = df_cleaned['website'].apply(extract_domain)

    # Check domain extraction results
    valid_domains = df_cleaned['domain'].notna().sum()
    invalid_domains = df_cleaned['domain'].isna().sum()

    print(f"Domain extraction results:")
    print(f"  - Valid domains extracted: {valid_domains:,}")
    print(f"  - Invalid/failed extractions: {invalid_domains:,}")

    # Create info@domain emails
    print("Creating info@domain emails...")
    df_cleaned['info@ email'] = df_cleaned['domain'].apply(
        lambda x: f"info@{x}" if pd.notna(x) and x != '' else None
    )

    # Check email creation results
    valid_emails = df_cleaned['info@ email'].notna().sum()
    print(f"Info emails created: {valid_emails:,}")

    # Show some examples
    sample_domains = df_cleaned[df_cleaned['domain'].notna()][['website', 'domain', 'info@ email']].head(5)
    print(f"\nSample domain extractions:")
    for idx, row in sample_domains.iterrows():
        print(f"  Website: {row['website']}")
        print(f"  Domain: {row['domain']}")
        print(f"  Email: {row['info@ email']}")
        print()

    # Save the cleaned data
    output_file = "merged_data_cleaned.csv"
    print(f"Saving cleaned data to: {output_file}")

    df_cleaned.to_csv(output_file, index=False, encoding='utf-8')

    # Check file sizes
    import os
    original_size = os.path.getsize('merged_data.csv') / (1024*1024)
    cleaned_size = os.path.getsize(output_file) / (1024*1024)

    print(f"\nFile size comparison:")
    print(f"  - Original file: {original_size:.2f} MB")
    print(f"  - Cleaned file: {cleaned_size:.2f} MB")
    print(f"  - Size reduction: {original_size - cleaned_size:.2f} MB ({((original_size - cleaned_size) / original_size * 100):.2f}%)")

    # Show distribution of remaining data by source file
    print(f"\nRemaining data distribution by source file:")
    source_counts = df_cleaned['source_file'].value_counts()
    for source, count in source_counts.head(10).items():
        print(f"  - {source}: {count:,} rows")

    if len(source_counts) > 10:
        print(f"  ... and {len(source_counts) - 10} more files")

    print(f"\nCleaning completed successfully!")
    return df_cleaned

if __name__ == "__main__":
    clean_merged_data()
