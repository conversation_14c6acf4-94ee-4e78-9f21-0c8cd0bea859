// Script to parse scraped data from output.json
const fs = require('fs');

function parseScrapedData() {
  try {
    // Read the output.json file
    const rawData = fs.readFileSync('logs/output.json', 'utf8');
    const scrapedData = JSON.parse(rawData);
    
    console.log('=== SCRAPED DATA ANALYSIS ===\n');
    
    // Check if it's an array of results
    if (Array.isArray(scrapedData)) {
      console.log(`📊 Total scraped items: ${scrapedData.length}\n`);
      
      scrapedData.forEach((item, index) => {
        console.log(`--- Item ${index + 1} ---`);
        
        // Parse basic info
        if (item.cid) {
          console.log(`🆔 CID: ${item.cid}`);
        }
        
        // Parse HTML content if present
        if (item.data) {
          const htmlContent = item.data;
          
          // Extract business information from HTML
          const businessInfo = extractBusinessInfo(htmlContent);
          
          console.log('🏢 Business Information:');
          Object.entries(businessInfo).forEach(([key, value]) => {
            if (value) {
              console.log(`   ${key}: ${value}`);
            }
          });
        }
        
        console.log(''); // Empty line for separation
      });
      
    } else {
      // Single item
      console.log('📄 Single scraped item detected\n');
      
      if (scrapedData.data) {
        const businessInfo = extractBusinessInfo(scrapedData.data);
        
        console.log('🏢 Business Information:');
        Object.entries(businessInfo).forEach(([key, value]) => {
          if (value) {
            console.log(`   ${key}: ${value}`);
          }
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Error parsing scraped data:', error.message);
  }
}

function extractBusinessInfo(htmlContent) {
  const businessInfo = {
    title: null,
    description: null,
    phone: null,
    email: null,
    address: null,
    website: null,
    businessName: null,
    services: []
  };
  
  try {
    // Extract title
    const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch) {
      businessInfo.title = titleMatch[1].trim();
    }
    
    // Extract meta description
    const descMatch = htmlContent.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
    if (descMatch) {
      businessInfo.description = descMatch[1].trim();
    }
    
    // Extract phone numbers
    const phoneRegex = /(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})/g;
    const phoneMatches = htmlContent.match(phoneRegex);
    if (phoneMatches && phoneMatches.length > 0) {
      businessInfo.phone = phoneMatches[0].trim();
    }
    
    // Extract email addresses
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
    const emailMatches = htmlContent.match(emailRegex);
    if (emailMatches && emailMatches.length > 0) {
      businessInfo.email = emailMatches[0].trim();
    }
    
    // Extract JSON-LD structured data
    const jsonLdMatch = htmlContent.match(/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/is);
    if (jsonLdMatch) {
      try {
        const jsonData = JSON.parse(jsonLdMatch[1]);
        
        if (jsonData['@graph']) {
          // Handle @graph structure
          jsonData['@graph'].forEach(item => {
            extractFromJsonLd(item, businessInfo);
          });
        } else {
          // Handle direct structure
          extractFromJsonLd(jsonData, businessInfo);
        }
      } catch (e) {
        console.log('   ⚠️  Could not parse JSON-LD data');
      }
    }
    
    // Extract Open Graph data
    const ogTitleMatch = htmlContent.match(/<meta[^>]*property=["\']og:title["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
    if (ogTitleMatch && !businessInfo.businessName) {
      businessInfo.businessName = ogTitleMatch[1].trim();
    }
    
    // Extract canonical URL
    const canonicalMatch = htmlContent.match(/<link[^>]*rel=["\']canonical["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i);
    if (canonicalMatch) {
      businessInfo.website = canonicalMatch[1].trim();
    }
    
  } catch (error) {
    console.log('   ⚠️  Error extracting business info:', error.message);
  }
  
  return businessInfo;
}

function extractFromJsonLd(jsonData, businessInfo) {
  // Extract business name
  if (jsonData.name && !businessInfo.businessName) {
    businessInfo.businessName = jsonData.name;
  }
  
  // Extract phone
  if (jsonData.telephone && !businessInfo.phone) {
    businessInfo.phone = jsonData.telephone;
  }
  
  // Extract email
  if (jsonData.email && !businessInfo.email) {
    businessInfo.email = jsonData.email;
  }
  
  // Extract address
  if (jsonData.address) {
    if (typeof jsonData.address === 'string') {
      businessInfo.address = jsonData.address;
    } else if (jsonData.address.streetAddress) {
      const addr = jsonData.address;
      businessInfo.address = `${addr.streetAddress || ''} ${addr.addressLocality || ''} ${addr.addressRegion || ''} ${addr.postalCode || ''}`.trim();
    }
  }
  
  // Extract URL
  if (jsonData.url && !businessInfo.website) {
    businessInfo.website = jsonData.url;
  }
  
  // Extract description
  if (jsonData.description && !businessInfo.description) {
    businessInfo.description = jsonData.description;
  }
}

// Run the parser
parseScrapedData();
