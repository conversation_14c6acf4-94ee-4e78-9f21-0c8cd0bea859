#!/usr/bin/env python3
"""
Final check and fix for any remaining duplicates.
"""

import pandas as pd

def final_dedup_check():
    print("Loading merged_data_final.csv for final deduplication check...")
    
    df = pd.read_csv('merged_data_final.csv', low_memory=False)
    
    print(f"Current data:")
    print(f"  - Total rows: {len(df):,}")
    print(f"  - Unique domains: {df['domain'].nunique():,}")
    
    # Check for duplicates
    duplicates = df[df.duplicated(subset=['domain'], keep=False)]
    
    if len(duplicates) > 0:
        print(f"\nFound {len(duplicates)} rows with duplicate domains:")
        
        duplicate_domains = duplicates['domain'].value_counts()
        print("Duplicate domains:")
        for domain, count in duplicate_domains.items():
            print(f"  {domain}: {count} entries")
        
        print("\nSample duplicate entries:")
        for domain in duplicate_domains.index[:3]:
            domain_entries = duplicates[duplicates['domain'] == domain][['title', 'domain', 'source_file']]
            print(f"\nDomain: {domain}")
            for _, row in domain_entries.iterrows():
                print(f"  - {row['title']} (from {row['source_file']})")
        
        # Remove duplicates, keeping first occurrence
        print(f"\nRemoving duplicates...")
        df_final = df.drop_duplicates(subset=['domain'], keep='first')
        
        print(f"After final deduplication:")
        print(f"  - Rows: {len(df_final):,}")
        print(f"  - Unique domains: {df_final['domain'].nunique():,}")
        print(f"  - Removed: {len(df) - len(df_final)} duplicate rows")
        
        # Save the perfectly deduplicated file
        df_final.to_csv('merged_data_final.csv', index=False, encoding='utf-8')
        print("Saved perfectly deduplicated file.")
        
    else:
        print("No duplicates found - file is already perfectly deduplicated!")
        df_final = df
    
    # Final verification
    print(f"\nFinal verification:")
    print(f"  - Total rows: {len(df_final):,}")
    print(f"  - Unique domains: {df_final['domain'].nunique():,}")
    print(f"  - Perfect deduplication: {len(df_final) == df_final['domain'].nunique()}")
    
    # Show some examples of cleaned domains
    print(f"\nExamples of cleaned domains:")
    examples = df_final[['title', 'website', 'domain', 'info@ email']].head(5)
    for _, row in examples.iterrows():
        print(f"  Business: {row['title'][:40]}...")
        print(f"  Domain: {row['domain']}")
        print(f"  Email: {row['info@ email']}")
        print()
    
    return df_final

if __name__ == "__main__":
    final_dedup_check()
