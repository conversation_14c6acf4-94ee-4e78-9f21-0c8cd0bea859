# Complete n8n Installation Guide for Ubuntu 22.04

This guide provides step-by-step instructions for installing n8n directly on Ubuntu 22.04 without using Docker.

## Prerequisites

### System Requirements
- Ubuntu 22.04 LTS (Jammy Jellyfish)
- Node.js 18.10 or newer
- npm package manager
- Sufficient disk space and memory

### Check Your System
```bash
# Check Ubuntu version
cat /etc/os-release

# Update package index
sudo apt update
```

## Step 1: Install Node.js and npm

```bash
# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -

# Install Node.js and npm
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

Expected output should show Node.js v20.x.x and npm 10.x.x or newer.

## Step 2: Install Redis

```bash
# Install Redis server
sudo apt install redis-server

# Enable Redis to start on boot
sudo systemctl enable redis-server

# Start Redis service
sudo systemctl start redis-server

# Verify Redis is running
sudo systemctl status redis-server

# Test Redis connection
redis-cli ping
```

Expected output from `redis-cli ping` should be `PONG`.

## Step 3: Install n8n

```bash
# Install n8n globally
sudo npm install -g n8n

# Verify installation
which n8n
n8n --version
```

## Step 4: Test n8n Installation

```bash
# Start n8n manually to test
n8n start
```

This should start n8n on `http://localhost:5678`. Press Ctrl+C to stop.

## Step 5: Create Systemd Service

```bash
# Create the service file
sudo nano /etc/systemd/system/n8n.service
```

Add the following configuration:

```ini
[Unit]
Description=n8n
After=network.target redis.service
Requires=redis.service

[Service]
Type=simple
User=root
WorkingDirectory=/root
ExecStart=/usr/bin/n8n start
Restart=on-failure
RestartSec=5
Environment=N8N_PORT=5678
Environment=N8N_HOST=serper2.gomdusa.net
Environment=N8N_WEBHOOK_URL=http://serper2.gomdusa.net:5678
Environment=N8N_SECURE_COOKIE=false
Environment=EXECUTIONS_MODE=queue
Environment=QUEUE_BULL_REDIS_HOST=localhost
Environment=QUEUE_BULL_REDIS_PORT=6379
Environment=QUEUE_BULL_REDIS_DB=0
Environment=QUEUE_HEALTH_CHECK_ACTIVE=true
Environment=N8N_WORKERS_AUTO_SCALE=true
Environment=N8N_WORKERS_MAX=8
Environment=EXECUTIONS_DATA_PRUNE=true
Environment=EXECUTIONS_DATA_MAX_AGE=168
Environment=N8N_LOG_LEVEL=debug
Environment=DB_SQLITE_VACUUM_ON_STARTUP=true
Environment=PATH=/usr/bin:/usr/local/bin:/bin
Environment=HOME=/root
Environment=NODE_OPTIONS=--max-old-space-size=102400
Environment=N8N_PAYLOAD_SIZE_MAX=268435456

[Install]
WantedBy=multi-user.target
```

## Step 6: Start and Enable the Service

```bash
# Reload systemd to recognize the new service
sudo systemctl daemon-reload

# Enable the service to start on boot
sudo systemctl enable n8n

# Start the service
sudo systemctl start n8n

# Check service status
sudo systemctl status n8n

# Verify n8n is listening on port 5678
ss -tulpn | grep 5678
```

## Step 7: Access n8n

Open your browser and go to: `http://localhost:5678` or `http://your-server-ip:5678`

## Troubleshooting

If the service fails to start, check the correct n8n path:

```bash
# Find correct n8n path
which n8n

# Update service file with correct path if needed
sudo nano /etc/systemd/system/n8n.service
# Update ExecStart line with the path from 'which n8n'

# Reload and restart
sudo systemctl daemon-reload
sudo systemctl restart n8n
```

## Service Management Commands

```bash
# Check n8n status
sudo systemctl status n8n

# Check Redis status
sudo systemctl status redis-server

# View n8n logs
sudo journalctl -u n8n -f

# Restart services
sudo systemctl restart redis-server
sudo systemctl restart n8n

# Stop services
sudo systemctl stop n8n
sudo systemctl stop redis-server

# Test Redis connection
redis-cli ping
```

## Conclusion

You should now have n8n running as a systemd service on Ubuntu 22.04. The service will automatically start on system boot and restart if it crashes.

Access your n8n instance at `http://localhost:5678` or `http://your-server-ip:5678` and start building your automation workflows!
