# n8n Setup Guide for Business Data Parser

## 🚀 Quick Setup

### 1. **Create Code Node**
- Add a **Code** node to your n8n workflow
- Set mode to **"Run Once for All Items"**

### 2. **Copy the Code**
Copy the entire content from `n8n-clean-content-parser.js` into the Code node

### 3. **Input Data Format**
Your input data should have this structure:
```json
{
  "cid": "9092643068171060391",
  "data": "<html>...</html>"
}
```

**Alternative field names supported:**
- `html` instead of `data`
- `content` instead of `data`
- `id` instead of `cid`

## 📊 **Expected Output**

Each processed item will return:
```json
{
  "cid": "9092643068171060391",
  "business_name": "General Contractor NYC",
  "phone": "+***********",
  "email": "<EMAIL>",
  "website": "https://generalcontractornyc.net/",
  "address": "845 Columbus ave Suite 3f, New York NY 10025",
  "description": "General Contractor NYC is a Manhattan-based...",
  "business_insights": {
    "years_of_experience": "15",
    "projects_completed": "1325",
    "buildings_constructed": "18",
    "satisfied_clients": "1295",
    "awards": "2023 QUALITY BUSINESS AWARDS",
    "area_served": "Manhattan, NYC, New York",
    "price_range": "$"
  },
  "contact_persons": [
    {
      "name": "Alex",
      "title": "Project Manager/Contractor",
      "source": "customer_reviews",
      "mentions": 2
    }
  ],
  "customer_reviews": [
    {
      "text": "Our kitchen looks incredible after the remodel...",
      "source": "google_reviews"
    }
  ],
  "services": [
    "renovation", "remodeling", "construction", "kitchen", "bathroom"
  ],
  "extraction_metadata": {
    "extraction_date": "2025-06-06T00:21:31.184Z",
    "source": "scraped_html_clean",
    "content_length": 352888
  },
  "extraction_success": true,
  "extraction_timestamp": "2025-06-06T00:21:31.184Z"
}
```

## 🔧 **Workflow Integration**

### Typical n8n Workflow:
```
[Trigger] → [ScraperAPI] → [Code Parser] → [Database/CRM]
```

### Example Workflow Steps:
1. **HTTP Request** - Scrape website with ScraperAPI
2. **Code Node** - Parse business data (use our code)
3. **Set Node** - Clean/format data if needed
4. **Database Node** - Save to your CRM/database

## ⚙️ **Configuration Options**

### Customize Service Keywords:
Edit the `serviceKeywords` array in the code:
```javascript
const serviceKeywords = [
  'renovation', 'remodeling', 'construction',
  // Add your industry-specific keywords
  'plumbing', 'electrical', 'roofing'
];
```

### Customize Contact Person Detection:
Edit the `namePatterns` array:
```javascript
const namePatterns = [
  { pattern: /alex\s+(?:did|to|remodel)/gi, name: "Alex", title: "Project Manager" },
  // Add patterns for other common names in your industry
];
```

## 🛠️ **Error Handling**

The code includes built-in error handling:
- Returns `extraction_success: false` on errors
- Includes `error` field with error message
- Continues processing other items even if one fails

## 📝 **Testing**

Test with your scraped data:
1. Use a small sample first
2. Check the output structure
3. Verify extracted data accuracy
4. Adjust patterns if needed

## 🎯 **Performance Notes**

- Processes multiple items efficiently
- Regex patterns optimized for speed
- Memory-efficient for large HTML content
- Typical processing: ~100ms per business

## 🔄 **Next Steps**

After parsing:
1. **Validate** phone/email formats
2. **Enrich** with additional APIs (Hunter.io, etc.)
3. **Score** data quality
4. **Export** to your CRM system

---

**Ready to use!** 🚀 Just copy the code into an n8n Code node and connect your data flow.
