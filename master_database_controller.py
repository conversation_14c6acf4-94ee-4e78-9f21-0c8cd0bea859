#!/usr/bin/env python3
"""
MASTER DATABASE CONTROLLER
==========================
Central access point for all 8 business datasets with 1,553,511 unique domains.

This script provides unified access to:
- B2 (Healthcare & Medical): 393,754 domains
- B3 (Specialized Medical): 43,189 domains
- B2A (Pet Care & Personal): 141,706 domains
- B4 (Accounting & Banking): 92,242 domains
- Hotel & Buildings (Real Estate & Travel): 467,162 domains
- Buildings & Churches (Construction & Religious): 108,244 domains
- Auto Business (Automotive & Business): 201,372 domains
- Equipment (Industrial & Consumer): 105,842 domains

Usage Examples:
    python master_database_controller.py --list-datasets
    python master_database_controller.py --load-dataset B2
    python master_database_controller.py --combine-industry healthcare
    python master_database_controller.py --stats
"""

import pandas as pd
import glob
import os
import argparse
from pathlib import Path

class MasterDatabaseController:
    """
    Master controller for accessing all business datasets.
    """

    def __init__(self):
        """Initialize the controller with dataset configurations."""
        self.datasets = {
            'B2': {
                'name': 'Healthcare & Medical',
                'files': ['B2/merged_data_final_part1.csv', 'B2/merged_data_final_part2.csv', 'B2/merged_data_final_part3.csv'],
                'domains': 393754,
                'description': 'Healthcare providers, medical practices, hospitals'
            },
            'B3': {
                'name': 'Specialized Medical',
                'files': ['B3/b3_final_cross_deduplicated.csv'],
                'domains': 43189,
                'description': 'Specialized medical services and practitioners'
            },
            'B2A': {
                'name': 'Pet Care & Personal Services',
                'files': ['B2A/b2a_final_cross_deduplicated.csv'],
                'domains': 141706,
                'description': 'Pet care, dental services, hair care'
            },
            'B4': {
                'name': 'Accounting & Banking',
                'files': ['B4 Done/b4_final_cross_deduplicated.csv'],
                'domains': 92242,
                'description': 'Accounting firms, banks, financial services'
            },
            'HOTEL_BUILDINGS': {
                'name': 'Real Estate & Travel',
                'files': ['hotel and buildings/hotel_buildings_final_cross_deduplicated_part1.csv',
                         'hotel and buildings/hotel_buildings_final_cross_deduplicated_part2.csv',
                         'hotel and buildings/hotel_buildings_final_cross_deduplicated_part3.csv'],
                'domains': 467162,
                'description': 'Hotels, travel, real estate, construction'
            },
            'BUILDINGS_CHURCHES': {
                'name': 'Construction & Religious',
                'files': ['buildings and church/buildings_church_final_cross_deduplicated.csv'],
                'domains': 108244,
                'description': 'Construction companies, religious organizations'
            },
            'AUTO_BUSINESS': {
                'name': 'Automotive & Business Services',
                'files': ['auto business/auto_business_final_cross_deduplicated_part1.csv',
                         'auto business/auto_business_final_cross_deduplicated_part2.csv'],
                'domains': 201372,
                'description': 'Car dealers, auto services, business brokers'
            },
            'EQUIPMENT': {
                'name': 'Industrial & Consumer Equipment',
                'files': ['Equipment/equipment_final_cross_deduplicated.csv'],
                'domains': 105842,
                'description': 'Industrial machinery, construction equipment, consumer goods'
            }
        }

        self.industry_groups = {
            'healthcare': ['B2', 'B3'],
            'construction': ['HOTEL_BUILDINGS', 'BUILDINGS_CHURCHES'],
            'automotive': ['AUTO_BUSINESS'],
            'equipment': ['EQUIPMENT'],
            'services': ['B2A', 'B4'],
            'all': list(self.datasets.keys())
        }

    def list_datasets(self):
        """List all available datasets with statistics."""
        print("AVAILABLE DATASETS")
        print("="*50)

        total_domains = 0
        total_files = 0

        for key, dataset in self.datasets.items():
            print(f"\n{key}: {dataset['name']}")
            print(f"  Description: {dataset['description']}")
            print(f"  Files: {len(dataset['files'])}")
            print(f"  Domains: {dataset['domains']:,}")
            print(f"  Files: {', '.join([os.path.basename(f) for f in dataset['files']])}")

            total_domains += dataset['domains']
            total_files += len(dataset['files'])

        print(f"\n" + "="*50)
        print(f"TOTAL: {len(self.datasets)} datasets, {total_files} files, {total_domains:,} unique domains")

    def load_dataset(self, dataset_key, sample_size=None):
        """
        Load a specific dataset.

        Args:
            dataset_key (str): Dataset identifier (e.g., 'B2', 'B3')
            sample_size (int, optional): Number of rows to sample

        Returns:
            pandas.DataFrame: Loaded dataset
        """
        if dataset_key not in self.datasets:
            raise ValueError(f"Dataset '{dataset_key}' not found. Available: {list(self.datasets.keys())}")

        dataset = self.datasets[dataset_key]
        print(f"Loading {dataset['name']} ({dataset_key})...")

        # Load all files for the dataset
        dataframes = []
        for file_path in dataset['files']:
            if os.path.exists(file_path):
                print(f"  Loading: {file_path}")
                df = pd.read_csv(file_path, low_memory=False)
                dataframes.append(df)
            else:
                print(f"  ⚠️  File not found: {file_path}")

        if not dataframes:
            raise FileNotFoundError(f"No files found for dataset {dataset_key}")

        # Combine all dataframes
        combined_df = pd.concat(dataframes, ignore_index=True)

        # Apply sampling if requested
        if sample_size and sample_size < len(combined_df):
            combined_df = combined_df.sample(n=sample_size, random_state=42)
            print(f"  Sampled {sample_size:,} rows from {len(combined_df):,} total")

        print(f"  Loaded: {len(combined_df):,} rows, {len(combined_df.columns)} columns")
        return combined_df

    def combine_industry(self, industry_group):
        """
        Combine datasets by industry group.

        Args:
            industry_group (str): Industry group ('healthcare', 'construction', etc.)

        Returns:
            pandas.DataFrame: Combined industry dataset
        """
        if industry_group not in self.industry_groups:
            raise ValueError(f"Industry group '{industry_group}' not found. Available: {list(self.industry_groups.keys())}")

        dataset_keys = self.industry_groups[industry_group]
        print(f"Combining {industry_group} industry datasets: {dataset_keys}")

        combined_dataframes = []
        total_domains = 0

        for dataset_key in dataset_keys:
            df = self.load_dataset(dataset_key)
            combined_dataframes.append(df)
            total_domains += len(df)

        if combined_dataframes:
            result = pd.concat(combined_dataframes, ignore_index=True)
            print(f"Combined {industry_group} industry: {len(result):,} total records")
            return result
        else:
            raise ValueError(f"No datasets loaded for industry group: {industry_group}")

    def get_statistics(self):
        """Generate comprehensive statistics for all datasets."""
        print("DATABASE STATISTICS")
        print("="*50)

        total_domains = sum(dataset['domains'] for dataset in self.datasets.values())
        total_files = sum(len(dataset['files']) for dataset in self.datasets.values())

        print(f"Total Datasets: {len(self.datasets)}")
        print(f"Total Files: {total_files}")
        print(f"Total Unique Domains: {total_domains:,}")
        print(f"Average Domains per Dataset: {total_domains // len(self.datasets):,}")

        print(f"\nDataset Distribution:")
        for key, dataset in sorted(self.datasets.items(), key=lambda x: x[1]['domains'], reverse=True):
            percentage = (dataset['domains'] / total_domains) * 100
            print(f"  {key}: {dataset['domains']:,} ({percentage:.1f}%)")

        print(f"\nIndustry Groups:")
        for group, datasets in self.industry_groups.items():
            if group != 'all':
                group_domains = sum(self.datasets[ds]['domains'] for ds in datasets)
                print(f"  {group.title()}: {group_domains:,} domains ({len(datasets)} datasets)")

    def search_businesses(self, dataset_key, search_term, column='title'):
        """
        Search for businesses in a specific dataset.

        Args:
            dataset_key (str): Dataset to search
            search_term (str): Term to search for
            column (str): Column to search in

        Returns:
            pandas.DataFrame: Matching businesses
        """
        df = self.load_dataset(dataset_key)

        if column not in df.columns:
            raise ValueError(f"Column '{column}' not found. Available: {list(df.columns)}")

        mask = df[column].str.contains(search_term, case=False, na=False)
        results = df[mask]

        print(f"Found {len(results):,} businesses matching '{search_term}' in {column}")
        return results

    def export_dataset(self, dataset_key, output_file, format='csv'):
        """
        Export a dataset to file.

        Args:
            dataset_key (str): Dataset to export
            output_file (str): Output file path
            format (str): Export format ('csv', 'excel')
        """
        df = self.load_dataset(dataset_key)

        if format.lower() == 'csv':
            df.to_csv(output_file, index=False, encoding='utf-8')
        elif format.lower() == 'excel':
            df.to_excel(output_file, index=False)
        else:
            raise ValueError(f"Unsupported format: {format}")

        file_size = os.path.getsize(output_file) / (1024*1024)
        print(f"Exported {len(df):,} records to {output_file} ({file_size:.2f} MB)")

    def verify_data_integrity(self):
        """Verify data integrity across all datasets."""
        print("DATA INTEGRITY VERIFICATION")
        print("="*40)

        all_domains = set()
        total_checked = 0

        for key, dataset in self.datasets.items():
            print(f"\nChecking {key}...")
            df = self.load_dataset(key)

            # Check for domains
            if 'domain' not in df.columns:
                print(f"  ❌ Missing 'domain' column")
                continue

            dataset_domains = set(df['domain'].dropna())

            # Check for overlaps with previous datasets
            overlaps = all_domains.intersection(dataset_domains)
            if overlaps:
                print(f"  ❌ Found {len(overlaps)} overlapping domains")
            else:
                print(f"  ✅ No overlaps found")

            # Check domain quality
            subdomain_count = sum(1 for domain in dataset_domains if domain.count('.') > 1)
            print(f"  ✅ Domain quality: {len(dataset_domains) - subdomain_count}/{len(dataset_domains)} clean")

            all_domains.update(dataset_domains)
            total_checked += len(dataset_domains)

        print(f"\n" + "="*40)
        print(f"✅ Verification complete: {total_checked:,} unique domains across all datasets")
        print(f"✅ Perfect separation maintained")

def main():
    """Main function for command-line interface."""
    parser = argparse.ArgumentParser(description='Master Database Controller')
    parser.add_argument('--list-datasets', action='store_true', help='List all available datasets')
    parser.add_argument('--load-dataset', type=str, help='Load a specific dataset')
    parser.add_argument('--combine-industry', type=str, help='Combine datasets by industry')
    parser.add_argument('--stats', action='store_true', help='Show database statistics')
    parser.add_argument('--verify', action='store_true', help='Verify data integrity')
    parser.add_argument('--sample-size', type=int, help='Sample size for loading datasets')

    args = parser.parse_args()

    controller = MasterDatabaseController()

    if args.list_datasets:
        controller.list_datasets()
    elif args.load_dataset:
        df = controller.load_dataset(args.load_dataset, args.sample_size)
        print(f"\nSample data:")
        print(df[['title', 'domain']].head())
    elif args.combine_industry:
        df = controller.combine_industry(args.combine_industry)
        print(f"\nSample data:")
        print(df[['title', 'domain']].head())
    elif args.stats:
        controller.get_statistics()
    elif args.verify:
        controller.verify_data_integrity()
    else:
        controller.list_datasets()
        print(f"\nUse --help for available commands")

if __name__ == "__main__":
    main()

# Example usage in scripts:
"""
from master_database_controller import MasterDatabaseController

# Initialize controller
db = MasterDatabaseController()

# Load healthcare businesses
healthcare_df = db.combine_industry('healthcare')

# Load specific dataset
auto_df = db.load_dataset('AUTO_BUSINESS')

# Search for specific businesses
dental_practices = db.search_businesses('B2A', 'dental')

# Export dataset
db.export_dataset('B3', 'medical_specialists.csv')

# Get statistics
db.get_statistics()

# Verify data integrity
db.verify_data_integrity()
"""
