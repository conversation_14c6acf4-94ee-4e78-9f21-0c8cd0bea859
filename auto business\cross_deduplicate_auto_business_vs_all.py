#!/usr/bin/env python3
"""
Cross-deduplicate Auto Business dataset against all other datasets 
by removing domains that already exist in any of the other datasets.
"""

import pandas as pd
import os
from pathlib import Path

def cross_deduplicate_auto_business_vs_all():
    """
    Remove domains from Auto Business that already exist in other datasets.
    """
    print("CROSS-DEDUPLICATION: AUTO BUSINESS vs ALL OTHER DATASETS")
    print("="*70)
    
    # File paths
    b2_file = "../B2/merged_data_final.csv"
    b3_file = "../B3/b3_final_cross_deduplicated.csv"
    b2a_file = "../B2A/b2a_final_cross_deduplicated.csv"
    b4_file = "../B4 Done/b4_final_cross_deduplicated.csv"
    hotel_buildings_file = "../hotel and buildings/hotel_buildings_final_cross_deduplicated.csv"
    buildings_church_file = "../buildings and church/buildings_church_final_cross_deduplicated.csv"
    auto_business_file = "auto_business_merged_deduplicated.csv"
    output_file = "auto_business_final_cross_deduplicated.csv"
    
    # Check if files exist
    files_to_check = [
        ("B2", b2_file),
        ("B3", b3_file),
        ("B2A", b2a_file),
        ("B4", b4_file),
        ("Hotel & Buildings", hotel_buildings_file),
        ("Buildings & Churches", buildings_church_file),
        ("Auto Business", auto_business_file)
    ]
    
    for name, file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ ERROR: {name} file not found: {file_path}")
            return
    
    # Load all datasets
    datasets = {}
    all_existing_domains = set()
    
    print("📁 Loading datasets...")
    
    # Load B2
    try:
        print("   Loading B2 (this may take a moment for large files)...")
        datasets['B2'] = pd.read_csv(b2_file, low_memory=False)
        b2_domains = set(datasets['B2']['domain'].dropna().str.strip())
        all_existing_domains.update(b2_domains)
        print(f"   ✅ B2 loaded: {len(datasets['B2']):,} rows, {len(b2_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B2 file: {e}")
        return
    
    # Load B3
    try:
        datasets['B3'] = pd.read_csv(b3_file, low_memory=False)
        b3_domains = set(datasets['B3']['domain'].dropna().str.strip())
        all_existing_domains.update(b3_domains)
        print(f"   ✅ B3 loaded: {len(datasets['B3']):,} rows, {len(b3_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B3 file: {e}")
        return
    
    # Load B2A
    try:
        datasets['B2A'] = pd.read_csv(b2a_file, low_memory=False)
        b2a_domains = set(datasets['B2A']['domain'].dropna().str.strip())
        all_existing_domains.update(b2a_domains)
        print(f"   ✅ B2A loaded: {len(datasets['B2A']):,} rows, {len(b2a_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B2A file: {e}")
        return
    
    # Load B4
    try:
        datasets['B4'] = pd.read_csv(b4_file, low_memory=False)
        b4_domains = set(datasets['B4']['domain'].dropna().str.strip())
        all_existing_domains.update(b4_domains)
        print(f"   ✅ B4 loaded: {len(datasets['B4']):,} rows, {len(b4_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B4 file: {e}")
        return
    
    # Load Hotel & Buildings
    try:
        print("   Loading Hotel & Buildings (this may take a moment for large files)...")
        datasets['Hotel_Buildings'] = pd.read_csv(hotel_buildings_file, low_memory=False)
        hb_domains = set(datasets['Hotel_Buildings']['domain'].dropna().str.strip())
        all_existing_domains.update(hb_domains)
        print(f"   ✅ Hotel & Buildings loaded: {len(datasets['Hotel_Buildings']):,} rows, {len(hb_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading Hotel & Buildings file: {e}")
        return
    
    # Load Buildings & Churches
    try:
        datasets['Buildings_Churches'] = pd.read_csv(buildings_church_file, low_memory=False)
        bc_domains = set(datasets['Buildings_Churches']['domain'].dropna().str.strip())
        all_existing_domains.update(bc_domains)
        print(f"   ✅ Buildings & Churches loaded: {len(datasets['Buildings_Churches']):,} rows, {len(bc_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading Buildings & Churches file: {e}")
        return
    
    # Load Auto Business
    try:
        print("   Loading Auto Business...")
        datasets['Auto_Business'] = pd.read_csv(auto_business_file, low_memory=False)
        ab_domains = set(datasets['Auto_Business']['domain'].dropna().str.strip())
        print(f"   ✅ Auto Business loaded: {len(datasets['Auto_Business']):,} rows, {len(ab_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading Auto Business file: {e}")
        return
    
    # Analyze overlaps
    print(f"\n🔍 ANALYZING DOMAIN OVERLAPS")
    print("-" * 60)
    
    overlap_ab_vs_b2 = ab_domains.intersection(b2_domains)
    overlap_ab_vs_b3 = ab_domains.intersection(b3_domains)
    overlap_ab_vs_b2a = ab_domains.intersection(b2a_domains)
    overlap_ab_vs_b4 = ab_domains.intersection(b4_domains)
    overlap_ab_vs_hb = ab_domains.intersection(hb_domains)
    overlap_ab_vs_bc = ab_domains.intersection(bc_domains)
    total_overlaps = ab_domains.intersection(all_existing_domains)
    
    print(f"Auto Business vs B2 overlaps: {len(overlap_ab_vs_b2):,}")
    print(f"Auto Business vs B3 overlaps: {len(overlap_ab_vs_b3):,}")
    print(f"Auto Business vs B2A overlaps: {len(overlap_ab_vs_b2a):,}")
    print(f"Auto Business vs B4 overlaps: {len(overlap_ab_vs_b4):,}")
    print(f"Auto Business vs Hotel & Buildings overlaps: {len(overlap_ab_vs_hb):,}")
    print(f"Auto Business vs Buildings & Churches overlaps: {len(overlap_ab_vs_bc):,}")
    print(f"Total Auto Business overlaps: {len(total_overlaps):,}")
    
    if len(total_overlaps) == 0:
        print("✅ No overlapping domains found! Auto Business dataset is already unique vs all others.")
        # Still save a copy for consistency
        datasets['Auto_Business'].to_csv(output_file, index=False, encoding='utf-8')
        print(f"💾 Saved copy as: {output_file}")
        return datasets['Auto_Business']
    
    # Show some examples of overlapping domains
    print(f"\nExamples of overlapping domains:")
    for i, domain in enumerate(list(total_overlaps)[:15]):
        # Check which dataset(s) it overlaps with
        overlap_sources = []
        if domain in b2_domains:
            overlap_sources.append("B2")
        if domain in b3_domains:
            overlap_sources.append("B3")
        if domain in b2a_domains:
            overlap_sources.append("B2A")
        if domain in b4_domains:
            overlap_sources.append("B4")
        if domain in hb_domains:
            overlap_sources.append("H&B")
        if domain in bc_domains:
            overlap_sources.append("B&C")
        print(f"  {i+1}. {domain} (overlaps with: {', '.join(overlap_sources)})")
    
    if len(total_overlaps) > 15:
        print(f"  ... and {len(total_overlaps) - 15} more")
    
    # Remove overlapping domains from Auto Business
    print(f"\n🗑️  REMOVING OVERLAPPING DOMAINS FROM AUTO BUSINESS")
    print("-" * 70)
    
    original_ab_count = len(datasets['Auto_Business'])
    
    # Create mask for rows to keep (domains NOT in any other dataset)
    mask_keep = ~datasets['Auto_Business']['domain'].isin(all_existing_domains)
    ab_filtered = datasets['Auto_Business'][mask_keep].copy()
    
    removed_count = original_ab_count - len(ab_filtered)
    
    print(f"Original Auto Business rows: {original_ab_count:,}")
    print(f"Rows removed: {removed_count:,}")
    print(f"Remaining rows: {len(ab_filtered):,}")
    print(f"Removal rate: {(removed_count / original_ab_count) * 100:.2f}%")
    print(f"Retention rate: {(len(ab_filtered) / original_ab_count) * 100:.2f}%")
    
    # Verify no overlaps remain
    remaining_ab_domains = set(ab_filtered['domain'].dropna().str.strip())
    final_overlap_b2 = b2_domains.intersection(remaining_ab_domains)
    final_overlap_b3 = b3_domains.intersection(remaining_ab_domains)
    final_overlap_b2a = b2a_domains.intersection(remaining_ab_domains)
    final_overlap_b4 = b4_domains.intersection(remaining_ab_domains)
    final_overlap_hb = hb_domains.intersection(remaining_ab_domains)
    final_overlap_bc = bc_domains.intersection(remaining_ab_domains)
    
    print(f"\n✅ VERIFICATION")
    print("-" * 15)
    print(f"Remaining Auto Business unique domains: {len(remaining_ab_domains):,}")
    print(f"Final overlap with B2: {len(final_overlap_b2)}")
    print(f"Final overlap with B3: {len(final_overlap_b3)}")
    print(f"Final overlap with B2A: {len(final_overlap_b2a)}")
    print(f"Final overlap with B4: {len(final_overlap_b4)}")
    print(f"Final overlap with Hotel & Buildings: {len(final_overlap_hb)}")
    print(f"Final overlap with Buildings & Churches: {len(final_overlap_bc)}")
    
    all_overlaps_zero = (len(final_overlap_b2) == 0 and 
                        len(final_overlap_b3) == 0 and 
                        len(final_overlap_b2a) == 0 and
                        len(final_overlap_b4) == 0 and
                        len(final_overlap_hb) == 0 and
                        len(final_overlap_bc) == 0)
    print(f"Cross-deduplication successful: {all_overlaps_zero}")
    
    # Save the cross-deduplicated dataset
    print(f"\n💾 SAVING CROSS-DEDUPLICATED DATASET")
    print("-" * 65)
    
    ab_filtered.to_csv(output_file, index=False, encoding='utf-8')
    
    file_size = os.path.getsize(output_file) / (1024*1024)
    print(f"Saved: {output_file}")
    print(f"File size: {file_size:.2f} MB")
    
    # Generate summary report
    generate_cross_dedup_report(datasets, ab_filtered, total_overlaps, 
                               overlap_ab_vs_b2, overlap_ab_vs_b3, 
                               overlap_ab_vs_b2a, overlap_ab_vs_b4, 
                               overlap_ab_vs_hb, overlap_ab_vs_bc)
    
    print(f"\n🎉 CROSS-DEDUPLICATION COMPLETED!")
    print(f"📁 Final Auto Business dataset: {output_file}")
    print(f"📊 Report: auto_business_cross_dedup_report.txt")
    
    # Show final combined potential
    print(f"\n🌟 COMBINED DATASET POTENTIAL:")
    print("-" * 55)
    total_unique_domains = (len(b2_domains) + len(b3_domains) + 
                           len(b2a_domains) + len(b4_domains) + 
                           len(hb_domains) + len(bc_domains) + 
                           len(remaining_ab_domains))
    print(f"B2 domains: {len(b2_domains):,}")
    print(f"B3 domains: {len(b3_domains):,}")
    print(f"B2A domains: {len(b2a_domains):,}")
    print(f"B4 domains: {len(b4_domains):,}")
    print(f"Hotel & Buildings domains: {len(hb_domains):,}")
    print(f"Buildings & Churches domains: {len(bc_domains):,}")
    print(f"Auto Business domains: {len(remaining_ab_domains):,}")
    print(f"Total unique domains: {total_unique_domains:,}")
    
    return ab_filtered

def generate_cross_dedup_report(datasets, ab_final, total_overlaps, 
                               overlap_b2, overlap_b3, overlap_b2a, overlap_b4, 
                               overlap_hb, overlap_bc):
    """
    Generate a comprehensive cross-deduplication report.
    """
    report_content = f"""AUTO BUSINESS vs ALL DATASETS CROSS-DEDUPLICATION REPORT
========================================================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
Process: Remove Auto Business domains that already exist in other datasets

DATASET SUMMARY:
===============
B2 Dataset: {len(datasets['B2']):,} rows, {datasets['B2']['domain'].nunique():,} unique domains
B3 Dataset: {len(datasets['B3']):,} rows, {datasets['B3']['domain'].nunique():,} unique domains
B2A Dataset: {len(datasets['B2A']):,} rows, {datasets['B2A']['domain'].nunique():,} unique domains
B4 Dataset: {len(datasets['B4']):,} rows, {datasets['B4']['domain'].nunique():,} unique domains
Hotel & Buildings Dataset: {len(datasets['Hotel_Buildings']):,} rows, {datasets['Hotel_Buildings']['domain'].nunique():,} unique domains
Buildings & Churches Dataset: {len(datasets['Buildings_Churches']):,} rows, {datasets['Buildings_Churches']['domain'].nunique():,} unique domains

Auto Business Dataset (Original): {len(datasets['Auto_Business']):,} rows, {datasets['Auto_Business']['domain'].nunique():,} unique domains

CROSS-DEDUPLICATION RESULTS:
===========================
- Auto Business vs B2 overlaps: {len(overlap_b2):,}
- Auto Business vs B3 overlaps: {len(overlap_b3):,}
- Auto Business vs B2A overlaps: {len(overlap_b2a):,}
- Auto Business vs B4 overlaps: {len(overlap_b4):,}
- Auto Business vs Hotel & Buildings overlaps: {len(overlap_hb):,}
- Auto Business vs Buildings & Churches overlaps: {len(overlap_bc):,}
- Total overlapping domains: {len(total_overlaps):,}
- Auto Business rows removed: {len(datasets['Auto_Business']) - len(ab_final):,}
- Auto Business rows retained: {len(ab_final):,}
- Retention rate: {(len(ab_final) / len(datasets['Auto_Business'])) * 100:.2f}%

FINAL COMBINED DATASET POTENTIAL:
================================
- B2 unique domains: {datasets['B2']['domain'].nunique():,}
- B3 unique domains: {datasets['B3']['domain'].nunique():,}
- B2A unique domains: {datasets['B2A']['domain'].nunique():,}
- B4 unique domains: {datasets['B4']['domain'].nunique():,}
- Hotel & Buildings unique domains: {datasets['Hotel_Buildings']['domain'].nunique():,}
- Buildings & Churches unique domains: {datasets['Buildings_Churches']['domain'].nunique():,}
- Auto Business unique domains (after cross-dedup): {ab_final['domain'].nunique():,}
- Total unique domains across all seven: {datasets['B2']['domain'].nunique() + datasets['B3']['domain'].nunique() + datasets['B2A']['domain'].nunique() + datasets['B4']['domain'].nunique() + datasets['Hotel_Buildings']['domain'].nunique() + datasets['Buildings_Churches']['domain'].nunique() + ab_final['domain'].nunique():,}
- Perfect separation achieved: True

FINAL AUTO BUSINESS CATEGORY BREAKDOWN:
======================================
"""
    
    # Add category breakdown for final Auto Business
    category_stats = ab_final['source_category'].value_counts()
    for category, count in category_stats.items():
        percentage = (count / len(ab_final)) * 100
        report_content += f"- {category}: {count:,} rows ({percentage:.1f}%)\n"
    
    # Add top source files
    report_content += f"\nTOP SOURCE FILES IN FINAL AUTO BUSINESS:\n"
    report_content += f"=======================================\n"
    source_stats = ab_final['source_file'].value_counts().head(15)
    for source, count in source_stats.items():
        report_content += f"- {source}: {count:,} rows\n"
    
    # Add top domains in final Auto Business
    report_content += f"\nTOP DOMAINS IN FINAL AUTO BUSINESS:\n"
    report_content += f"==================================\n"
    domain_counts = ab_final['domain'].value_counts().head(20)
    for i, (domain, count) in enumerate(domain_counts.items(), 1):
        report_content += f"{i}. {domain}: {count} businesses\n"
    
    # Save report
    with open('auto_business_cross_dedup_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📊 Cross-deduplication report saved: auto_business_cross_dedup_report.txt")

def main():
    """
    Main function to execute cross-deduplication.
    """
    try:
        result_df = cross_deduplicate_auto_business_vs_all()
        if result_df is not None:
            print(f"\n✅ SUCCESS: Cross-deduplication completed successfully!")
        else:
            print(f"\n❌ FAILED: Cross-deduplication encountered errors.")
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")

if __name__ == "__main__":
    main()
