import pandas as pd
import os
import logging
import math
from datetime import datetime
from urllib.parse import urlparse
import re

# Import corrected functions
from process_corrected_template import (
    setup_logging, extract_domain, create_info_email, process_domains_and_emails,
    load_master_database, deduplicate_against_master, deduplicate_by_domain,
    filter_records_with_domains_only, estimate_rows_per_50mb, split_dataframe,
    generate_processing_report, get_file_size_mb
)

def load_excel_files(folder_path, logger):
    """Load and combine all Excel files from B6 folder and subfolders."""
    logger.info(f"Loading Excel files from: {folder_path}")
    
    all_dataframes = []
    file_stats = {}
    
    # Get all Excel files recursively
    excel_files = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.xlsx') and not file.startswith('~') and not file.startswith('Copy of'):
                full_path = os.path.join(root, file)
                subfolder = os.path.basename(root) if root != folder_path else ""
                excel_files.append((full_path, file, subfolder))
    
    logger.info(f"Found {len(excel_files)} Excel files to process")
    
    for i, (file_path, filename, subfolder) in enumerate(excel_files, 1):
        try:
            logger.info(f"Processing: {i}. {filename} (from {subfolder if subfolder else 'root'})")
            
            # Read Excel file
            df = pd.read_excel(file_path, engine='openpyxl')
            
            if len(df) > 0:
                # Add metadata columns
                df['source_file'] = filename
                df['source_category'] = filename.replace('.xlsx', '')
                df['subfolder'] = subfolder if subfolder else 'B6'
                df['business_type'] = categorize_b6_business(filename, subfolder)
                df['created_date'] = datetime.now().strftime('%Y-%m-%d')
                
                all_dataframes.append(df)
                file_stats[filename] = len(df)
                
                logger.info(f"  - Loaded {len(df)} records from {filename}")
            else:
                logger.warning(f"  - Empty file: {filename}")
                file_stats[filename] = 0
                
        except Exception as e:
            logger.error(f"  - Error loading {filename}: {e}")
            file_stats[filename] = 0
    
    if all_dataframes:
        combined_df = pd.concat(all_dataframes, ignore_index=True)
        logger.info(f"Combined dataset shape: {combined_df.shape}")
        return combined_df, file_stats
    else:
        logger.error("No data loaded from any files")
        return pd.DataFrame(), file_stats

def categorize_b6_business(filename, subfolder):
    """Categorize B6 business type based on filename and subfolder."""
    filename_lower = filename.lower()
    
    # Remove common prefixes and suffixes
    filename_clean = filename_lower.replace('.xlsx', '').strip()
    
    # Determine main category based on subfolder
    if subfolder == "Health & Medical Services":
        # Health & Medical Services categorization
        if any(word in filename_lower for word in ['hospital', 'clinic', 'medical center', 'health center']):
            return 'Medical Facilities'
        elif any(word in filename_lower for word in ['dental', 'dentist', 'orthodontist']):
            return 'Dental Services'
        elif any(word in filename_lower for word in ['care', 'day care', 'child care', 'elder care']):
            return 'Care Services'
        elif any(word in filename_lower for word in ['pharmacy', 'drug store']):
            return 'Pharmacy Services'
        elif any(word in filename_lower for word in ['therapy', 'rehabilitation', 'physical therapy']):
            return 'Therapy Services'
        elif any(word in filename_lower for word in ['veterinary', 'vet', 'animal hospital', 'pet']):
            return 'Veterinary Services'
        else:
            return 'Health & Medical Services'
    
    elif subfolder == "Retail & Consumer Services":
        # Retail & Consumer Services categorization
        if any(word in filename_lower for word in ['store', 'shop', 'market', 'retail']):
            return 'General Retail'
        elif any(word in filename_lower for word in ['restaurant', 'cafe', 'food', 'dining']):
            return 'Food & Dining'
        elif any(word in filename_lower for word in ['beauty', 'salon', 'spa', 'barber']):
            return 'Beauty & Personal Care'
        elif any(word in filename_lower for word in ['clothing', 'fashion', 'apparel']):
            return 'Clothing & Fashion'
        elif any(word in filename_lower for word in ['auto', 'car', 'automotive']):
            return 'Automotive Services'
        elif any(word in filename_lower for word in ['home', 'furniture', 'appliance']):
            return 'Home & Garden'
        elif any(word in filename_lower for word in ['electronics', 'computer', 'phone']):
            return 'Electronics & Technology'
        elif any(word in filename_lower for word in ['pet', 'animal']):
            return 'Pet & Animal Supplies'
        else:
            return 'Consumer Services'
    
    else:
        return 'Other B6 Services'

def main():
    """Main function to process B6 dataset with corrected logic."""
    # Set up logging
    logger = setup_logging('b6_corrected_processing.log')
    logger.info("Starting CORRECTED B6 processing...")
    
    # Configuration
    folder_path = "B6"
    master_db_path = "master_business_database_2025-06-26_v2.csv"
    base_output_name = "b6_corrected_deduped_by_domain"
    
    try:
        # Step 1: Load and merge all Excel files
        logger.info("Step 1: Loading and merging Excel files...")
        combined_df, file_stats = load_excel_files(folder_path, logger)
        
        if combined_df.empty:
            logger.error("No data loaded. Exiting.")
            return
        
        # Step 2: Process domains and emails
        logger.info("Step 2: Processing domains and emails...")
        processed_df = process_domains_and_emails(combined_df, logger)
        
        # Step 3: Load master database
        logger.info("Step 3: Loading master database...")
        master_df = load_master_database(master_db_path, logger)
        
        # Step 4: Deduplicate against master
        logger.info("Step 4: Deduplicating against master database...")
        deduplicated_df, master_duplicate_count, unique_after_master = deduplicate_against_master(processed_df, master_df, logger)
        
        # Step 5: Deduplicate by domain within dataset
        logger.info("Step 5: Deduplicating by domain within dataset...")
        deduped_df = deduplicate_by_domain(deduplicated_df, logger)
        
        # Step 6: CORRECTED - Filter to keep only records with domains
        logger.info("Step 6: Filtering to keep only records with valid domains...")
        final_df = filter_records_with_domains_only(deduped_df, logger)
        
        if final_df.empty:
            logger.error("No records with domains found. Exiting.")
            return
        
        # Step 7: Estimate optimal split size and split
        logger.info("Step 7: Estimating optimal split size...")
        rows_per_file, rows_per_mb, sample_size_mb = estimate_rows_per_50mb(final_df, temp_prefix="temp_b6_sample")
        
        logger.info(f"  - Sample size: 1000 rows = {sample_size_mb:.2f} MB")
        logger.info(f"  - Estimated rows per MB: {rows_per_mb:.0f}")
        logger.info(f"  - Target rows per file (45MB): {rows_per_file:,}")
        
        logger.info("Step 8: Splitting data into files...")
        split_files = split_dataframe(final_df, rows_per_file, base_output_name, logger)
        
        # Collect processing statistics
        processing_stats = {
            'total_files_processed': len(file_stats),
            'total_records_loaded': sum(file_stats.values()),
            'records_after_domain_processing': len(processed_df),
            'records_with_websites': processed_df['website'].notna().sum() if 'website' in processed_df.columns else 0,
            'records_with_domains': processed_df['domain'].notna().sum(),
            'records_with_info_emails': processed_df['info@ email'].notna().sum(),
            'master_duplicates_removed': master_duplicate_count,
            'records_after_master_dedup': unique_after_master,
            'internal_duplicates_removed': len(deduplicated_df) - len(deduped_df),
            'records_without_domains_removed': len(deduped_df) - len(final_df),
            'final_unique_records_with_domains': len(final_df),
            'total_deduplication_rate_percent': round((sum(file_stats.values()) - len(final_df))/sum(file_stats.values())*100, 1),
            'domain_coverage_percent': 100.0,  # Now 100% since we filtered
            'files_created': len(split_files)
        }
        
        # Step 9: Generate processing report
        logger.info("Step 9: Generating processing report...")
        report_path = generate_processing_report(file_stats, processing_stats, split_files, ".", 'b6_corrected_processing_report.txt')
        logger.info(f"Processing report saved to: {report_path}")
        
        logger.info("CORRECTED processing completed successfully!")
        logger.info(f"Created {len(split_files)} files, all under 50MB with 100% domain coverage")
        
        # Summary
        total_size = sum(f['size_mb'] for f in split_files)
        logger.info(f"\nSUMMARY:")
        logger.info(f"- Original records: {sum(file_stats.values()):,}")
        logger.info(f"- Final unique records with domains: {len(final_df):,}")
        logger.info(f"- Total deduplication rate: {processing_stats['total_deduplication_rate_percent']}%")
        logger.info(f"- Domain coverage: 100.0% (corrected)")
        logger.info(f"- Files created: {len(split_files)}")
        logger.info(f"- Total size: {total_size:.1f} MB")
        logger.info(f"- Average file size: {total_size/len(split_files):.1f} MB")
        
        # Verify all files have 100% domain coverage
        all_100_percent = all(f['domain_coverage'] == 100.0 for f in split_files)
        logger.info(f"- All files have 100% domain coverage: {all_100_percent}")
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        raise

if __name__ == "__main__":
    main()
