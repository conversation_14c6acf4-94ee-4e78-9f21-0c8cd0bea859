#!/usr/bin/env python3
"""
Remove rows that don't have a website from b6_deduplicated_against_master.csv
"""

import pandas as pd
import logging
import os
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('remove_no_website.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_file_size_mb(filename):
    """Get file size in MB."""
    return os.path.getsize(filename) / (1024 * 1024)

def load_and_filter_file(input_filename, chunk_size=50000):
    """Load file in chunks and filter out rows without websites."""
    logger.info(f"Loading and filtering file: {input_filename}")

    try:
        # Get total rows for progress tracking
        total_rows = sum(1 for line in open(input_filename, 'r', encoding='utf-8')) - 1  # -1 for header
        logger.info(f"Total rows in file: {total_rows:,}")

        valid_records = []
        removed_count = 0
        processed_count = 0

        # Process file in chunks
        for chunk_num, chunk_df in enumerate(pd.read_csv(input_filename, chunksize=chunk_size, low_memory=False), 1):
            logger.info(f"Processing chunk {chunk_num}: {len(chunk_df):,} rows")

            # Check if website column exists
            if 'website' not in chunk_df.columns:
                logger.error("No 'website' column found in file")
                return None, 0, 0

            # Count records before filtering
            before_count = len(chunk_df)

            # Filter out rows without websites (empty, null, or whitespace-only)
            # First convert to string to handle mixed types
            chunk_df['website'] = chunk_df['website'].astype(str)

            chunk_df_filtered = chunk_df[
                (chunk_df['website'] != 'nan') &
                (chunk_df['website'].str.strip() != '') &
                (chunk_df['website'] != 'None') &
                (chunk_df['website'].notna())
            ]

            after_count = len(chunk_df_filtered)
            chunk_removed = before_count - after_count
            removed_count += chunk_removed

            # Add to valid records
            if not chunk_df_filtered.empty:
                valid_records.append(chunk_df_filtered)

            processed_count += before_count
            logger.info(f"  - Chunk {chunk_num}: {chunk_removed:,} rows without website removed, {after_count:,} valid rows kept")
            logger.info(f"  - Progress: {processed_count:,}/{total_rows:,} ({processed_count/total_rows*100:.1f}%)")

        # Combine all valid records
        if valid_records:
            final_df = pd.concat(valid_records, ignore_index=True)
            logger.info(f"Final records with websites: {len(final_df):,}")
        else:
            final_df = pd.DataFrame()
            logger.info("No records with websites found")

        return final_df, removed_count, processed_count

    except Exception as e:
        logger.error(f"Error processing file: {e}")
        raise

def save_filtered_file(df, output_filename):
    """Save the filtered dataframe to CSV."""
    logger.info(f"Saving filtered file: {output_filename}")

    try:
        df.to_csv(output_filename, index=False)
        file_size_mb = get_file_size_mb(output_filename)

        logger.info(f"Filtered file saved: {output_filename}")
        logger.info(f"File size: {file_size_mb:.1f} MB")
        logger.info(f"Total records: {len(df):,}")

        return file_size_mb

    except Exception as e:
        logger.error(f"Error saving filtered file: {e}")
        raise

def generate_filtering_report(original_count, removed_count, final_count, output_filename, file_size_mb):
    """Generate a detailed filtering report."""
    report_path = 'remove_no_website_report.txt'

    logger.info(f"Generating filtering report: {report_path}")

    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("B6 Website Filtering Report\n")
            f.write("=" * 27 + "\n")
            f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("Filtering Statistics:\n")
            f.write("-" * 21 + "\n")
            f.write(f"Initial records: {original_count:,}\n")
            f.write(f"Records without website: {removed_count:,}\n")
            f.write(f"Final records with website: {final_count:,}\n")
            f.write(f"Removal rate: {removed_count/original_count*100:.1f}%\n")
            f.write(f"Data retention rate: {final_count/original_count*100:.1f}%\n\n")

            f.write("Output File Information:\n")
            f.write("-" * 25 + "\n")
            f.write(f"Output file: {output_filename}\n")
            f.write(f"File size: {file_size_mb:.1f} MB\n")
            f.write(f"Total records: {final_count:,}\n\n")

            f.write("Processing Details:\n")
            f.write("-" * 19 + "\n")
            f.write(f"Input file: b6_deduplicated_against_master.csv\n")
            f.write(f"Filtering method: Remove rows with empty, null, or whitespace-only websites\n")
            f.write(f"Processing method: Chunked processing for memory efficiency\n")
            f.write(f"Website column criteria: Not null, not empty, not whitespace-only\n")

        logger.info(f"Filtering report saved to: {report_path}")

    except Exception as e:
        logger.error(f"Error generating report: {e}")
        raise

def main():
    """Main function to remove rows without websites."""
    logger.info("Starting website filtering process...")

    # Configuration
    input_file = "b6_deduplicated_against_master.csv"
    output_file = "b6_deduplicated_with_websites.csv"

    try:
        # Check if input file exists
        if not os.path.exists(input_file):
            logger.error(f"Input file not found: {input_file}")
            return

        # Step 1: Load and filter file
        logger.info("Step 1: Loading and filtering records...")
        filtered_df, removed_count, original_count = load_and_filter_file(input_file)

        if filtered_df is None:
            logger.error("Failed to process file. Exiting.")
            return

        # Step 2: Save filtered file
        logger.info("Step 2: Saving filtered file...")
        file_size_mb = save_filtered_file(filtered_df, output_file)

        # Step 3: Generate report
        logger.info("Step 3: Generating filtering report...")
        generate_filtering_report(original_count, removed_count, len(filtered_df), output_file, file_size_mb)

        # Final summary
        logger.info("Website filtering completed successfully!")
        logger.info(f"Original records: {original_count:,}")
        logger.info(f"Records without website removed: {removed_count:,}")
        logger.info(f"Final records with website: {len(filtered_df):,}")
        logger.info(f"Output file: {output_file}")
        logger.info(f"File size: {file_size_mb:.1f} MB")

    except Exception as e:
        logger.error(f"Website filtering failed: {e}")
        raise

if __name__ == "__main__":
    main()
