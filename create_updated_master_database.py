#!/usr/bin/env python3
"""
Create Updated Master Database
=============================

This script creates a new master database file that includes:
1. Existing master_business_database_2025-06-26.csv
2. New Rank Professional Services 31-120 deduplicated data

Author: Augment Agent
Date: 2025-06-26
"""

import pandas as pd
import os
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_master_database.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_existing_master():
    """Load the existing master database."""
    master_file = "master_business_database_2025-06-26.csv"
    logger.info(f"Loading existing master database: {master_file}")
    
    try:
        df = pd.read_csv(master_file, low_memory=False)
        logger.info(f"Loaded existing master database with {len(df):,} records")
        return df
    except Exception as e:
        logger.error(f"Error loading master database: {e}")
        raise

def load_rank_31_120_data():
    """Load all the Rank Professional Services 31-120 deduplicated files."""
    logger.info("Loading Rank Professional Services 31-120 deduplicated data...")
    
    files = [
        'rank_professional_services_31_120_deduped_by_domain_part01.csv',
        'rank_professional_services_31_120_deduped_by_domain_part02.csv',
        'rank_professional_services_31_120_deduped_by_domain_part03.csv',
        'rank_professional_services_31_120_deduped_by_domain_part04.csv'
    ]
    
    all_dataframes = []
    total_records = 0
    
    for file_path in files:
        if os.path.exists(file_path):
            logger.info(f"Loading: {file_path}")
            df = pd.read_csv(file_path, low_memory=False)
            all_dataframes.append(df)
            total_records += len(df)
            logger.info(f"  - Loaded {len(df):,} records")
        else:
            logger.warning(f"File not found: {file_path}")
    
    if not all_dataframes:
        raise ValueError("No Rank Professional Services 31-120 files found")
    
    # Combine all dataframes
    combined_df = pd.concat(all_dataframes, ignore_index=True)
    logger.info(f"Total Rank Professional Services 31-120 records: {len(combined_df):,}")
    
    return combined_df

def prepare_rank_31_120_for_master(df):
    """Prepare the Rank 31-120 data for inclusion in the master database."""
    logger.info("Preparing Rank Professional Services 31-120 data for master database...")
    
    # Create a copy
    prepared_df = df.copy()
    
    # Add master database specific columns
    prepared_df['dataset_name'] = 'Rank_Professional_Services_31-120'
    prepared_df['industry_group'] = 'Professional Services'
    prepared_df['created_date'] = datetime.now().strftime('%Y-%m-%d')
    prepared_df['master_file_version'] = 2.0
    
    # Ensure all required columns exist (matching the master database structure)
    required_columns = [
        'dataset_name', 'industry_group', 'title', 'domain', 'info@ email',
        'address', 'website', 'category', 'position', 'latitude', 'longitude',
        'rating', 'ratingCount', 'phoneNumber', 'cid', 'bookingLinks',
        'scraped_business_name', 'scraped_phone', 'email', 'description',
        'business_insights', 'contact_persons', 'services', 'extraction_metadata',
        'extraction_success', 'extraction_timestamp', 'priceLevel', 'scrape',
        'source_file', 'source_category', 'created_date', 'master_file_version'
    ]
    
    for col in required_columns:
        if col not in prepared_df.columns:
            prepared_df[col] = None
    
    # Reorder columns to match master database structure
    prepared_df = prepared_df[required_columns]
    
    logger.info(f"Prepared {len(prepared_df):,} records for master database")
    
    return prepared_df

def create_new_master_database(existing_master, new_data, output_filename):
    """Create the new master database file."""
    logger.info("Creating new master database...")
    
    # Combine the dataframes
    combined_df = pd.concat([existing_master, new_data], ignore_index=True)
    
    # Save to file
    combined_df.to_csv(output_filename, index=False)
    
    # Get file size
    file_size_mb = os.path.getsize(output_filename) / (1024 * 1024)
    
    logger.info(f"New master database created:")
    logger.info(f"  - File: {output_filename}")
    logger.info(f"  - Total records: {len(combined_df):,}")
    logger.info(f"  - File size: {file_size_mb:.1f} MB")
    logger.info(f"  - Existing records: {len(existing_master):,}")
    logger.info(f"  - New records added: {len(new_data):,}")
    
    return combined_df

def generate_update_report(existing_count, new_count, final_count, output_file):
    """Generate a report about the master database update."""
    report_path = 'master_database_update_report.txt'
    
    with open(report_path, 'w') as f:
        f.write("Master Database Update Report\n")
        f.write("=" * 30 + "\n")
        f.write(f"Update Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("Update Summary:\n")
        f.write("-" * 15 + "\n")
        f.write(f"Previous master database records: {existing_count:,}\n")
        f.write(f"New records added (Rank Professional Services 31-120): {new_count:,}\n")
        f.write(f"Total records in new master database: {final_count:,}\n")
        f.write(f"Growth: +{new_count:,} records ({new_count/existing_count*100:.1f}% increase)\n\n")
        
        f.write("Dataset Composition:\n")
        f.write("-" * 19 + "\n")
        f.write("1. Original master database records\n")
        f.write("2. Rank Professional Services 1-30 (deduplicated)\n")
        f.write("3. Rank Professional Services 31-120 (deduplicated)\n\n")
        
        f.write("Files:\n")
        f.write("-" * 6 + "\n")
        f.write(f"Previous master file: master_business_database_2025-06-26.csv\n")
        f.write(f"New master file: {output_file}\n")
        f.write(f"Update log: update_master_database.log\n")
        f.write(f"This report: {report_path}\n")
    
    logger.info(f"Update report saved to: {report_path}")

def main():
    """Main function to create updated master database."""
    logger.info("Starting master database update process...")
    
    try:
        # Step 1: Load existing master database
        logger.info("Step 1: Loading existing master database...")
        existing_master = load_existing_master()
        
        # Step 2: Load Rank Professional Services 31-120 data
        logger.info("Step 2: Loading Rank Professional Services 31-120 data...")
        rank_31_120_data = load_rank_31_120_data()
        
        # Step 3: Prepare new data for master database
        logger.info("Step 3: Preparing new data for master database...")
        prepared_data = prepare_rank_31_120_for_master(rank_31_120_data)
        
        # Step 4: Create new master database
        logger.info("Step 4: Creating new master database...")
        today = datetime.now().strftime('%Y-%m-%d')
        output_filename = f"master_business_database_{today}_v2.csv"
        
        final_df = create_new_master_database(existing_master, prepared_data, output_filename)
        
        # Step 5: Generate update report
        logger.info("Step 5: Generating update report...")
        generate_update_report(len(existing_master), len(prepared_data), len(final_df), output_filename)
        
        logger.info("Master database update completed successfully!")
        logger.info(f"New master database: {output_filename}")
        logger.info(f"Total records: {len(final_df):,}")
        
        # Summary statistics
        logger.info(f"\nFINAL SUMMARY:")
        logger.info(f"- Previous master database: {len(existing_master):,} records")
        logger.info(f"- New records added: {len(prepared_data):,} records")
        logger.info(f"- Final master database: {len(final_df):,} records")
        logger.info(f"- Growth: +{len(prepared_data)/len(existing_master)*100:.1f}%")
        
    except Exception as e:
        logger.error(f"Master database update failed: {e}")
        raise

if __name__ == "__main__":
    main()
