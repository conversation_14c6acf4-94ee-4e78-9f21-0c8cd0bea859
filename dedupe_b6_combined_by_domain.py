import pandas as pd
import os
import logging
import math
from datetime import datetime
from urllib.parse import urlparse

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('b6_deduplication.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def extract_domain(url):
    """Extract main domain from URL, removing subdomains and www."""
    if pd.isna(url) or url == '':
        return None

    try:
        # Clean the URL
        url = str(url).strip()

        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # Remove www prefix
        if domain.startswith('www.'):
            domain = domain[4:]

        # Remove common subdomains (but keep main domain)
        domain_parts = domain.split('.')
        if len(domain_parts) > 2:
            # Keep only the last two parts (domain.tld)
            domain = '.'.join(domain_parts[-2:])

        return domain if domain else None

    except Exception as e:
        logger.debug(f"Error extracting domain from {url}: {e}")
        return None

def create_info_email(domain):
    """Create info@ email from domain."""
    if pd.isna(domain) or domain == '':
        return None
    return f"info@{domain}"

def load_combined_file_in_chunks(file_path, chunk_size=50000):
    """Load the large combined file in chunks for processing."""
    logger.info(f"Loading combined file: {file_path}")

    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return None

    try:
        # Get total number of rows first
        total_rows = sum(1 for line in open(file_path, 'r', encoding='utf-8')) - 1  # subtract header
        logger.info(f"Total rows in file: {total_rows:,}")

        # Read file in chunks
        chunk_list = []
        chunk_count = 0

        for chunk in pd.read_csv(file_path, chunksize=chunk_size, low_memory=False):
            chunk_count += 1
            chunk_list.append(chunk)
            logger.info(f"Loaded chunk {chunk_count}: {len(chunk):,} rows")

        # Combine all chunks
        combined_df = pd.concat(chunk_list, ignore_index=True)
        logger.info(f"Successfully loaded {len(combined_df):,} total records")

        return combined_df

    except Exception as e:
        logger.error(f"Error loading file: {e}")
        return None

def process_domains_and_emails(df):
    """Process domains and create info@ emails if not already present."""
    logger.info("Processing domains and emails...")

    # Find website column (could be 'website', 'Website', 'url', etc.)
    website_cols = [col for col in df.columns if 'website' in col.lower() or 'url' in col.lower()]

    if not website_cols:
        logger.warning("No website column found, checking for other URL-like columns")
        # Look for columns that might contain URLs
        for col in df.columns:
            if df[col].dtype == 'object':
                sample_values = df[col].dropna().head(10).astype(str)
                if any('.' in str(val) and ('http' in str(val) or 'www' in str(val) or '.com' in str(val)) for val in sample_values):
                    website_cols = [col]
                    logger.info(f"Found potential website column: {col}")
                    break

    if website_cols:
        website_col = website_cols[0]
        logger.info(f"Using website column: {website_col}")

        # Extract domains - always overwrite existing domain column
        logger.info("Extracting domains from website URLs...")
        df['domain'] = None
        mask = df[website_col].notna()

        # Apply domain extraction to non-null website values
        logger.info(f"Processing {mask.sum():,} records with website URLs...")
        df.loc[mask, 'domain'] = df.loc[mask, website_col].apply(extract_domain)

        # Create info@ emails
        logger.info("Creating info@ email addresses...")
        df['info@ email'] = df['domain'].apply(create_info_email)
    else:
        logger.warning("No website column found - cannot extract domains")
        if 'domain' not in df.columns:
            df['domain'] = None
        if 'info@ email' not in df.columns:
            df['info@ email'] = None

    # Log results
    total_records = len(df)
    records_with_domain = df['domain'].notna().sum()
    records_with_email = df['info@ email'].notna().sum()

    logger.info("Domain processing results:")
    logger.info(f"  - Total records: {total_records:,}")
    logger.info(f"  - Records with domain: {records_with_domain:,} ({records_with_domain/total_records*100:.1f}%)")
    logger.info(f"  - Records with info@ email: {records_with_email:,} ({records_with_email/total_records*100:.1f}%)")

    return df

def deduplicate_by_domain(df):
    """Remove duplicate records by domain within the dataset."""
    logger.info("Deduplicating records by domain...")

    initial_count = len(df)

    # Separate records with and without domains
    df_with_domain = df[df['domain'].notna()].copy()
    df_without_domain = df[df['domain'].isna()].copy()

    logger.info(f"  - Initial records: {initial_count:,}")
    logger.info(f"  - Records without domain: {len(df_without_domain):,}")
    logger.info(f"  - Records with domain: {len(df_with_domain):,}")

    # Deduplicate records with domains (keep first occurrence)
    df_deduped = df_with_domain.drop_duplicates(subset=['domain'], keep='first')

    # Combine back with records without domains
    df_final = pd.concat([df_deduped, df_without_domain], ignore_index=True)

    final_count = len(df_final)
    duplicates_removed = len(df_with_domain) - len(df_deduped)

    logger.info(f"  - Duplicates removed: {duplicates_removed:,}")
    logger.info(f"  - Final unique records: {final_count:,}")
    if len(df_with_domain) > 0:
        logger.info(f"  - Deduplication rate: {duplicates_removed/len(df_with_domain)*100:.1f}%")
    else:
        logger.info(f"  - Deduplication rate: 0.0% (no records with domains)")

    return df_final, duplicates_removed

def get_file_size_mb(file_path):
    """Get file size in MB."""
    if os.path.exists(file_path):
        return os.path.getsize(file_path) / (1024 * 1024)
    return 0



def generate_single_file_report(initial_count, final_count, duplicates_removed, output_filename, file_size_mb, output_dir):
    """Generate a detailed deduplication report for single file output."""
    report_path = os.path.join(output_dir, 'b6_deduplication_report.txt')

    with open(report_path, 'w') as f:
        f.write("B6 Combined File Deduplication Report\n")
        f.write("=" * 37 + "\n")
        f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("Deduplication Statistics:\n")
        f.write("-" * 26 + "\n")
        f.write(f"Initial records: {initial_count:,}\n")
        f.write(f"Final unique records: {final_count:,}\n")
        f.write(f"Duplicates removed: {duplicates_removed:,}\n")
        f.write(f"Deduplication rate: {duplicates_removed/initial_count*100:.1f}%\n")
        f.write(f"Data retention rate: {final_count/initial_count*100:.1f}%\n\n")

        f.write("Output File Information:\n")
        f.write("-" * 25 + "\n")
        f.write(f"Output file: {output_filename}\n")
        f.write(f"File size: {file_size_mb:.1f} MB\n")
        f.write(f"Total records: {final_count:,}\n")

    logger.info(f"Deduplication report saved to: {report_path}")

def main():
    """Main function to deduplicate B6 combined file by domain."""
    logger.info("Starting B6 combined file deduplication by domain...")

    # Configuration
    input_file = "b6_combined_all_files.csv"
    base_output_name = "b6_combined_deduped_by_domain"

    try:
        # Step 1: Load the combined file
        logger.info("Step 1: Loading combined file...")
        df = load_combined_file_in_chunks(input_file)

        if df is None or df.empty:
            logger.error("Failed to load data. Exiting.")
            return

        initial_count = len(df)

        # Step 2: Process domains and emails
        logger.info("Step 2: Processing domains and emails...")
        df = process_domains_and_emails(df)

        # Step 3: Deduplicate by domain
        logger.info("Step 3: Deduplicating by domain...")
        df_deduped, duplicates_removed = deduplicate_by_domain(df)

        final_count = len(df_deduped)

        # Step 4: Save single output file
        logger.info("Step 4: Saving deduplicated data to single file...")
        output_filename = f"{base_output_name}.csv"
        df_deduped.to_csv(output_filename, index=False)

        # Get file size
        file_size_mb = get_file_size_mb(output_filename)
        logger.info(f"Saved deduplicated file: {output_filename}")
        logger.info(f"File size: {file_size_mb:.1f} MB")

        # Step 5: Generate deduplication report
        logger.info("Step 5: Generating deduplication report...")
        generate_single_file_report(initial_count, final_count, duplicates_removed, output_filename, file_size_mb, ".")

        logger.info("Deduplication completed successfully!")
        logger.info(f"Created single output file: {output_filename}")

        # Summary
        logger.info(f"\nSUMMARY:")
        logger.info(f"- Original records: {initial_count:,}")
        logger.info(f"- Final unique records: {final_count:,}")
        logger.info(f"- Duplicates removed: {duplicates_removed:,}")
        logger.info(f"- Deduplication rate: {duplicates_removed/initial_count*100:.1f}%")
        logger.info(f"- Output file: {output_filename}")
        logger.info(f"- Output file size: {file_size_mb:.1f} MB")

    except Exception as e:
        logger.error(f"Deduplication failed: {e}")
        raise

if __name__ == "__main__":
    main()
