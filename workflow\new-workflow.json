{"name": "My workflow", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [200, -120], "id": "49363d2b-44b1-46e1-8165-a778b30b2a39", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"batchSize": "=1", "options": {}}, "id": "9c1a3d2a-c994-43dd-971d-1341b7f453fe", "name": "Loop Search Queries", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [560, -120]}, {"parameters": {"method": "POST", "url": "https://google.serper.dev/places", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-API-KEY", "value": "c8f2c4b3b683e919d68aa4d9631ff88c16eb7d4e"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "q", "value": "=contractor near {{ $json.city }} {{ $json.state }}"}, {"name": "gl", "value": "us"}, {"name": "location", "value": "United States"}]}, "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "page", "value": "={{ $pageCount + 1 }}"}]}, "paginationCompleteWhen": "other", "completeExpression": "={{ $response.body.places.isEmpty() }}"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1160, -40], "id": "e7a8b096-499d-44eb-bc04-1bb92c367081", "name": "HTTP Request"}, {"parameters": {"jsCode": "return [\n  {\n    \"city\": \"New York\", \n    \"growth_from_2000_to_2013\": \"4.8%\", \n    \"latitude\": 40.7127837, \n    \"longitude\": -74.0059413, \n    \"population\": \"8405837\", \n    \"rank\": \"1\", \n    \"state\": \"New York\"\n  }, \n  {\n    \"city\": \"Los Angeles\", \n    \"growth_from_2000_to_2013\": \"4.8%\", \n    \"latitude\": 34.0522342, \n    \"longitude\": -118.2436849, \n    \"population\": \"3884307\", \n    \"rank\": \"2\", \n    \"state\": \"California\"\n  }, \n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [380, -120], "id": "ff8ba655-9fde-4d18-80c9-38002dd4a88d", "name": "Top Cities"}, {"parameters": {"jsCode": "console.log($input.all());\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, -40], "id": "b39de756-9187-4c79-9186-a5c066c8a71e", "name": "Code1"}, {"parameters": {"fieldToSplitOut": "places", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1400, -40], "id": "28cf4b27-f041-4eb1-9525-14e8ff44a554", "name": "Split Out1"}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Top Cities", "type": "main", "index": 0}]]}, "Loop Search Queries": {"main": [[], [{"node": "Code1", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Loop Search Queries", "type": "main", "index": 0}, {"node": "Split Out1", "type": "main", "index": 0}]]}, "Top Cities": {"main": [[{"node": "Loop Search Queries", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d8e35ac8-70b8-4127-b41f-6d44b2fa118e", "meta": {"instanceId": "f0c1230faa584ad7a426801d804cca8e0d8678d13d6f8bef8f679dc4db8541ba"}, "id": "zUdlmozsrGZMNmiK", "tags": []}