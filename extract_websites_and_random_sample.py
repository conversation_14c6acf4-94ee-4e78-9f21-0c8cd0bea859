#!/usr/bin/env python3
"""
Extract all websites from master database and create a random sample of 5000 websites.

This script:
1. Reads the latest master database file
2. Extracts all unique domains/websites
3. Creates a file with all websites
4. Creates a file with 5000 random websites from the complete list
"""

import pandas as pd
import random
import os
from datetime import datetime

def main():
    # Configuration
    master_file = "master_business_database_2025-06-26_v2.csv"
    all_websites_file = "all_websites_from_master_database.txt"
    random_websites_file = "random_5000_websites_from_master_database.txt"
    
    print("=" * 60)
    print("WEBSITE EXTRACTION FROM MASTER DATABASE")
    print("=" * 60)
    print(f"Processing: {master_file}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check if master file exists
    if not os.path.exists(master_file):
        print(f"❌ Error: Master file '{master_file}' not found!")
        return
    
    print("📊 Reading master database file...")
    try:
        # Read the CSV file in chunks to handle large file size
        chunk_size = 50000
        domains = set()
        total_records = 0
        
        for chunk_num, chunk in enumerate(pd.read_csv(master_file, chunksize=chunk_size), 1):
            # Extract domains from this chunk
            chunk_domains = chunk['domain'].dropna().unique()
            domains.update(chunk_domains)
            total_records += len(chunk)
            
            if chunk_num % 10 == 0:
                print(f"   Processed {total_records:,} records, found {len(domains):,} unique domains...")
        
        print(f"✅ Completed reading {total_records:,} total records")
        print(f"✅ Found {len(domains):,} unique domains")
        print()
        
        # Convert to sorted list for consistency
        domains_list = sorted(list(domains))
        
        # Create file with all websites
        print(f"📝 Creating file with all websites: {all_websites_file}")
        with open(all_websites_file, 'w', encoding='utf-8') as f:
            f.write(f"# All Websites from Master Database\n")
            f.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Source: {master_file}\n")
            f.write(f"# Total unique domains: {len(domains_list):,}\n")
            f.write(f"#\n")
            for domain in domains_list:
                f.write(f"{domain}\n")
        
        print(f"✅ Created {all_websites_file} with {len(domains_list):,} websites")
        print()
        
        # Create file with 5000 random websites
        if len(domains_list) >= 5000:
            print(f"🎲 Selecting 5000 random websites...")
            random.seed(42)  # For reproducible results
            random_domains = random.sample(domains_list, 5000)
            random_domains.sort()  # Sort for easier reading
            
            print(f"📝 Creating file with random websites: {random_websites_file}")
            with open(random_websites_file, 'w', encoding='utf-8') as f:
                f.write(f"# Random 5000 Websites from Master Database\n")
                f.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Source: {master_file}\n")
                f.write(f"# Selected from {len(domains_list):,} total unique domains\n")
                f.write(f"# Random seed: 42 (for reproducibility)\n")
                f.write(f"#\n")
                for domain in random_domains:
                    f.write(f"{domain}\n")
            
            print(f"✅ Created {random_websites_file} with 5,000 random websites")
        else:
            print(f"⚠️  Warning: Only {len(domains_list):,} domains available, less than 5000 requested")
            print(f"📝 Creating file with all available websites: {random_websites_file}")
            with open(random_websites_file, 'w', encoding='utf-8') as f:
                f.write(f"# All Available Websites from Master Database\n")
                f.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Source: {master_file}\n")
                f.write(f"# Total unique domains: {len(domains_list):,}\n")
                f.write(f"# Note: Requested 5000 but only {len(domains_list):,} available\n")
                f.write(f"#\n")
                for domain in domains_list:
                    f.write(f"{domain}\n")
        
        print()
        print("=" * 60)
        print("SUMMARY")
        print("=" * 60)
        print(f"📊 Total records processed: {total_records:,}")
        print(f"🌐 Unique domains found: {len(domains_list):,}")
        print(f"📁 All websites file: {all_websites_file}")
        print(f"📁 Random sample file: {random_websites_file}")
        print(f"✅ Process completed successfully!")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error processing file: {str(e)}")
        return

if __name__ == "__main__":
    main()
