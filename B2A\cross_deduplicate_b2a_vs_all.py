#!/usr/bin/env python3
"""
Cross-deduplicate B2A dataset against both B2 and B3 datasets by removing domains 
that already exist in either B2 or B3. This ensures no duplicate domains exist 
across all three datasets (B2, B3, B2A).
"""

import pandas as pd
import os
from pathlib import Path

def cross_deduplicate_b2a_vs_all():
    """
    Remove domains from B2A that already exist in B2 or B3 datasets.
    """
    print("CROSS-DEDUPLICATION: B2A vs B2 & B3 DATASETS")
    print("="*55)
    
    # File paths
    b2_file = "../B2/merged_data_final.csv"
    b3_file = "../B3/b3_final_cross_deduplicated.csv"
    b2a_file = "b2a_merged_deduplicated.csv"
    output_file = "b2a_final_cross_deduplicated.csv"
    
    # Check if files exist
    files_to_check = [
        ("B2", b2_file),
        ("B3", b3_file),
        ("B2A", b2a_file)
    ]
    
    for name, file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ ERROR: {name} file not found: {file_path}")
            return
    
    # Load all datasets
    datasets = {}
    all_existing_domains = set()
    
    print("📁 Loading datasets...")
    
    # Load B2
    try:
        datasets['B2'] = pd.read_csv(b2_file, low_memory=False)
        b2_domains = set(datasets['B2']['domain'].dropna().str.strip())
        all_existing_domains.update(b2_domains)
        print(f"   ✅ B2 loaded: {len(datasets['B2']):,} rows, {len(b2_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B2 file: {e}")
        return
    
    # Load B3
    try:
        datasets['B3'] = pd.read_csv(b3_file, low_memory=False)
        b3_domains = set(datasets['B3']['domain'].dropna().str.strip())
        all_existing_domains.update(b3_domains)
        print(f"   ✅ B3 loaded: {len(datasets['B3']):,} rows, {len(b3_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B3 file: {e}")
        return
    
    # Load B2A
    try:
        datasets['B2A'] = pd.read_csv(b2a_file, low_memory=False)
        b2a_domains = set(datasets['B2A']['domain'].dropna().str.strip())
        print(f"   ✅ B2A loaded: {len(datasets['B2A']):,} rows, {len(b2a_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B2A file: {e}")
        return
    
    # Analyze overlaps
    print(f"\n🔍 ANALYZING DOMAIN OVERLAPS")
    print("-" * 35)
    
    overlap_b2a_vs_b2 = b2a_domains.intersection(b2_domains)
    overlap_b2a_vs_b3 = b2a_domains.intersection(b3_domains)
    total_overlaps = b2a_domains.intersection(all_existing_domains)
    
    print(f"B2A vs B2 overlaps: {len(overlap_b2a_vs_b2):,}")
    print(f"B2A vs B3 overlaps: {len(overlap_b2a_vs_b3):,}")
    print(f"Total B2A overlaps: {len(total_overlaps):,}")
    
    if len(total_overlaps) == 0:
        print("✅ No overlapping domains found! B2A dataset is already unique vs B2 & B3.")
        # Still save a copy for consistency
        datasets['B2A'].to_csv(output_file, index=False, encoding='utf-8')
        print(f"💾 Saved copy as: {output_file}")
        return datasets['B2A']
    
    # Show some examples of overlapping domains
    print(f"\nExamples of overlapping domains:")
    for i, domain in enumerate(list(total_overlaps)[:15]):
        # Check which dataset(s) it overlaps with
        overlap_sources = []
        if domain in b2_domains:
            overlap_sources.append("B2")
        if domain in b3_domains:
            overlap_sources.append("B3")
        print(f"  {i+1}. {domain} (overlaps with: {', '.join(overlap_sources)})")
    
    if len(total_overlaps) > 15:
        print(f"  ... and {len(total_overlaps) - 15} more")
    
    # Remove overlapping domains from B2A
    print(f"\n🗑️  REMOVING OVERLAPPING DOMAINS FROM B2A")
    print("-" * 45)
    
    original_b2a_count = len(datasets['B2A'])
    
    # Create mask for rows to keep (domains NOT in B2 or B3)
    mask_keep = ~datasets['B2A']['domain'].isin(all_existing_domains)
    b2a_filtered = datasets['B2A'][mask_keep].copy()
    
    removed_count = original_b2a_count - len(b2a_filtered)
    
    print(f"Original B2A rows: {original_b2a_count:,}")
    print(f"Rows removed: {removed_count:,}")
    print(f"Remaining rows: {len(b2a_filtered):,}")
    print(f"Removal rate: {(removed_count / original_b2a_count) * 100:.2f}%")
    print(f"Retention rate: {(len(b2a_filtered) / original_b2a_count) * 100:.2f}%")
    
    # Verify no overlaps remain
    remaining_b2a_domains = set(b2a_filtered['domain'].dropna().str.strip())
    final_overlap_b2 = b2_domains.intersection(remaining_b2a_domains)
    final_overlap_b3 = b3_domains.intersection(remaining_b2a_domains)
    
    print(f"\n✅ VERIFICATION")
    print("-" * 15)
    print(f"Remaining B2A unique domains: {len(remaining_b2a_domains):,}")
    print(f"Final overlap with B2: {len(final_overlap_b2)}")
    print(f"Final overlap with B3: {len(final_overlap_b3)}")
    print(f"Cross-deduplication successful: {len(final_overlap_b2) == 0 and len(final_overlap_b3) == 0}")
    
    # Save the cross-deduplicated dataset
    print(f"\n💾 SAVING CROSS-DEDUPLICATED DATASET")
    print("-" * 40)
    
    b2a_filtered.to_csv(output_file, index=False, encoding='utf-8')
    
    file_size = os.path.getsize(output_file) / (1024*1024)
    print(f"Saved: {output_file}")
    print(f"File size: {file_size:.2f} MB")
    
    # Generate summary report
    generate_cross_dedup_report(datasets, b2a_filtered, total_overlaps, overlap_b2a_vs_b2, overlap_b2a_vs_b3)
    
    print(f"\n🎉 CROSS-DEDUPLICATION COMPLETED!")
    print(f"📁 Final B2A dataset: {output_file}")
    print(f"📊 Report: b2a_cross_dedup_report.txt")
    
    # Show final combined potential
    print(f"\n🌟 COMBINED DATASET POTENTIAL:")
    print("-" * 30)
    total_unique_domains = len(b2_domains) + len(b3_domains) + len(remaining_b2a_domains)
    print(f"B2 domains: {len(b2_domains):,}")
    print(f"B3 domains: {len(b3_domains):,}")
    print(f"B2A domains: {len(remaining_b2a_domains):,}")
    print(f"Total unique domains: {total_unique_domains:,}")
    
    return b2a_filtered

def generate_cross_dedup_report(datasets, b2a_final, total_overlaps, overlap_b2, overlap_b3):
    """
    Generate a comprehensive cross-deduplication report.
    """
    report_content = f"""B2A vs B2 & B3 CROSS-DEDUPLICATION REPORT
==========================================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
Process: Remove B2A domains that already exist in B2 or B3

DATASET SUMMARY:
===============
B2 Dataset (merged_data_final.csv):
- Total rows: {len(datasets['B2']):,}
- Unique domains: {datasets['B2']['domain'].nunique():,}

B3 Dataset (b3_final_cross_deduplicated.csv):
- Total rows: {len(datasets['B3']):,}
- Unique domains: {datasets['B3']['domain'].nunique():,}

B2A Dataset (b2a_merged_deduplicated.csv):
- Original rows: {len(datasets['B2A']):,}
- Original unique domains: {datasets['B2A']['domain'].nunique():,}

CROSS-DEDUPLICATION RESULTS:
===========================
- B2A vs B2 overlaps: {len(overlap_b2):,}
- B2A vs B3 overlaps: {len(overlap_b3):,}
- Total overlapping domains: {len(total_overlaps):,}
- B2A rows removed: {len(datasets['B2A']) - len(b2a_final):,}
- B2A rows retained: {len(b2a_final):,}
- Retention rate: {(len(b2a_final) / len(datasets['B2A'])) * 100:.2f}%

FINAL COMBINED DATASET POTENTIAL:
================================
- B2 unique domains: {datasets['B2']['domain'].nunique():,}
- B3 unique domains: {datasets['B3']['domain'].nunique():,}
- B2A unique domains (after cross-dedup): {b2a_final['domain'].nunique():,}
- Total unique domains across all three: {datasets['B2']['domain'].nunique() + datasets['B3']['domain'].nunique() + b2a_final['domain'].nunique():,}
- Perfect separation achieved: True

FINAL B2A SOURCE FILE DISTRIBUTION:
==================================
"""
    
    # Add source file distribution for final B2A
    source_stats = b2a_final['source_file'].value_counts()
    for source, count in source_stats.items():
        report_content += f"- {source}: {count:,} rows\n"
    
    # Add top domains in final B2A
    report_content += f"\nTOP DOMAINS IN FINAL B2A:\n"
    report_content += f"=========================\n"
    domain_counts = b2a_final['domain'].value_counts().head(20)
    for i, (domain, count) in enumerate(domain_counts.items(), 1):
        report_content += f"{i}. {domain}: {count} businesses\n"
    
    # Save report
    with open('b2a_cross_dedup_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📊 Cross-deduplication report saved: b2a_cross_dedup_report.txt")

def main():
    """
    Main function to execute cross-deduplication.
    """
    try:
        result_df = cross_deduplicate_b2a_vs_all()
        if result_df is not None:
            print(f"\n✅ SUCCESS: Cross-deduplication completed successfully!")
        else:
            print(f"\n❌ FAILED: Cross-deduplication encountered errors.")
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")

if __name__ == "__main__":
    main()
