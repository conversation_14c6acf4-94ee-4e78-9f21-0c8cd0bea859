#!/usr/bin/env python3
"""
Process Rank Professional Services 1-30 Dataset
===============================================

This script processes all Excel files in the "Rank Professional Services 1-30" folder:
1. Merges all files into a single dataset
2. Fills domain column (no subdomains)
3. Fills info@ email column
4. Deduplicates against master database
5. Creates new master database file with today's date

Author: Augment Agent
Date: 2025-06-26
"""

import pandas as pd
import os
import glob
import re
from urllib.parse import urlparse
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rank_professional_services_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def extract_domain(url):
    """
    Extract the main domain from a URL, removing subdomains.

    Args:
        url (str): URL to extract domain from

    Returns:
        str: Main domain without subdomains, or None if invalid
    """
    if pd.isna(url) or not url:
        return None

    try:
        # Clean the URL
        url = str(url).strip()

        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url

        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # Remove www. prefix
        if domain.startswith('www.'):
            domain = domain[4:]

        # Extract main domain (remove subdomains)
        domain_parts = domain.split('.')
        if len(domain_parts) >= 2:
            # Keep only the last two parts (domain.tld)
            main_domain = '.'.join(domain_parts[-2:])
            return main_domain

        return domain if domain else None

    except Exception as e:
        logger.warning(f"Error extracting domain from '{url}': {e}")
        return None

def create_info_email(domain):
    """
    Create info@ email address from domain.

    Args:
        domain (str): Domain name

    Returns:
        str: Email address in format info@domain, or None if invalid domain
    """
    if pd.isna(domain) or not domain:
        return None

    domain = str(domain).strip()
    if domain and '.' in domain:
        return f"info@{domain}"

    return None

def load_excel_files(folder_path):
    """
    Load all Excel files from the specified folder.

    Args:
        folder_path (str): Path to folder containing Excel files

    Returns:
        pd.DataFrame: Combined dataframe from all Excel files
    """
    logger.info(f"Loading Excel files from: {folder_path}")

    # Get all Excel files
    excel_files = glob.glob(os.path.join(folder_path, "*.xlsx"))
    excel_files = [f for f in excel_files if not os.path.basename(f).startswith('~')]  # Exclude temp files

    logger.info(f"Found {len(excel_files)} Excel files to process")

    all_dataframes = []
    file_stats = {}

    for file_path in excel_files:
        try:
            filename = os.path.basename(file_path)
            logger.info(f"Processing: {filename}")

            # Read Excel file
            df = pd.read_excel(file_path)

            # Add source file information
            df['source_file'] = filename
            df['source_category'] = filename.replace('.xlsx', '')

            file_stats[filename] = len(df)
            all_dataframes.append(df)

            logger.info(f"  - Loaded {len(df)} records from {filename}")

        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
            continue

    if not all_dataframes:
        raise ValueError("No valid Excel files found or processed")

    # Combine all dataframes
    combined_df = pd.concat(all_dataframes, ignore_index=True)
    logger.info(f"Combined dataset shape: {combined_df.shape}")

    return combined_df, file_stats

def process_domains_and_emails(df):
    """
    Process domain and email columns.

    Args:
        df (pd.DataFrame): Input dataframe

    Returns:
        pd.DataFrame: Dataframe with processed domain and info@ email columns
    """
    logger.info("Processing domains and emails...")

    # Create a copy to avoid modifying original
    df = df.copy()

    # Initialize domain column if it doesn't exist or is empty
    if 'domain' not in df.columns:
        df['domain'] = None

    # Extract domains from website column if domain is empty
    website_col = 'website'
    if website_col in df.columns:
        mask = df['domain'].isna() | (df['domain'] == '')
        df.loc[mask, 'domain'] = df.loc[mask, website_col].apply(extract_domain)

    # Clean existing domains (remove subdomains)
    df['domain'] = df['domain'].apply(extract_domain)

    # Create info@ email column
    df['info@ email'] = df['domain'].apply(create_info_email)

    # Log statistics
    total_records = len(df)
    records_with_domain = df['domain'].notna().sum()
    records_with_email = df['info@ email'].notna().sum()

    logger.info(f"Domain processing results:")
    logger.info(f"  - Total records: {total_records}")
    logger.info(f"  - Records with domain: {records_with_domain} ({records_with_domain/total_records*100:.1f}%)")
    logger.info(f"  - Records with info@ email: {records_with_email} ({records_with_email/total_records*100:.1f}%)")

    return df

def load_master_database(master_file_path):
    """
    Load the existing master database.

    Args:
        master_file_path (str): Path to master database CSV file

    Returns:
        pd.DataFrame: Master database dataframe
    """
    logger.info(f"Loading master database from: {master_file_path}")

    if not os.path.exists(master_file_path):
        logger.warning(f"Master database file not found: {master_file_path}")
        return pd.DataFrame()

    try:
        master_df = pd.read_csv(master_file_path)
        logger.info(f"Loaded master database with {len(master_df)} records")
        return master_df
    except Exception as e:
        logger.error(f"Error loading master database: {e}")
        return pd.DataFrame()

def deduplicate_against_master(new_df, master_df):
    """
    Remove records from new_df that already exist in master_df based on domain.

    Args:
        new_df (pd.DataFrame): New records to deduplicate
        master_df (pd.DataFrame): Existing master database

    Returns:
        tuple: (deduplicated_df, duplicate_count, unique_count)
    """
    logger.info("Deduplicating against master database...")

    if master_df.empty:
        logger.info("Master database is empty, no deduplication needed")
        return new_df, 0, len(new_df)

    # Get domains from master database
    master_domains = set()
    if 'domain' in master_df.columns:
        master_domains = set(master_df['domain'].dropna().str.lower())

    logger.info(f"Master database contains {len(master_domains)} unique domains")

    # Filter out records with domains that exist in master
    initial_count = len(new_df)

    # Create mask for records to keep (not in master)
    if 'domain' in new_df.columns:
        new_df_domains = new_df['domain'].fillna('').str.lower()
        keep_mask = ~new_df_domains.isin(master_domains)
        deduplicated_df = new_df[keep_mask].copy()
    else:
        deduplicated_df = new_df.copy()

    final_count = len(deduplicated_df)
    duplicate_count = initial_count - final_count

    logger.info(f"Deduplication results:")
    logger.info(f"  - Initial records: {initial_count}")
    logger.info(f"  - Duplicates removed: {duplicate_count}")
    logger.info(f"  - Unique records: {final_count}")
    logger.info(f"  - Deduplication rate: {duplicate_count/initial_count*100:.1f}%")

    return deduplicated_df, duplicate_count, final_count

def prepare_for_master_database(df):
    """
    Prepare the dataframe for inclusion in the master database.

    Args:
        df (pd.DataFrame): Input dataframe

    Returns:
        pd.DataFrame: Prepared dataframe with master database structure
    """
    logger.info("Preparing data for master database...")

    # Create a copy
    prepared_df = df.copy()

    # Add master database specific columns
    prepared_df['dataset_name'] = 'Rank_Professional_Services_1-30'
    prepared_df['industry_group'] = 'Professional Services'
    prepared_df['created_date'] = datetime.now().strftime('%Y-%m-%d')
    prepared_df['master_file_version'] = 1.0

    # Ensure all required columns exist
    required_columns = [
        'dataset_name', 'industry_group', 'title', 'domain', 'info@ email',
        'address', 'website', 'category', 'position', 'latitude', 'longitude',
        'rating', 'ratingCount', 'phoneNumber', 'cid', 'bookingLinks',
        'scraped_business_name', 'scraped_phone', 'email', 'description',
        'business_insights', 'contact_persons', 'services', 'extraction_metadata',
        'extraction_success', 'extraction_timestamp', 'priceLevel', 'scrape',
        'source_file', 'source_category', 'created_date', 'master_file_version'
    ]

    for col in required_columns:
        if col not in prepared_df.columns:
            prepared_df[col] = None

    # Reorder columns to match master database structure
    prepared_df = prepared_df[required_columns]

    logger.info(f"Prepared {len(prepared_df)} records for master database")

    return prepared_df

def create_new_master_database(master_df, new_df, output_path):
    """
    Create new master database file combining existing and new records.

    Args:
        master_df (pd.DataFrame): Existing master database
        new_df (pd.DataFrame): New records to add
        output_path (str): Path for new master database file

    Returns:
        pd.DataFrame: Combined master database
    """
    logger.info("Creating new master database...")

    if master_df.empty:
        combined_df = new_df.copy()
    else:
        combined_df = pd.concat([master_df, new_df], ignore_index=True)

    # Save to file
    combined_df.to_csv(output_path, index=False)

    logger.info(f"New master database created:")
    logger.info(f"  - File: {output_path}")
    logger.info(f"  - Total records: {len(combined_df)}")
    logger.info(f"  - Existing records: {len(master_df) if not master_df.empty else 0}")
    logger.info(f"  - New records added: {len(new_df)}")

    return combined_df

def generate_processing_report(file_stats, processing_stats, output_dir):
    """
    Generate a detailed processing report.

    Args:
        file_stats (dict): Statistics from file loading
        processing_stats (dict): Statistics from processing
        output_dir (str): Directory to save report
    """
    report_path = os.path.join(output_dir, 'rank_professional_services_processing_report.txt')

    with open(report_path, 'w') as f:
        f.write("Rank Professional Services 1-30 Processing Report\n")
        f.write("=" * 50 + "\n")
        f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("File Loading Statistics:\n")
        f.write("-" * 25 + "\n")
        total_files = len(file_stats)
        total_records = sum(file_stats.values())
        f.write(f"Total files processed: {total_files}\n")
        f.write(f"Total records loaded: {total_records}\n\n")

        f.write("Files processed:\n")
        for filename, count in file_stats.items():
            f.write(f"  - {filename}: {count} records\n")
        f.write("\n")

        f.write("Processing Statistics:\n")
        f.write("-" * 22 + "\n")
        for key, value in processing_stats.items():
            f.write(f"{key}: {value}\n")
        f.write("\n")

        f.write("Output Files:\n")
        f.write("-" * 13 + "\n")
        f.write(f"New master database: {processing_stats.get('output_file', 'N/A')}\n")
        f.write(f"Processing log: rank_professional_services_processing.log\n")
        f.write(f"This report: {report_path}\n")

    logger.info(f"Processing report saved to: {report_path}")

def main():
    """
    Main function to process Rank Professional Services 1-30 dataset.
    """
    logger.info("Starting Rank Professional Services 1-30 processing...")

    # Configuration
    folder_path = "Rank Professional Services 1-30"
    master_db_path = "master_business_database.csv"
    today = datetime.now().strftime('%Y-%m-%d')
    output_file = f"master_business_database_{today}.csv"

    try:
        # Step 1: Load and merge all Excel files
        logger.info("Step 1: Loading and merging Excel files...")
        combined_df, file_stats = load_excel_files(folder_path)

        # Step 2: Process domains and emails
        logger.info("Step 2: Processing domains and emails...")
        processed_df = process_domains_and_emails(combined_df)

        # Step 3: Load master database
        logger.info("Step 3: Loading master database...")
        master_df = load_master_database(master_db_path)

        # Step 4: Deduplicate against master
        logger.info("Step 4: Deduplicating against master database...")
        deduplicated_df, duplicate_count, unique_count = deduplicate_against_master(processed_df, master_df)

        # Step 5: Prepare for master database
        logger.info("Step 5: Preparing data for master database...")
        prepared_df = prepare_for_master_database(deduplicated_df)

        # Step 6: Create new master database
        logger.info("Step 6: Creating new master database...")
        final_df = create_new_master_database(master_df, prepared_df, output_file)

        # Collect processing statistics
        processing_stats = {
            'total_files_processed': len(file_stats),
            'total_records_loaded': sum(file_stats.values()),
            'records_after_domain_processing': len(processed_df),
            'records_with_domains': processed_df['domain'].notna().sum(),
            'records_with_info_emails': processed_df['info@ email'].notna().sum(),
            'duplicate_records_removed': duplicate_count,
            'unique_records_added': unique_count,
            'final_master_database_size': len(final_df),
            'existing_master_records': len(master_df) if not master_df.empty else 0,
            'output_file': output_file
        }

        # Step 7: Generate processing report
        logger.info("Step 7: Generating processing report...")
        generate_processing_report(file_stats, processing_stats, ".")

        logger.info("Processing completed successfully!")
        logger.info(f"New master database saved as: {output_file}")
        logger.info(f"Total records in new master database: {len(final_df)}")

    except Exception as e:
        logger.error(f"Processing failed: {e}")
        raise

if __name__ == "__main__":
    main()
