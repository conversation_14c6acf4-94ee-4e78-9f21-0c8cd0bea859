#!/usr/bin/env python3
"""
Final verification of Hotel and Buildings processing results and overall cross-dataset verification.
"""

import pandas as pd
import os

def verify_all_datasets():
    print("COMPLETE DATASET VERIFICATION (5 DATASETS)")
    print("="*50)
    
    # Check all files exist
    files_to_check = [
        ("B2 final", "../B2/merged_data_final.csv"),
        ("B3 final", "../B3/b3_final_cross_deduplicated.csv"),
        ("B2A final", "../B2A/b2a_final_cross_deduplicated.csv"),
        ("B4 final", "../B4 Done/b4_final_cross_deduplicated.csv"),
        ("Hotel & Buildings merged", "hotel_buildings_merged_deduplicated.csv"),
        ("Hotel & Buildings final", "hotel_buildings_final_cross_deduplicated.csv")
    ]
    
    print("FILE EXISTENCE CHECK:")
    print("-" * 25)
    for name, path in files_to_check:
        if os.path.exists(path):
            size_mb = os.path.getsize(path) / (1024*1024)
            print(f"✅ {name}: {size_mb:.2f} MB")
        else:
            print(f"❌ {name}: NOT FOUND")
    
    # Load final datasets
    try:
        print(f"\nLOADING ALL FINAL DATASETS:")
        print("-" * 35)
        
        b2_df = pd.read_csv("../B2/merged_data_final.csv", low_memory=False)
        b3_df = pd.read_csv("../B3/b3_final_cross_deduplicated.csv", low_memory=False)
        b2a_df = pd.read_csv("../B2A/b2a_final_cross_deduplicated.csv", low_memory=False)
        b4_df = pd.read_csv("../B4 Done/b4_final_cross_deduplicated.csv", low_memory=False)
        hb_df = pd.read_csv("hotel_buildings_final_cross_deduplicated.csv", low_memory=False)
        
        print(f"B2 final: {len(b2_df):,} rows")
        print(f"B3 final: {len(b3_df):,} rows")
        print(f"B2A final: {len(b2a_df):,} rows")
        print(f"B4 final: {len(b4_df):,} rows")
        print(f"Hotel & Buildings final: {len(hb_df):,} rows")
        
    except Exception as e:
        print(f"❌ Error loading files: {e}")
        return
    
    # Extract domains
    b2_domains = set(b2_df['domain'].dropna())
    b3_domains = set(b3_df['domain'].dropna())
    b2a_domains = set(b2a_df['domain'].dropna())
    b4_domains = set(b4_df['domain'].dropna())
    hb_domains = set(hb_df['domain'].dropna())
    
    print(f"\nDOMAIN ANALYSIS:")
    print("-" * 20)
    print(f"B2 unique domains: {len(b2_domains):,}")
    print(f"B3 unique domains: {len(b3_domains):,}")
    print(f"B2A unique domains: {len(b2a_domains):,}")
    print(f"B4 unique domains: {len(b4_domains):,}")
    print(f"Hotel & Buildings unique domains: {len(hb_domains):,}")
    
    # Check for any overlaps (should all be zero)
    overlaps = {
        'B2 vs B3': b2_domains.intersection(b3_domains),
        'B2 vs B2A': b2_domains.intersection(b2a_domains),
        'B2 vs B4': b2_domains.intersection(b4_domains),
        'B2 vs H&B': b2_domains.intersection(hb_domains),
        'B3 vs B2A': b3_domains.intersection(b2a_domains),
        'B3 vs B4': b3_domains.intersection(b4_domains),
        'B3 vs H&B': b3_domains.intersection(hb_domains),
        'B2A vs B4': b2a_domains.intersection(b4_domains),
        'B2A vs H&B': b2a_domains.intersection(hb_domains),
        'B4 vs H&B': b4_domains.intersection(hb_domains)
    }
    
    print(f"\nCOMPLETE OVERLAP VERIFICATION:")
    print("-" * 40)
    all_overlaps_zero = True
    for comparison, overlap_set in overlaps.items():
        overlap_count = len(overlap_set)
        print(f"{comparison} overlap: {overlap_count} domains")
        if overlap_count > 0:
            all_overlaps_zero = False
    
    print(f"Perfect separation achieved: {all_overlaps_zero}")
    
    # Combined statistics
    total_unique = (len(b2_domains) + len(b3_domains) + len(b2a_domains) + 
                   len(b4_domains) + len(hb_domains))
    
    print(f"\nCOMBINED DATASET SUMMARY:")
    print("-" * 30)
    print(f"Total unique domains: {total_unique:,}")
    print(f"B2 contribution: {len(b2_domains):,} ({len(b2_domains)/total_unique*100:.1f}%)")
    print(f"B3 contribution: {len(b3_domains):,} ({len(b3_domains)/total_unique*100:.1f}%)")
    print(f"B2A contribution: {len(b2a_domains):,} ({len(b2a_domains)/total_unique*100:.1f}%)")
    print(f"B4 contribution: {len(b4_domains):,} ({len(b4_domains)/total_unique*100:.1f}%)")
    print(f"H&B contribution: {len(hb_domains):,} ({len(hb_domains)/total_unique*100:.1f}%)")
    
    # Hotel & Buildings category breakdown
    print(f"\nHOTEL & BUILDINGS CATEGORY BREAKDOWN:")
    print("-" * 45)
    category_counts = hb_df['source_category'].value_counts()
    for category, count in category_counts.items():
        percentage = (count / len(hb_df)) * 100
        print(f"{category}: {count:,} ({percentage:.1f}%)")
    
    # Top source files in Hotel & Buildings
    print(f"\nTOP HOTEL & BUILDINGS SOURCE FILES:")
    print("-" * 40)
    source_counts = hb_df['source_file'].value_counts()
    for source, count in source_counts.head(10).items():
        percentage = (count / len(hb_df)) * 100
        print(f"{source}: {count:,} ({percentage:.1f}%)")
    
    # Sample data from each dataset
    print(f"\nSAMPLE DATA FROM EACH DATASET:")
    print("-" * 35)
    
    print("B2 Sample:")
    for _, row in b2_df[['title', 'domain']].head(3).iterrows():
        print(f"  {row['title'][:40]}... -> {row['domain']}")
    
    print("B3 Sample:")
    for _, row in b3_df[['title', 'domain']].head(3).iterrows():
        print(f"  {row['title'][:40]}... -> {row['domain']}")
    
    print("B2A Sample:")
    for _, row in b2a_df[['title', 'domain']].head(3).iterrows():
        print(f"  {row['title'][:40]}... -> {row['domain']}")
    
    print("B4 Sample:")
    for _, row in b4_df[['title', 'domain']].head(3).iterrows():
        print(f"  {row['title'][:40]}... -> {row['domain']}")
    
    print("Hotel & Buildings Sample:")
    for _, row in hb_df[['title', 'domain']].head(3).iterrows():
        print(f"  {row['title'][:40]}... -> {row['domain']}")
    
    # Domain cleaning verification for Hotel & Buildings
    print(f"\nHOTEL & BUILDINGS DOMAIN CLEANING VERIFICATION:")
    print("-" * 50)
    sample_hb = hb_df[['website', 'domain']].head(5)
    for _, row in sample_hb.iterrows():
        print(f"  {row['website'][:50]}... -> {row['domain']}")
    
    # Check for subdomains in Hotel & Buildings (should be minimal)
    subdomain_count = 0
    for domain in hb_df['domain'].dropna():
        parts = domain.split('.')
        if len(parts) > 2:
            # Check if it's a legitimate subdomain or country domain
            if not (parts[-2] in ['co', 'com', 'net', 'org', 'gov', 'edu', 'ac']):
                subdomain_count += 1
    
    print(f"\nDOMAIN QUALITY CHECK:")
    print("-" * 25)
    print(f"Potential subdomains in H&B: {subdomain_count}")
    print(f"Domain cleaning effectiveness: {((len(hb_domains) - subdomain_count) / len(hb_domains)) * 100:.1f}%")
    
    # Final status
    print(f"\n🎉 FINAL STATUS:")
    print("-" * 15)
    if all_overlaps_zero:
        print("✅ All five datasets are perfectly separated")
        print("✅ Ready for combined use without duplicates")
        print(f"✅ Total addressable market: {total_unique:,} unique domains")
        print("✅ Domain cleaning successful (subdomains removed)")
    else:
        print("❌ Some overlaps still exist - review needed")
    
    # Generate final summary report
    generate_final_summary_report(b2_df, b3_df, b2a_df, b4_df, hb_df, total_unique, all_overlaps_zero)

def generate_final_summary_report(b2_df, b3_df, b2a_df, b4_df, hb_df, total_unique, all_overlaps_zero):
    """
    Generate a final summary report for all five datasets.
    """
    report_content = f"""COMPLETE 5-DATASET PROCESSING SUMMARY
====================================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
All five datasets processed and cross-deduplicated successfully

FINAL DATASET SUMMARY:
=====================
B2 Dataset: {len(b2_df):,} rows, {b2_df['domain'].nunique():,} unique domains
B3 Dataset: {len(b3_df):,} rows, {b3_df['domain'].nunique():,} unique domains  
B2A Dataset: {len(b2a_df):,} rows, {b2a_df['domain'].nunique():,} unique domains
B4 Dataset: {len(b4_df):,} rows, {b4_df['domain'].nunique():,} unique domains
Hotel & Buildings Dataset: {len(hb_df):,} rows, {hb_df['domain'].nunique():,} unique domains

TOTAL: {len(b2_df) + len(b3_df) + len(b2a_df) + len(b4_df) + len(hb_df):,} rows, {total_unique:,} unique domains

CROSS-DEDUPLICATION STATUS:
==========================
Perfect separation achieved: {all_overlaps_zero}
Zero overlaps between all datasets: {all_overlaps_zero}

DATASET CONTRIBUTIONS:
=====================
B2: {b2_df['domain'].nunique():,} domains ({b2_df['domain'].nunique()/total_unique*100:.1f}%)
B3: {b3_df['domain'].nunique():,} domains ({b3_df['domain'].nunique()/total_unique*100:.1f}%)
B2A: {b2a_df['domain'].nunique():,} domains ({b2a_df['domain'].nunique()/total_unique*100:.1f}%)
B4: {b4_df['domain'].nunique():,} domains ({b4_df['domain'].nunique()/total_unique*100:.1f}%)
Hotel & Buildings: {hb_df['domain'].nunique():,} domains ({hb_df['domain'].nunique()/total_unique*100:.1f}%)

INDUSTRY COVERAGE:
=================
- Healthcare & Medical: B2, B3, B4 (partial)
- Pet Care & Dental: B2A
- Accounting & Banking: B4
- Hotels & Travel: Hotel & Buildings (partial)
- Real Estate & Construction: Hotel & Buildings (partial)

READY FOR USE:
=============
✅ Email marketing campaigns (no duplicate outreach)
✅ Lead generation (comprehensive coverage)
✅ Business directory creation
✅ Market analysis and segmentation
✅ Competitive intelligence
✅ Sales prospecting across multiple industries

FILES LOCATION:
==============
- B2: B2/merged_data_final.csv
- B3: B3/b3_final_cross_deduplicated.csv
- B2A: B2A/b2a_final_cross_deduplicated.csv
- B4: B4 Done/b4_final_cross_deduplicated.csv
- Hotel & Buildings: hotel and buildings/hotel_buildings_final_cross_deduplicated.csv
"""
    
    # Save report
    with open('final_complete_5dataset_summary.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n📊 Final 5-dataset summary report saved: final_complete_5dataset_summary.txt")

if __name__ == "__main__":
    verify_all_datasets()
