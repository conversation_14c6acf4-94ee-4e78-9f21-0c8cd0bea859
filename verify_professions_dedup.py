import pandas as pd

files = [
    'professions_deduped_by_domain_part01.csv',
    'professions_deduped_by_domain_part02.csv', 
    'professions_deduped_by_domain_part03.csv',
    'professions_deduped_by_domain_part04.csv'
]

all_domains = []
total_records = 0
for f in files:
    df = pd.read_csv(f, usecols=['domain'])
    all_domains.extend(df['domain'].dropna().tolist())
    total_records += len(df)

print(f'Total domains across all files: {len(all_domains)}')
print(f'Unique domains across all files: {len(set(all_domains))}')
print(f'Verification: No duplicates = {len(all_domains) == len(set(all_domains))}')
print(f'Total records across all files: {total_records}')
