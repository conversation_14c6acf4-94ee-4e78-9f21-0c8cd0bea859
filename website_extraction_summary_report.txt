# Website Extraction Summary Report
Generated: 2025-07-04 19:06:01

## Overview
Successfully extracted all unique websites from the latest master database and created a random sample of 5,000 websites.

## Source Data
- **Master Database File:** master_business_database_2025-06-26_v2.csv
- **File Size:** 1.26 GB (1,264,228,794 bytes)
- **Total Records Processed:** 3,644,119 business records
- **Processing Method:** Chunked reading (50,000 records per chunk) for memory efficiency

## Results

### 1. Complete Website List
- **File:** all_websites_from_master_database.txt
- **Total Unique Domains:** 2,458,802 websites
- **File Size:** 51.6 MB (51,588,125 bytes)
- **Format:** Plain text, one domain per line
- **Content:** All unique domains from the master database, sorted alphabetically

### 2. Random Sample
- **File:** random_5000_websites_from_master_database.txt
- **Sample Size:** 5,000 websites
- **File Size:** 105 KB (105,187 bytes)
- **Selection Method:** Random sampling with seed 42 (for reproducibility)
- **Format:** Plain text, one domain per line, sorted alphabetically

## File Structure
Both files include header comments with:
- Generation timestamp
- Source file information
- Total domain counts
- Processing notes

## Data Quality
- **Deduplication:** All domains are unique (no duplicates)
- **Cleaning:** Domains extracted from cleaned master database
- **Encoding:** UTF-8 text format
- **Sorting:** Alphabetically sorted for easy navigation

## Usage Examples

### Load All Websites (Python)
```python
websites = []
with open('all_websites_from_master_database.txt', 'r') as f:
    for line in f:
        if not line.startswith('#') and line.strip():
            websites.append(line.strip())
print(f"Loaded {len(websites):,} websites")
```

### Load Random Sample (Python)
```python
random_websites = []
with open('random_5000_websites_from_master_database.txt', 'r') as f:
    for line in f:
        if not line.startswith('#') and line.strip():
            random_websites.append(line.strip())
print(f"Loaded {len(random_websites):,} random websites")
```

### PowerShell Usage
```powershell
# Count websites (excluding header)
$websites = Get-Content all_websites_from_master_database.txt | Where-Object { -not $_.StartsWith('#') -and $_.Trim() }
Write-Host "Total websites: $($websites.Count)"

# Load random sample
$randomSample = Get-Content random_5000_websites_from_master_database.txt | Where-Object { -not $_.StartsWith('#') -and $_.Trim() }
Write-Host "Random sample size: $($randomSample.Count)"
```

## Sample Domains
From the complete list:
- 0-60.com
- 000webhostapp.com
- 001aliceabailbonds.com
- ...

From the random sample:
- 10kautos.com
- 120moving.com
- 1333gough.com
- 1800dnagene.com
- ...

## Processing Statistics
- **Processing Time:** ~5 minutes
- **Memory Usage:** Chunked processing for large file handling
- **Success Rate:** 100% - all records processed successfully
- **Data Integrity:** Verified unique domain extraction

## Files Created
1. `all_websites_from_master_database.txt` - Complete website list (2,458,802 domains)
2. `random_5000_websites_from_master_database.txt` - Random sample (5,000 domains)
3. `extract_websites_and_random_sample.py` - Processing script
4. `website_extraction_summary_report.txt` - This summary report

## Next Steps
These files can be used for:
- Email marketing campaigns
- Website analysis and research
- Lead generation activities
- Market research and competitive analysis
- SEO and digital marketing initiatives

## Technical Notes
- Random seed 42 used for reproducible random sampling
- Alphabetical sorting applied to both files for consistency
- Header comments included for documentation
- UTF-8 encoding ensures international character support
- Chunked processing handles large datasets efficiently

---
**Process completed successfully on 2025-07-04 at 19:06:01**
