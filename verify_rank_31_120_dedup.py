import pandas as pd

files = [
    'rank_professional_services_31_120_deduped_by_domain_part01.csv',
    'rank_professional_services_31_120_deduped_by_domain_part02.csv', 
    'rank_professional_services_31_120_deduped_by_domain_part03.csv',
    'rank_professional_services_31_120_deduped_by_domain_part04.csv'
]

all_domains = []
for f in files:
    df = pd.read_csv(f, usecols=['domain'])
    all_domains.extend(df['domain'].dropna().tolist())

print(f'Total domains across all files: {len(all_domains)}')
print(f'Unique domains across all files: {len(set(all_domains))}')
print(f'Verification: No duplicates = {len(all_domains) == len(set(all_domains))}')

# Also check total records
total_records = 0
for f in files:
    df = pd.read_csv(f, usecols=['domain'])
    total_records += len(df)
    
print(f'Total records across all files: {total_records}')
