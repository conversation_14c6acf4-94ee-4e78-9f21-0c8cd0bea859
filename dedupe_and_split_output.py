#!/usr/bin/env python3
"""
Deduplicate by Domain and Split Output File
==========================================

This script:
1. Deduplicates the Professional Services records by domain
2. Splits the result into files no larger than 50MB each

Author: Augment Agent
Date: 2025-06-26
"""

import pandas as pd
import os
import math
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dedupe_and_split.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_file_size_mb(file_path):
    """Get file size in MB."""
    if os.path.exists(file_path):
        return os.path.getsize(file_path) / (1024 * 1024)
    return 0

def deduplicate_by_domain(df):
    """
    Deduplicate records by domain, keeping the first occurrence.
    
    Args:
        df (pd.DataFrame): Input dataframe
        
    Returns:
        pd.DataFrame: Deduplicated dataframe
    """
    logger.info("Deduplicating records by domain...")
    
    initial_count = len(df)
    
    # Remove records with no domain first
    df_with_domain = df[df['domain'].notna() & (df['domain'] != '')].copy()
    records_without_domain = len(df) - len(df_with_domain)
    
    # Deduplicate by domain (keep first occurrence)
    df_deduped = df_with_domain.drop_duplicates(subset=['domain'], keep='first')
    
    final_count = len(df_deduped)
    duplicates_removed = len(df_with_domain) - final_count
    
    logger.info(f"Domain deduplication results:")
    logger.info(f"  - Initial records: {initial_count:,}")
    logger.info(f"  - Records without domain: {records_without_domain:,}")
    logger.info(f"  - Records with domain: {len(df_with_domain):,}")
    logger.info(f"  - Duplicates removed: {duplicates_removed:,}")
    logger.info(f"  - Final unique records: {final_count:,}")
    logger.info(f"  - Deduplication rate: {duplicates_removed/len(df_with_domain)*100:.1f}%")
    
    return df_deduped

def estimate_rows_per_50mb(df, sample_size=1000):
    """
    Estimate how many rows fit in 50MB based on a sample.
    
    Args:
        df (pd.DataFrame): Input dataframe
        sample_size (int): Number of rows to sample for estimation
        
    Returns:
        int: Estimated rows per 50MB file
    """
    logger.info("Estimating optimal file split size...")
    
    # Take a sample and save it to estimate size
    sample_df = df.head(min(sample_size, len(df)))
    temp_file = "temp_sample.csv"
    sample_df.to_csv(temp_file, index=False)
    
    sample_size_mb = get_file_size_mb(temp_file)
    os.remove(temp_file)
    
    # Calculate rows per MB
    rows_per_mb = sample_size / sample_size_mb
    
    # Target 45MB to leave some buffer for 50MB limit
    target_mb = 45
    estimated_rows = int(rows_per_mb * target_mb)
    
    logger.info(f"  - Sample size: {sample_size} rows = {sample_size_mb:.2f} MB")
    logger.info(f"  - Estimated rows per MB: {rows_per_mb:.0f}")
    logger.info(f"  - Target rows per file (45MB): {estimated_rows:,}")
    
    return estimated_rows

def split_dataframe(df, rows_per_file, base_filename):
    """
    Split dataframe into multiple files.
    
    Args:
        df (pd.DataFrame): Input dataframe
        rows_per_file (int): Number of rows per file
        base_filename (str): Base filename for output files
        
    Returns:
        list: List of created file paths
    """
    logger.info(f"Splitting dataframe into files of {rows_per_file:,} rows each...")
    
    total_rows = len(df)
    num_files = math.ceil(total_rows / rows_per_file)
    
    logger.info(f"  - Total rows: {total_rows:,}")
    logger.info(f"  - Rows per file: {rows_per_file:,}")
    logger.info(f"  - Number of files: {num_files}")
    
    created_files = []
    
    for i in range(num_files):
        start_idx = i * rows_per_file
        end_idx = min((i + 1) * rows_per_file, total_rows)
        
        # Create chunk
        chunk_df = df.iloc[start_idx:end_idx].copy()
        
        # Generate filename
        filename = f"{base_filename}_part{i+1:02d}.csv"
        
        # Save chunk
        chunk_df.to_csv(filename, index=False)
        
        # Check file size
        file_size_mb = get_file_size_mb(filename)
        
        logger.info(f"  - Created {filename}: {len(chunk_df):,} rows, {file_size_mb:.1f} MB")
        
        created_files.append({
            'filename': filename,
            'rows': len(chunk_df),
            'size_mb': file_size_mb
        })
    
    return created_files

def generate_split_report(original_file, deduped_stats, split_files, output_dir):
    """
    Generate a report about the deduplication and splitting process.
    
    Args:
        original_file (str): Original file name
        deduped_stats (dict): Deduplication statistics
        split_files (list): List of created files with stats
        output_dir (str): Output directory
    """
    report_path = os.path.join(output_dir, 'dedupe_and_split_report.txt')
    
    with open(report_path, 'w') as f:
        f.write("Professional Services - Dedupe and Split Report\n")
        f.write("=" * 48 + "\n")
        f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("Original File:\n")
        f.write("-" * 14 + "\n")
        f.write(f"File: {original_file}\n")
        f.write(f"Original size: ~510 MB\n\n")
        
        f.write("Deduplication Results:\n")
        f.write("-" * 22 + "\n")
        for key, value in deduped_stats.items():
            f.write(f"{key}: {value}\n")
        f.write("\n")
        
        f.write("Split Files Created:\n")
        f.write("-" * 20 + "\n")
        total_size = 0
        for i, file_info in enumerate(split_files, 1):
            f.write(f"{i:2d}. {file_info['filename']}: {file_info['rows']:,} rows, {file_info['size_mb']:.1f} MB\n")
            total_size += file_info['size_mb']
        
        f.write(f"\nTotal split files: {len(split_files)}\n")
        f.write(f"Total size: {total_size:.1f} MB\n")
        f.write(f"Average file size: {total_size/len(split_files):.1f} MB\n")
        f.write(f"All files under 50MB: {'Yes' if all(f['size_mb'] < 50 for f in split_files) else 'No'}\n")
    
    logger.info(f"Split report saved to: {report_path}")

def main():
    """
    Main function to deduplicate and split the Professional Services data.
    """
    logger.info("Starting dedupe and split process...")
    
    # Configuration
    input_file = "rank_professional_services_deduplicated_records.csv"
    base_output_name = "rank_professional_services_deduped_by_domain"
    
    try:
        # Step 1: Load the data
        logger.info(f"Step 1: Loading data from {input_file}...")
        df = pd.read_csv(input_file, low_memory=False)
        logger.info(f"Loaded {len(df):,} records")
        
        # Step 2: Deduplicate by domain
        logger.info("Step 2: Deduplicating by domain...")
        df_deduped = deduplicate_by_domain(df)
        
        # Step 3: Estimate optimal split size
        logger.info("Step 3: Estimating optimal split size...")
        rows_per_file = estimate_rows_per_50mb(df_deduped)
        
        # Step 4: Split the data
        logger.info("Step 4: Splitting data into files...")
        split_files = split_dataframe(df_deduped, rows_per_file, base_output_name)
        
        # Collect statistics
        deduped_stats = {
            'original_records': len(df),
            'records_with_domain': (df['domain'].notna() & (df['domain'] != '')).sum(),
            'final_unique_records': len(df_deduped),
            'duplicates_removed': len(df) - len(df_deduped),
            'deduplication_rate_percent': round((len(df) - len(df_deduped))/len(df)*100, 1)
        }
        
        # Step 5: Generate report
        logger.info("Step 5: Generating report...")
        generate_split_report(input_file, deduped_stats, split_files, ".")
        
        logger.info("Process completed successfully!")
        logger.info(f"Created {len(split_files)} files, all under 50MB")
        
        # Summary
        total_size = sum(f['size_mb'] for f in split_files)
        logger.info(f"\nSUMMARY:")
        logger.info(f"- Original records: {len(df):,}")
        logger.info(f"- Unique records after dedup: {len(df_deduped):,}")
        logger.info(f"- Files created: {len(split_files)}")
        logger.info(f"- Total size: {total_size:.1f} MB")
        logger.info(f"- Average file size: {total_size/len(split_files):.1f} MB")
        
    except Exception as e:
        logger.error(f"Process failed: {e}")
        raise

if __name__ == "__main__":
    main()
