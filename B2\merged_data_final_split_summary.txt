FILE SPLIT SUMMARY
==================

Original file: B2/merged_data_final.csv
Original size: 117.84 MB
Original rows: 393,754

Split into 3 parts:
- merged_data_final_part1.csv: 41.20 MB (131,252 rows)
- merged_data_final_part2.csv: 39.63 MB (131,252 rows)
- merged_data_final_part3.csv: 37.01 MB (131,250 rows)

To recombine files:
```python
import pandas as pd
parts = []
parts.append(pd.read_csv('merged_data_final_part1.csv'))
parts.append(pd.read_csv('merged_data_final_part2.csv'))
parts.append(pd.read_csv('merged_data_final_part3.csv'))
combined = pd.concat(parts, ignore_index=True)
combined.to_csv('merged_data_final_recombined.csv', index=False)
```
