#!/usr/bin/env python3
"""
Script to clean domains by removing subdomains and deduplicate by domain.
"""

import pandas as pd
import re

def clean_domain(domain):
    """
    Clean domain by removing common subdomains and keeping only the main domain.
    """
    if pd.isna(domain) or domain == '':
        return None
    
    try:
        domain = str(domain).lower().strip()
        
        # List of common subdomains to remove
        subdomains_to_remove = [
            'www.', 'local.', 'store.', 'shop.', 'booking.', 'appointments.', 
            'schedule.', 'book.', 'online.', 'web.', 'site.', 'portal.', 
            'app.', 'mobile.', 'locations.', 'find.', 'search.', 'directory.',
            'maps.', 'map.', 'location.', 'stores.', 'offices.', 'clinic.',
            'hospital.', 'health.', 'care.', 'medical.', 'doctors.', 'dr.',
            'practice.', 'services.', 'center.', 'centres.', 'facility.',
            'facilities.', 'branch.', 'branches.', 'office.', 'main.',
            'home.', 'index.', 'welcome.', 'about.', 'contact.', 'info.',
            'support.', 'help.', 'customer.', 'client.', 'patient.',
            'member.', 'members.', 'login.', 'signin.', 'register.',
            'signup.', 'account.', 'profile.', 'dashboard.', 'admin.',
            'staff.', 'team.', 'professionals.', 'specialists.', 'experts.'
        ]
        
        # Remove common subdomains
        for subdomain in subdomains_to_remove:
            if domain.startswith(subdomain):
                domain = domain[len(subdomain):]
                break
        
        # Handle special cases for multi-level subdomains
        # Split by dots and analyze
        parts = domain.split('.')
        
        if len(parts) >= 3:
            # For domains like "local.lenscrafters.com" or "store.walmart.com"
            # Keep only the last two parts (main domain + TLD)
            # But be careful with country domains like "co.uk", "com.au"
            
            # List of common country/regional TLDs that need 3 parts
            country_tlds = [
                'co.uk', 'com.au', 'co.nz', 'co.za', 'com.br', 'co.jp',
                'co.in', 'com.mx', 'co.kr', 'com.sg', 'co.th', 'com.my',
                'co.id', 'com.ph', 'co.il', 'com.tr', 'co.ve', 'com.ar',
                'com.co', 'com.pe', 'com.ec', 'com.uy', 'com.py', 'com.bo'
            ]
            
            # Check if it's a country TLD
            last_two = '.'.join(parts[-2:])
            if last_two in country_tlds:
                # Keep last 3 parts for country domains
                domain = '.'.join(parts[-3:])
            else:
                # Keep last 2 parts for regular domains
                domain = '.'.join(parts[-2:])
        
        # Final validation
        if '.' not in domain or len(domain) < 3:
            return None
            
        return domain
    except:
        return None

def clean_domains_and_dedupe():
    """
    Clean domains and deduplicate by cleaned domain.
    """
    print("Loading merged_data_with_domains.csv...")
    
    # Read the CSV file
    df = pd.read_csv('merged_data_with_domains.csv', low_memory=False)
    
    print(f"Original data shape: {df.shape}")
    print(f"Original rows: {len(df):,}")
    
    # Show some examples of current domains that need cleaning
    print(f"\nAnalyzing domains that need cleaning...")
    
    # Find domains with subdomains
    subdomain_examples = []
    for domain in df['domain'].dropna().unique()[:1000]:  # Check first 1000 unique domains
        if domain.count('.') >= 2:  # Has subdomain
            subdomain_examples.append(domain)
    
    print(f"Examples of domains with subdomains (before cleaning):")
    for i, domain in enumerate(subdomain_examples[:10], 1):
        cleaned = clean_domain(domain)
        print(f"  {i}. {domain} → {cleaned}")
    
    # Clean domains
    print(f"\nCleaning domains...")
    df['cleaned_domain'] = df['domain'].apply(clean_domain)
    
    # Compare original vs cleaned domains
    original_unique = df['domain'].nunique()
    cleaned_unique = df['cleaned_domain'].nunique()
    
    print(f"Domain cleaning results:")
    print(f"  - Original unique domains: {original_unique:,}")
    print(f"  - Cleaned unique domains: {cleaned_unique:,}")
    print(f"  - Reduction: {original_unique - cleaned_unique:,} domains")
    
    # Show most common domain changes
    print(f"\nMost common domain changes:")
    domain_changes = df[df['domain'] != df['cleaned_domain']].groupby(['domain', 'cleaned_domain']).size().reset_index(name='count')
    domain_changes = domain_changes.sort_values('count', ascending=False)
    
    for _, row in domain_changes.head(10).iterrows():
        print(f"  {row['domain']} → {row['cleaned_domain']} ({row['count']} businesses)")
    
    # Update the domain and info@ email columns
    df['domain'] = df['cleaned_domain']
    df['info@ email'] = df['cleaned_domain'].apply(
        lambda x: f"info@{x}" if pd.notna(x) and x != '' else None
    )
    
    # Drop the temporary column
    df = df.drop('cleaned_domain', axis=1)
    
    # Check for duplicates by cleaned domain
    print(f"\nAnalyzing duplicates by cleaned domain...")
    
    total_rows = len(df)
    unique_domains = df['domain'].nunique()
    duplicate_count = total_rows - unique_domains
    
    print(f"Duplicate analysis:")
    print(f"  - Total rows: {total_rows:,}")
    print(f"  - Unique domains: {unique_domains:,}")
    print(f"  - Duplicate rows: {duplicate_count:,}")
    
    if duplicate_count > 0:
        # Show which domains have the most duplicates
        domain_counts = df['domain'].value_counts()
        top_duplicates = domain_counts[domain_counts > 1].head(10)
        
        print(f"\nDomains with most duplicates:")
        for domain, count in top_duplicates.items():
            print(f"  {domain}: {count} businesses")
        
        # Deduplicate by domain, keeping the first occurrence
        print(f"\nRemoving duplicates by domain...")
        df_deduped = df.drop_duplicates(subset=['domain'], keep='first')
        
        print(f"After deduplication:")
        print(f"  - Remaining rows: {len(df_deduped):,}")
        print(f"  - Removed duplicate rows: {len(df) - len(df_deduped):,}")
        
        # Show which source files contributed most to duplicates
        removed_rows = df[df.duplicated(subset=['domain'], keep='first')]
        if len(removed_rows) > 0:
            source_counts = removed_rows['source_file'].value_counts()
            print(f"\nSource files with most removed duplicates:")
            for source, count in source_counts.head(10).items():
                print(f"  {source}: {count} duplicates removed")
    else:
        df_deduped = df
        print("No duplicates found after domain cleaning.")
    
    # Final summary
    print(f"\nFinal processing summary:")
    print(f"  - Original rows: {len(df):,}")
    print(f"  - Final rows: {len(df_deduped):,}")
    print(f"  - Rows removed: {len(df) - len(df_deduped):,}")
    print(f"  - Unique domains: {df_deduped['domain'].nunique():,}")
    print(f"  - Retention rate: {(len(df_deduped) / len(df) * 100):.2f}%")
    
    # Save the cleaned and deduplicated data
    output_file = "merged_data_final.csv"
    print(f"\nSaving final data to: {output_file}")
    
    df_deduped.to_csv(output_file, index=False, encoding='utf-8')
    
    # Check file sizes
    import os
    original_size = os.path.getsize('merged_data_with_domains.csv') / (1024*1024)
    final_size = os.path.getsize(output_file) / (1024*1024)
    
    print(f"\nFile size comparison:")
    print(f"  - Original file: {original_size:.2f} MB")
    print(f"  - Final file: {final_size:.2f} MB")
    print(f"  - Size change: {final_size - original_size:.2f} MB")
    
    # Show final domain statistics
    print(f"\nFinal domain statistics:")
    final_domain_counts = df_deduped['domain'].value_counts()
    print(f"  - Total unique domains: {len(final_domain_counts):,}")
    print(f"  - Top domains:")
    for domain, count in final_domain_counts.head(10).items():
        print(f"    {domain}: {count} (should be 1 after dedup)")
    
    # Show sample of final data
    print(f"\nSample of final cleaned data:")
    sample = df_deduped[['title', 'website', 'domain', 'info@ email']].head(5)
    for _, row in sample.iterrows():
        print(f"  Business: {row['title'][:50]}...")
        print(f"  Website: {row['website']}")
        print(f"  Domain: {row['domain']}")
        print(f"  Email: {row['info@ email']}")
        print()
    
    print(f"Processing completed successfully!")
    return df_deduped

if __name__ == "__main__":
    clean_domains_and_dedupe()
