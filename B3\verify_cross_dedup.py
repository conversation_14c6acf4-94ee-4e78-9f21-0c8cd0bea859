#!/usr/bin/env python3
"""
Verify the cross-deduplication results.
"""

import pandas as pd
import os

def verify_cross_dedup():
    print("CROSS-DEDUPLICATION VERIFICATION")
    print("="*40)
    
    # Load all three datasets
    try:
        print("Loading datasets...")
        b2_df = pd.read_csv("../B2/merged_data_final.csv")
        b3_original = pd.read_csv("b3_merged_deduplicated.csv")
        b3_final = pd.read_csv("b3_final_cross_deduplicated.csv")
        
        print(f"✅ B2 dataset: {len(b2_df):,} rows")
        print(f"✅ B3 original: {len(b3_original):,} rows")
        print(f"✅ B3 final: {len(b3_final):,} rows")
        
    except Exception as e:
        print(f"❌ Error loading files: {e}")
        return
    
    # Extract domains
    b2_domains = set(b2_df['domain'].dropna())
    b3_original_domains = set(b3_original['domain'].dropna())
    b3_final_domains = set(b3_final['domain'].dropna())
    
    print(f"\nDOMAIN ANALYSIS:")
    print("-" * 20)
    print(f"B2 unique domains: {len(b2_domains):,}")
    print(f"B3 original domains: {len(b3_original_domains):,}")
    print(f"B3 final domains: {len(b3_final_domains):,}")
    
    # Check for overlaps
    overlap_b2_b3_final = b2_domains.intersection(b3_final_domains)
    overlap_removed = b3_original_domains.intersection(b2_domains)
    
    print(f"\nOVERLAP VERIFICATION:")
    print("-" * 20)
    print(f"Overlap B2 vs B3 final: {len(overlap_b2_b3_final)} domains")
    print(f"Domains removed from B3: {len(overlap_removed):,}")
    print(f"Cross-deduplication successful: {len(overlap_b2_b3_final) == 0}")
    
    # Combined potential
    total_unique = len(b2_domains) + len(b3_final_domains)
    print(f"\nCOMBINED DATASET POTENTIAL:")
    print("-" * 30)
    print(f"Total unique domains: {total_unique:,}")
    print(f"B2 contribution: {len(b2_domains):,} ({len(b2_domains)/total_unique*100:.1f}%)")
    print(f"B3 contribution: {len(b3_final_domains):,} ({len(b3_final_domains)/total_unique*100:.1f}%)")
    
    # File sizes
    print(f"\nFILE SIZES:")
    print("-" * 15)
    files = [
        ("B2 final", "../B2/merged_data_final.csv"),
        ("B3 original", "b3_merged_deduplicated.csv"),
        ("B3 cross-dedup", "b3_final_cross_deduplicated.csv")
    ]
    
    for name, path in files:
        if os.path.exists(path):
            size_mb = os.path.getsize(path) / (1024*1024)
            print(f"{name}: {size_mb:.2f} MB")
    
    # Sample data from final B3
    print(f"\nSAMPLE B3 FINAL DATA:")
    print("-" * 20)
    sample = b3_final[['title', 'domain', 'source_file']].head()
    for _, row in sample.iterrows():
        print(f"  {row['title'][:35]}... -> {row['domain']}")

if __name__ == "__main__":
    verify_cross_dedup()
