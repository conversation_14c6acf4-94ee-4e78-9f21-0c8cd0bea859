{"name": "Parallel City Processing Workflow", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [200, 300], "id": "manual-trigger", "name": "Start Processing"}, {"parameters": {"jsCode": "// Load cities data\nconst cities = [\n  { \"city\": \"New York\", \"latitude\": 40.7127837, \"longitude\": -74.0059413, \"state\": \"New York\" },\n  { \"city\": \"Los Angeles\", \"latitude\": 34.0522342, \"longitude\": -118.2436849, \"state\": \"California\" },\n  { \"city\": \"Chicago\", \"latitude\": 41.8781136, \"longitude\": -87.6297982, \"state\": \"Illinois\" },\n  { \"city\": \"Houston\", \"latitude\": 29.7604267, \"longitude\": -95.3698028, \"state\": \"Texas\" },\n  { \"city\": \"Phoenix\", \"latitude\": 33.4483771, \"longitude\": -112.0740373, \"state\": \"Arizona\" },\n  { \"city\": \"Philadelphia\", \"latitude\": 39.9525839, \"longitude\": -75.1652215, \"state\": \"Pennsylvania\" }\n];\n\n// Split cities into batches for parallel processing\nconst batchSize = 5; // Process 5 cities in parallel - adjust based on your resources\nconst batches = [];\n\nfor (let i = 0; i < cities.length; i += batchSize) {\n  batches.push({\n    batchId: Math.floor(i / batchSize) + 1,\n    cities: cities.slice(i, i + batchSize)\n  });\n}\n\nreturn batches.map(batch => ({ json: batch }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 300], "id": "split-cities", "name": "Split Cities into Batches"}, {"parameters": {"batchSize": 1, "options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [600, 300], "id": "batch-loop", "name": "Process Batches"}, {"parameters": {"workflowId": "{{ $json.workflowId || 'city-processor-subworkflow' }}", "waitForSubWorkflow": false, "source": "parameter", "parameters": {"parameters": [{"name": "cities", "value": "={{ $json.cities }}"}, {"name": "batchId", "value": "={{ $json.batchId }}"}]}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [800, 300], "id": "execute-parallel", "name": "Execute Parallel City Processing"}], "connections": {"Start Processing": {"main": [[{"node": "Split Cities into Batches", "type": "main", "index": 0}]]}, "Split Cities into Batches": {"main": [[{"node": "Process Batches", "type": "main", "index": 0}]]}, "Process Batches": {"main": [[], [{"node": "Execute Parallel City Processing", "type": "main", "index": 0}]]}, "Execute Parallel City Processing": {"main": [[{"node": "Process Batches", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}}