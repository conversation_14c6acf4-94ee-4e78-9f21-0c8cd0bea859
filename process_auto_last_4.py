#!/usr/bin/env python3
"""
Process Auto Last 4 Dataset
===========================

This script processes all Excel files in the "Auto Last 4" folder:
1. Merges all files from all subdirectories into a single dataset
2. Fills domain column (no subdomains)
3. Fills info@ email column
4. Deduplicates against master database
5. Deduplicates by domain within the dataset
6. Splits into files no larger than 50MB

Author: Augment Agent
Date: 2025-06-26
"""

import pandas as pd
import os
import glob
import math
from urllib.parse import urlparse
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_last_4_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def extract_domain(url):
    """Extract the main domain from a URL, removing subdomains."""
    if pd.isna(url) or not url:
        return None

    try:
        url = str(url).strip()
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url

        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        if domain.startswith('www.'):
            domain = domain[4:]

        domain_parts = domain.split('.')
        if len(domain_parts) >= 2:
            main_domain = '.'.join(domain_parts[-2:])
            return main_domain

        return domain if domain else None

    except Exception as e:
        logger.warning(f"Error extracting domain from '{url}': {e}")
        return None

def create_info_email(domain):
    """Create info@ email address from domain."""
    if pd.isna(domain) or not domain:
        return None

    domain = str(domain).strip()
    if domain and '.' in domain:
        return f"info@{domain}"

    return None

def load_excel_files_recursive(base_folder):
    """Load all Excel files from the base folder and all subdirectories."""
    logger.info(f"Loading Excel files from: {base_folder}")

    # Get all Excel files recursively
    excel_files = []
    for root, dirs, files in os.walk(base_folder):
        for file in files:
            if file.endswith('.xlsx') and not file.startswith('~'):
                excel_files.append(os.path.join(root, file))

    logger.info(f"Found {len(excel_files)} Excel files to process")

    all_dataframes = []
    file_stats = {}

    for file_path in excel_files:
        try:
            filename = os.path.basename(file_path)
            subfolder = os.path.basename(os.path.dirname(file_path))
            logger.info(f"Processing: {subfolder}/{filename}")

            # Read Excel file
            df = pd.read_excel(file_path)

            # Add source file information
            df['source_file'] = filename
            df['source_category'] = filename.replace('.xlsx', '')
            df['source_subfolder'] = subfolder

            file_stats[f"{subfolder}/{filename}"] = len(df)
            all_dataframes.append(df)

            logger.info(f"  - Loaded {len(df)} records from {subfolder}/{filename}")

        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
            continue

    if not all_dataframes:
        raise ValueError("No valid Excel files found or processed")

    # Combine all dataframes
    combined_df = pd.concat(all_dataframes, ignore_index=True)
    logger.info(f"Combined dataset shape: {combined_df.shape}")

    return combined_df, file_stats

def process_domains_and_emails(df):
    """Process domain and email columns."""
    logger.info("Processing domains and emails...")

    df = df.copy()

    # Initialize domain column if it doesn't exist or is empty
    if 'domain' not in df.columns:
        df['domain'] = None

    # Extract domains from website column if domain is empty
    website_col = 'website'
    if website_col in df.columns:
        mask = df['domain'].isna() | (df['domain'] == '')
        df.loc[mask, 'domain'] = df.loc[mask, website_col].apply(extract_domain)

    # Clean existing domains (remove subdomains)
    df['domain'] = df['domain'].apply(extract_domain)

    # Create info@ email column
    df['info@ email'] = df['domain'].apply(create_info_email)

    # Log statistics
    total_records = len(df)
    records_with_domain = df['domain'].notna().sum()
    records_with_email = df['info@ email'].notna().sum()

    logger.info(f"Domain processing results:")
    logger.info(f"  - Total records: {total_records}")
    logger.info(f"  - Records with domain: {records_with_domain} ({records_with_domain/total_records*100:.1f}%)")
    logger.info(f"  - Records with info@ email: {records_with_email} ({records_with_email/total_records*100:.1f}%)")

    return df

def load_master_database(master_file_path):
    """Load the existing master database."""
    logger.info(f"Loading master database from: {master_file_path}")

    if not os.path.exists(master_file_path):
        logger.warning(f"Master database file not found: {master_file_path}")
        return pd.DataFrame()

    try:
        master_df = pd.read_csv(master_file_path, low_memory=False)
        logger.info(f"Loaded master database with {len(master_df)} records")
        return master_df
    except Exception as e:
        logger.error(f"Error loading master database: {e}")
        return pd.DataFrame()

def deduplicate_against_master(new_df, master_df):
    """Remove records from new_df that already exist in master_df based on domain."""
    logger.info("Deduplicating against master database...")

    if master_df.empty:
        logger.info("Master database is empty, no deduplication needed")
        return new_df, 0, len(new_df)

    # Get domains from master database
    master_domains = set()
    if 'domain' in master_df.columns:
        master_domains = set(master_df['domain'].dropna().str.lower())

    logger.info(f"Master database contains {len(master_domains)} unique domains")

    # Filter out records with domains that exist in master
    initial_count = len(new_df)

    if 'domain' in new_df.columns:
        new_df_domains = new_df['domain'].fillna('').str.lower()
        keep_mask = ~new_df_domains.isin(master_domains)
        deduplicated_df = new_df[keep_mask].copy()
    else:
        deduplicated_df = new_df.copy()

    final_count = len(deduplicated_df)
    duplicate_count = initial_count - final_count

    logger.info(f"Master deduplication results:")
    logger.info(f"  - Initial records: {initial_count}")
    logger.info(f"  - Duplicates removed: {duplicate_count}")
    logger.info(f"  - Unique records: {final_count}")
    logger.info(f"  - Deduplication rate: {duplicate_count/initial_count*100:.1f}%")

    return deduplicated_df, duplicate_count, final_count

def deduplicate_by_domain(df):
    """Deduplicate records by domain, keeping the first occurrence."""
    logger.info("Deduplicating records by domain within dataset...")

    initial_count = len(df)

    # Remove records with no domain first
    df_with_domain = df[df['domain'].notna() & (df['domain'] != '')].copy()
    records_without_domain = len(df) - len(df_with_domain)

    # Deduplicate by domain (keep first occurrence)
    df_deduped = df_with_domain.drop_duplicates(subset=['domain'], keep='first')

    final_count = len(df_deduped)
    duplicates_removed = len(df_with_domain) - final_count

    logger.info(f"Domain deduplication results:")
    logger.info(f"  - Initial records: {initial_count}")
    logger.info(f"  - Records without domain: {records_without_domain}")
    logger.info(f"  - Records with domain: {len(df_with_domain)}")
    logger.info(f"  - Duplicates removed: {duplicates_removed}")
    logger.info(f"  - Final unique records: {final_count}")
    logger.info(f"  - Deduplication rate: {duplicates_removed/len(df_with_domain)*100:.1f}%")

    return df_deduped

def get_file_size_mb(file_path):
    """Get file size in MB."""
    if os.path.exists(file_path):
        return os.path.getsize(file_path) / (1024 * 1024)
    return 0

def estimate_rows_per_50mb(df, sample_size=1000):
    """Estimate how many rows fit in 50MB based on a sample."""
    logger.info("Estimating optimal file split size...")

    # Take a sample and save it to estimate size
    sample_df = df.head(min(sample_size, len(df)))
    temp_file = "temp_sample_auto.csv"
    sample_df.to_csv(temp_file, index=False)

    sample_size_mb = get_file_size_mb(temp_file)
    os.remove(temp_file)

    # Calculate rows per MB
    rows_per_mb = sample_size / sample_size_mb

    # Target 45MB to leave some buffer for 50MB limit
    target_mb = 45
    estimated_rows = int(rows_per_mb * target_mb)

    logger.info(f"  - Sample size: {sample_size} rows = {sample_size_mb:.2f} MB")
    logger.info(f"  - Estimated rows per MB: {rows_per_mb:.0f}")
    logger.info(f"  - Target rows per file (45MB): {estimated_rows:,}")

    return estimated_rows

def split_dataframe(df, rows_per_file, base_filename):
    """Split dataframe into multiple files."""
    logger.info(f"Splitting dataframe into files of {rows_per_file:,} rows each...")

    total_rows = len(df)
    num_files = math.ceil(total_rows / rows_per_file)

    logger.info(f"  - Total rows: {total_rows:,}")
    logger.info(f"  - Rows per file: {rows_per_file:,}")
    logger.info(f"  - Number of files: {num_files}")

    created_files = []

    for i in range(num_files):
        start_idx = i * rows_per_file
        end_idx = min((i + 1) * rows_per_file, total_rows)

        # Create chunk
        chunk_df = df.iloc[start_idx:end_idx].copy()

        # Generate filename
        filename = f"{base_filename}_part{i+1:02d}.csv"

        # Save chunk
        chunk_df.to_csv(filename, index=False)

        # Check file size
        file_size_mb = get_file_size_mb(filename)

        logger.info(f"  - Created {filename}: {len(chunk_df):,} rows, {file_size_mb:.1f} MB")

        created_files.append({
            'filename': filename,
            'rows': len(chunk_df),
            'size_mb': file_size_mb
        })

    return created_files

def generate_processing_report(file_stats, processing_stats, split_files, output_dir):
    """Generate a detailed processing report."""
    report_path = os.path.join(output_dir, 'auto_last_4_processing_report.txt')

    with open(report_path, 'w') as f:
        f.write("Auto Last 4 Processing Report\n")
        f.write("=" * 30 + "\n")
        f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("File Loading Statistics:\n")
        f.write("-" * 25 + "\n")
        total_files = len(file_stats)
        total_records = sum(file_stats.values())
        f.write(f"Total files processed: {total_files}\n")
        f.write(f"Total records loaded: {total_records:,}\n\n")

        f.write("Files processed:\n")
        for filename, count in file_stats.items():
            f.write(f"  - {filename}: {count:,} records\n")
        f.write("\n")

        f.write("Processing Statistics:\n")
        f.write("-" * 22 + "\n")
        for key, value in processing_stats.items():
            if isinstance(value, (int, float)):
                f.write(f"{key}: {value:,}\n")
            else:
                f.write(f"{key}: {value}\n")
        f.write("\n")

        f.write("Split Files Created:\n")
        f.write("-" * 20 + "\n")
        total_size = 0
        for i, file_info in enumerate(split_files, 1):
            f.write(f"{i:2d}. {file_info['filename']}: {file_info['rows']:,} rows, {file_info['size_mb']:.1f} MB\n")
            total_size += file_info['size_mb']

        f.write(f"\nTotal split files: {len(split_files)}\n")
        f.write(f"Total size: {total_size:.1f} MB\n")
        f.write(f"Average file size: {total_size/len(split_files):.1f} MB\n")
        f.write(f"All files under 50MB: {'Yes' if all(f['size_mb'] < 50 for f in split_files) else 'No'}\n")

    logger.info(f"Processing report saved to: {report_path}")

def main():
    """Main function to process Auto Last 4 dataset."""
    logger.info("Starting Auto Last 4 processing...")

    # Configuration
    folder_path = "Auto Last 4"
    master_db_path = "master_business_database_2025-06-26.csv"
    base_output_name = "auto_last_4_deduped_by_domain"

    try:
        # Step 1: Load and merge all Excel files
        logger.info("Step 1: Loading and merging Excel files...")
        combined_df, file_stats = load_excel_files_recursive(folder_path)

        # Step 2: Process domains and emails
        logger.info("Step 2: Processing domains and emails...")
        processed_df = process_domains_and_emails(combined_df)

        # Step 3: Load master database
        logger.info("Step 3: Loading master database...")
        master_df = load_master_database(master_db_path)

        # Step 4: Deduplicate against master
        logger.info("Step 4: Deduplicating against master database...")
        deduplicated_df, master_duplicate_count, unique_after_master = deduplicate_against_master(processed_df, master_df)

        # Step 5: Deduplicate by domain within dataset
        logger.info("Step 5: Deduplicating by domain within dataset...")
        final_df = deduplicate_by_domain(deduplicated_df)

        # Step 6: Estimate optimal split size and split
        logger.info("Step 6: Estimating optimal split size...")
        rows_per_file = estimate_rows_per_50mb(final_df)

        logger.info("Step 7: Splitting data into files...")
        split_files = split_dataframe(final_df, rows_per_file, base_output_name)

        # Collect processing statistics
        processing_stats = {
            'total_files_processed': len(file_stats),
            'total_records_loaded': sum(file_stats.values()),
            'records_after_domain_processing': len(processed_df),
            'records_with_domains': processed_df['domain'].notna().sum(),
            'records_with_info_emails': processed_df['info@ email'].notna().sum(),
            'master_duplicates_removed': master_duplicate_count,
            'records_after_master_dedup': unique_after_master,
            'internal_duplicates_removed': len(deduplicated_df) - len(final_df),
            'final_unique_records': len(final_df),
            'total_deduplication_rate_percent': round((sum(file_stats.values()) - len(final_df))/sum(file_stats.values())*100, 1),
            'files_created': len(split_files)
        }

        # Step 8: Generate processing report
        logger.info("Step 8: Generating processing report...")
        generate_processing_report(file_stats, processing_stats, split_files, ".")

        logger.info("Processing completed successfully!")
        logger.info(f"Created {len(split_files)} files, all under 50MB")

        # Summary
        total_size = sum(f['size_mb'] for f in split_files)
        logger.info(f"\nSUMMARY:")
        logger.info(f"- Original records: {sum(file_stats.values()):,}")
        logger.info(f"- Final unique records: {len(final_df):,}")
        logger.info(f"- Total deduplication rate: {processing_stats['total_deduplication_rate_percent']}%")
        logger.info(f"- Files created: {len(split_files)}")
        logger.info(f"- Total size: {total_size:.1f} MB")
        logger.info(f"- Average file size: {total_size/len(split_files):.1f} MB")

    except Exception as e:
        logger.error(f"Processing failed: {e}")
        raise

if __name__ == "__main__":
    main()
