{"name": "City Processor Subworkflow", "nodes": [{"parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"cities\": [\n    {\"city\": \"New York\", \"state\": \"New York\", \"latitude\": 40.7127837, \"longitude\": -74.0059413}\n  ],\n  \"batchId\": 1\n}"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [200, 300], "id": "subworkflow-trigger", "name": "Subworkflow Trigger"}, {"parameters": {"jsCode": "// Extract cities from input\nconst cities = $input.first().json.cities;\nconst batchId = $input.first().json.batchId;\n\nconsole.log(`Processing batch ${batchId} with ${cities.length} cities`);\n\n// Return each city as separate item for parallel processing\nreturn cities.map(city => ({\n  json: {\n    ...city,\n    batchId: batchId,\n    timestamp: new Date().toISOString()\n  }\n}));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 300], "id": "extract-cities", "name": "Extract Cities"}, {"parameters": {"method": "POST", "url": "https://google.serper.dev/places", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-API-KEY", "value": "c8f2c4b3b683e919d68aa4d9631ff88c16eb7d4e"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "q", "value": "=contractor near {{ $json.city }} {{ $json.state }}"}, {"name": "gl", "value": "us"}, {"name": "location", "value": "United States"}, {"name": "num", "value": "20"}]}, "options": {"timeout": 30000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 300], "id": "serper-search", "name": "Search Places"}, {"parameters": {"jsCode": "// Process Serper response and add city context\nconst cityData = $input.first().json;\nconst response = $input.last().json;\n\nif (!response.places || response.places.length === 0) {\n  console.log(`No places found for ${cityData.city}, ${cityData.state}`);\n  return [];\n}\n\nconsole.log(`Found ${response.places.length} places for ${cityData.city}, ${cityData.state}`);\n\n// Add city context to each place\nreturn response.places.map(place => ({\n  json: {\n    ...place,\n    sourceCity: cityData.city,\n    sourceState: cityData.state,\n    batchId: cityData.batchId,\n    searchTimestamp: cityData.timestamp\n  }\n}));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 300], "id": "process-places", "name": "Process Places"}, {"parameters": {"fieldToSplitOut": "json", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1000, 300], "id": "split-places", "name": "Split Places"}, {"parameters": {"compare": "allFieldsExcept", "fieldsToExclude": {"fields": ["searchTimestamp"]}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1.2, "position": [1200, 300], "id": "remove-duplicates", "name": "Remove Duplicates"}], "connections": {"Subworkflow Trigger": {"main": [[{"node": "Extract Cities", "type": "main", "index": 0}]]}, "Extract Cities": {"main": [[{"node": "Search Places", "type": "main", "index": 0}]]}, "Search Places": {"main": [[{"node": "Process Places", "type": "main", "index": 0}]]}, "Process Places": {"main": [[{"node": "Split Places", "type": "main", "index": 0}]]}, "Split Places": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}}