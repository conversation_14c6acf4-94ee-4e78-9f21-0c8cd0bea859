import pandas as pd
import os

# Check the latest master database
master_file = 'master_business_database_2025-06-26_v2.csv'

if os.path.exists(master_file):
    print(f"Loading master database: {master_file}")
    df = pd.read_csv(master_file, low_memory=False)
    
    print(f"Total records in master database: {len(df):,}")
    
    if 'domain' in df.columns:
        unique_domains = df['domain'].dropna().nunique()
        total_domains = df['domain'].notna().sum()
        print(f"Records with domains: {total_domains:,}")
        print(f"Unique domains: {unique_domains:,}")
        print(f"Domain coverage: {total_domains/len(df)*100:.1f}%")
    else:
        print("No domain column found in master database")
    
    print(f"\nColumns in master database: {list(df.columns)}")
    print(f"File size: {os.path.getsize(master_file) / (1024*1024*1024):.2f} GB")
    
else:
    print(f"Master database file not found: {master_file}")
    
    # Check for other master database files
    master_files = [f for f in os.listdir('.') if f.startswith('master_business_database') and f.endswith('.csv')]
    if master_files:
        print(f"\nFound other master database files:")
        for f in sorted(master_files):
            size_mb = os.path.getsize(f) / (1024*1024)
            print(f"  - {f}: {size_mb:.1f} MB")
    else:
        print("No master database files found")
