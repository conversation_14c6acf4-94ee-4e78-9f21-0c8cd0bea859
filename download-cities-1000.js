// Script to download the complete 1000 US cities dataset from GitHub Gist
// and create a local us-cities-1000.js file

const fs = require('fs');
const https = require('https');

const GIST_URL = 'https://gist.githubusercontent.com/Miserlou/c5cd8364bf9b2420bb29/raw/2bf258763cdddd704f8ffd3ea9a3e81d25e2c6f6/cities.json';

console.log('Downloading 1000 US cities dataset from GitHub Gist...');

https.get(GIST_URL, (response) => {
  let data = '';

  response.on('data', (chunk) => {
    data += chunk;
  });

  response.on('end', () => {
    try {
      const cities = JSON.parse(data);
      console.log(`Downloaded ${cities.length} cities`);

      // Create the JavaScript file content
      const fileContent = `// Complete 1000 US Cities dataset from GitHub Gist
// Source: https://gist.github.com/Miserlou/c5cd8364bf9b2420bb29
// Contains the 1000 largest US cities by population with geographic coordinates

const cities1000 = ${JSON.stringify(cities, null, 2)};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = cities1000;
}

// For browser environments
if (typeof window !== 'undefined') {
  window.cities1000 = cities1000;
}`;

      // Write to file
      fs.writeFileSync('us-cities-1000-complete.js', fileContent);
      console.log('✅ Successfully created us-cities-1000-complete.js');
      console.log(`📊 Total cities: ${cities.length}`);
      console.log('📁 File size:', (fileContent.length / 1024).toFixed(2), 'KB');
      
      // Show sample cities
      console.log('\n🏙️  Sample cities:');
      cities.slice(0, 5).forEach(city => {
        console.log(`   ${city.city}, ${city.state} (Pop: ${city.population})`);
      });
      
      console.log('\n📝 To use this file in generate-search-queries-maps.js:');
      console.log('   1. Uncomment line 78: const cities = require(\'./us-cities-1000-complete.js\');');
      console.log('   2. Comment out the current cities array (lines 81-184)');
      
    } catch (error) {
      console.error('❌ Error parsing JSON:', error.message);
    }
  });

}).on('error', (error) => {
  console.error('❌ Error downloading data:', error.message);
});
