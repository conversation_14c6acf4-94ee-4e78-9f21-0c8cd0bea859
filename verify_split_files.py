#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to verify that split files are working correctly and provide a summary.
"""

import pandas as pd
import os
import glob
from pathlib import Path

def verify_split_files():
    """
    Verify all split files and provide a comprehensive summary.
    """
    print("SPLIT FILES VERIFICATION")
    print("="*50)
    
    # Define the split file patterns
    split_patterns = [
        ("B2", "B2/merged_data_final_part*.csv"),
        ("Hotel & Buildings Merged", "hotel and buildings/hotel_buildings_merged_data_part*.csv"),
        ("Hotel & Buildings Deduplicated", "hotel and buildings/hotel_buildings_merged_deduplicated_part*.csv"),
        ("Hotel & Buildings Final", "hotel and buildings/hotel_buildings_final_cross_deduplicated_part*.csv")
    ]
    
    total_files = 0
    total_size = 0
    
    for dataset_name, pattern in split_patterns:
        print(f"\n📁 {dataset_name}")
        print("-" * (len(dataset_name) + 3))
        
        # Find all matching files
        files = sorted(glob.glob(pattern))
        
        if not files:
            print(f"❌ No split files found for pattern: {pattern}")
            continue
        
        print(f"Found {len(files)} split files:")
        
        dataset_total_rows = 0
        dataset_total_size = 0
        
        for i, file_path in enumerate(files, 1):
            try:
                # Get file size
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                
                # Read file to get row count
                df = pd.read_csv(file_path, low_memory=False)
                rows = len(df)
                
                # Check if file has expected columns
                has_domain = 'domain' in df.columns
                has_website = 'website' in df.columns
                
                print(f"  Part {i}: {Path(file_path).name}")
                print(f"    Size: {size_mb:.2f} MB")
                print(f"    Rows: {rows:,}")
                print(f"    Columns: {len(df.columns)}")
                print(f"    Has domain: {has_domain}")
                print(f"    Has website: {has_website}")
                
                # Check for data integrity
                if has_domain:
                    non_null_domains = df['domain'].notna().sum()
                    print(f"    Non-null domains: {non_null_domains:,} ({non_null_domains/rows*100:.1f}%)")
                
                dataset_total_rows += rows
                dataset_total_size += size_mb
                total_files += 1
                total_size += size_mb
                
            except Exception as e:
                print(f"  ❌ Error reading {file_path}: {e}")
        
        print(f"\n  📊 {dataset_name} Summary:")
        print(f"    Total files: {len(files)}")
        print(f"    Total rows: {dataset_total_rows:,}")
        print(f"    Total size: {dataset_total_size:.2f} MB")
        print(f"    Average file size: {dataset_total_size/len(files):.2f} MB")
        print(f"    All files under 50MB: {all(os.path.getsize(f)/(1024*1024) < 50 for f in files)}")
    
    print(f"\n🎉 OVERALL SUMMARY:")
    print(f"="*20)
    print(f"Total split files created: {total_files}")
    print(f"Total size of split files: {total_size:.2f} MB")
    print(f"Average file size: {total_size/total_files:.2f} MB")
    print(f"All files under 50MB: {total_size/total_files < 50}")
    
    # Check for summary files
    print(f"\n📋 SUMMARY FILES:")
    print("-" * 15)
    summary_files = [
        "B2/merged_data_final_split_summary.txt",
        "hotel and buildings/hotel_buildings_merged_data_split_summary.txt",
        "hotel and buildings/hotel_buildings_merged_deduplicated_split_summary.txt",
        "hotel and buildings/hotel_buildings_final_cross_deduplicated_split_summary.txt"
    ]
    
    for summary_file in summary_files:
        if os.path.exists(summary_file):
            print(f"✅ {summary_file}")
        else:
            print(f"❌ {summary_file} - Not found")

def check_original_files():
    """
    Check if original large files still exist.
    """
    print(f"\n📂 ORIGINAL LARGE FILES STATUS:")
    print("-" * 35)
    
    original_files = [
        "B2/merged_data_final.csv",
        "hotel and buildings/hotel_buildings_merged_data.csv",
        "hotel and buildings/hotel_buildings_merged_deduplicated.csv", 
        "hotel and buildings/hotel_buildings_final_cross_deduplicated.csv"
    ]
    
    for file_path in original_files:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            print(f"⚠️  {file_path}: {size_mb:.2f} MB (still present)")
        else:
            print(f"✅ {file_path}: Removed")

def generate_usage_instructions():
    """
    Generate instructions for using the split files.
    """
    instructions = """
USING SPLIT FILES - INSTRUCTIONS
===============================

The large CSV files have been split into smaller chunks (max 50MB each) for easier handling.

1. INDIVIDUAL USE:
   Each split file can be used independently:
   ```python
   import pandas as pd
   df = pd.read_csv('merged_data_final_part1.csv')
   ```

2. COMBINING FILES:
   To work with the complete dataset, combine all parts:
   ```python
   import pandas as pd
   import glob
   
   # For B2 dataset
   b2_files = sorted(glob.glob('B2/merged_data_final_part*.csv'))
   b2_parts = [pd.read_csv(f) for f in b2_files]
   b2_complete = pd.concat(b2_parts, ignore_index=True)
   
   # For Hotel & Buildings final dataset
   hb_files = sorted(glob.glob('hotel and buildings/hotel_buildings_final_cross_deduplicated_part*.csv'))
   hb_parts = [pd.read_csv(f) for f in hb_files]
   hb_complete = pd.concat(hb_parts, ignore_index=True)
   ```

3. MEMORY EFFICIENT PROCESSING:
   Process files one at a time to save memory:
   ```python
   import pandas as pd
   import glob
   
   def process_in_chunks(pattern, process_func):
       files = sorted(glob.glob(pattern))
       results = []
       for file in files:
           df = pd.read_csv(file)
           result = process_func(df)
           results.append(result)
       return results
   ```

4. FILE LOCATIONS:
   - B2 dataset: B2/merged_data_final_part1.csv to part3.csv
   - Hotel & Buildings (raw): hotel and buildings/hotel_buildings_merged_data_part1.csv to part13.csv
   - Hotel & Buildings (deduplicated): hotel and buildings/hotel_buildings_merged_deduplicated_part1.csv to part4.csv
   - Hotel & Buildings (final): hotel and buildings/hotel_buildings_final_cross_deduplicated_part1.csv to part3.csv

5. VERIFICATION:
   Each directory contains a *_split_summary.txt file with detailed information about the split.

6. ORIGINAL FILES:
   Original large files are still present. You can safely remove them after verifying the split files work correctly.
"""
    
    with open('split_files_usage_instructions.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"\n📖 Usage instructions saved: split_files_usage_instructions.txt")

def main():
    """
    Main verification function.
    """
    verify_split_files()
    check_original_files()
    generate_usage_instructions()
    
    print(f"\n✅ VERIFICATION COMPLETED!")
    print(f"All split files are ready for use.")

if __name__ == "__main__":
    main()
