#!/usr/bin/env python3
"""
Verify Master CSV File Integrity
================================
Quick verification script to confirm the master CSV file is complete and accurate.
"""

import pandas as pd
import os

def verify_master_csv():
    """
    Verify the master CSV file integrity and provide summary statistics.
    """
    print("MASTER CSV VERIFICATION")
    print("="*40)
    
    master_file = "master_business_database.csv"
    
    if not os.path.exists(master_file):
        print(f"❌ Master file not found: {master_file}")
        return
    
    # Check file size
    file_size = os.path.getsize(master_file) / (1024*1024)
    print(f"📁 File: {master_file}")
    print(f"📊 Size: {file_size:.2f} MB")
    
    # Load and verify data
    print(f"\n📖 Loading master CSV file...")
    try:
        # Load with low_memory=False to ensure proper data types
        df = pd.read_csv(master_file, low_memory=False)
        print(f"✅ Successfully loaded: {len(df):,} rows, {len(df.columns)} columns")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return
    
    # Basic verification
    print(f"\n🔍 BASIC VERIFICATION:")
    print(f"   Total records: {len(df):,}")
    print(f"   Total columns: {len(df.columns)}")
    print(f"   Unique domains: {df['domain'].nunique():,}")
    print(f"   Perfect deduplication: {len(df) == df['domain'].nunique()}")
    
    # Dataset verification
    print(f"\n📊 DATASET BREAKDOWN:")
    dataset_counts = df['dataset_name'].value_counts()
    expected_counts = {
        'B2_Healthcare_Medical': 393754,
        'B3_Specialized_Medical': 43189,
        'B2A_Pet_Care_Personal': 141706,
        'B4_Accounting_Banking': 92242,
        'Hotel_Buildings_Real_Estate_Travel': 467162,
        'Buildings_Churches_Construction_Religious': 108244,
        'Auto_Business_Automotive_Services': 201372,
        'Equipment_Industrial_Consumer': 105842
    }
    
    total_expected = sum(expected_counts.values())
    
    for dataset, expected in expected_counts.items():
        actual = dataset_counts.get(dataset, 0)
        status = "✅" if actual == expected else "❌"
        print(f"   {status} {dataset}: {actual:,} (expected: {expected:,})")
    
    print(f"\n   Total: {len(df):,} (expected: {total_expected:,})")
    print(f"   Match: {len(df) == total_expected}")
    
    # Industry verification
    print(f"\n🏢 INDUSTRY BREAKDOWN:")
    industry_counts = df['industry_group'].value_counts()
    for industry, count in industry_counts.items():
        percentage = (count / len(df)) * 100
        print(f"   {industry}: {count:,} ({percentage:.1f}%)")
    
    # Column verification
    print(f"\n📋 KEY COLUMNS VERIFICATION:")
    key_columns = ['dataset_name', 'industry_group', 'title', 'domain', 'info@ email']
    for col in key_columns:
        if col in df.columns:
            non_null = df[col].notna().sum()
            percentage = (non_null / len(df)) * 100
            print(f"   ✅ {col}: {non_null:,} non-null ({percentage:.1f}%)")
        else:
            print(f"   ❌ {col}: Missing")
    
    # Sample data
    print(f"\n📋 SAMPLE DATA:")
    sample = df[['dataset_name', 'industry_group', 'title', 'domain']].head(3)
    for _, row in sample.iterrows():
        print(f"   {row['dataset_name'][:20]}... | {row['industry_group'][:15]}... | {row['title'][:30]}... | {row['domain']}")
    
    # Domain quality check
    print(f"\n🔍 DOMAIN QUALITY CHECK:")
    if 'domain' in df.columns:
        total_domains = df['domain'].notna().sum()
        # Check for potential subdomains (more than one dot, excluding country domains)
        subdomain_pattern = df['domain'].str.count('\.') > 1
        potential_subdomains = subdomain_pattern.sum()
        
        print(f"   Total domains: {total_domains:,}")
        print(f"   Potential subdomains: {potential_subdomains:,}")
        print(f"   Domain quality: {((total_domains - potential_subdomains) / total_domains * 100):.1f}%")
    
    # Email verification
    print(f"\n📧 EMAIL VERIFICATION:")
    if 'info@ email' in df.columns:
        total_emails = df['info@ email'].notna().sum()
        valid_format = df['info@ email'].str.contains('@', na=False).sum()
        print(f"   Total emails: {total_emails:,}")
        print(f"   Valid format: {valid_format:,}")
        print(f"   Email quality: {(valid_format / total_emails * 100):.1f}%")
    
    print(f"\n✅ MASTER CSV VERIFICATION COMPLETED!")
    print(f"📁 File: {master_file} ({file_size:.2f} MB)")
    print(f"📊 Records: {len(df):,} unique business domains")
    print(f"🏢 Industries: {df['industry_group'].nunique()} sectors")
    print(f"📋 Datasets: {df['dataset_name'].nunique()} combined")

def main():
    """
    Main verification function.
    """
    verify_master_csv()

if __name__ == "__main__":
    main()
