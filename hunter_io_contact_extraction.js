// HUNTER.IO CONTACT EXTRACTION NODE
// Uses Hunter.io API to find verified contact information
// Add this as an HTTP Request node or Code node in your n8n workflow

console.log('=== HUNTER.IO CONTACT EXTRACTION NODE ===');

// Configuration - Replace with your actual Hunter.io API key
const HUNTER_API_KEY = 'your-hunter-api-key-here';
const HUNTER_BASE_URL = 'https://api.hunter.io/v2';

const businesses = $input.all();
console.log('Processing businesses for Hunter.io contact extraction:', businesses.length);

const results = [];

// Helper function to extract domain from URL
function extractDomain(url) {
  if (!url) return null;
  
  try {
    // Remove protocol
    let domain = url.replace(/^https?:\/\//, '');
    
    // Remove www
    domain = domain.replace(/^www\./, '');
    
    // Get just the domain part (before first slash)
    domain = domain.split('/')[0];
    
    // Remove port if present
    domain = domain.split(':')[0];
    
    return domain.toLowerCase();
  } catch (error) {
    console.error('Error extracting domain from:', url, error);
    return null;
  }
}

// Function to search for contacts using Hunter.io domain search
async function searchContactsByDomain(domain, businessName) {
  if (!domain) {
    return {
      contacts: [],
      error: 'No domain provided'
    };
  }
  
  const url = `${HUNTER_BASE_URL}/domain-search?domain=${encodeURIComponent(domain)}&api_key=${HUNTER_API_KEY}&limit=10`;
  
  try {
    console.log(`Searching Hunter.io for domain: ${domain}`);
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Hunter.io API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (data.errors) {
      throw new Error(`Hunter.io API errors: ${JSON.stringify(data.errors)}`);
    }
    
    const domainData = data.data;
    
    if (!domainData || !domainData.emails) {
      return {
        contacts: [],
        domain_info: {
          domain: domain,
          organization: domainData?.organization || businessName,
          emails_found: 0,
          webmail: domainData?.webmail || false
        }
      };
    }
    
    // Process the emails into contact format
    const contacts = domainData.emails.map(email => ({
      name: email.first_name && email.last_name ? 
            `${email.first_name} ${email.last_name}` : 
            email.value.split('@')[0], // Fallback to email username
      first_name: email.first_name,
      last_name: email.last_name,
      title: email.position || 'Contact Person',
      email: email.value,
      phone: email.phone_number || null,
      confidence: email.confidence,
      verification_status: email.verification?.result || 'unknown',
      verification_date: email.verification?.date || null,
      source: 'hunter.io',
      hunter_metadata: {
        type: email.type,
        sources: email.sources || [],
        last_seen_on: email.last_seen_on,
        still_on_page: email.still_on_page
      }
    }));
    
    // Sort by confidence score (highest first)
    contacts.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));
    
    return {
      contacts: contacts,
      domain_info: {
        domain: domain,
        organization: domainData.organization || businessName,
        emails_found: domainData.emails.length,
        webmail: domainData.webmail,
        accept_all: domainData.accept_all,
        pattern: domainData.pattern,
        country: domainData.country,
        state: domainData.state,
        city: domainData.city
      },
      api_usage: {
        requests_used: data.meta?.results || 0,
        requests_available: data.meta?.limit || 0
      }
    };
    
  } catch (error) {
    console.error(`Hunter.io search failed for domain ${domain}:`, error.message);
    
    return {
      contacts: [],
      error: error.message,
      domain: domain
    };
  }
}

// Function to verify a specific email address
async function verifyEmail(email) {
  const url = `${HUNTER_BASE_URL}/email-verifier?email=${encodeURIComponent(email)}&api_key=${HUNTER_API_KEY}`;
  
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Hunter.io verification error: ${response.status}`);
    }
    
    const data = await response.json();
    
    return {
      email: email,
      result: data.data?.result || 'unknown',
      score: data.data?.score || 0,
      regexp: data.data?.regexp || false,
      gibberish: data.data?.gibberish || false,
      disposable: data.data?.disposable || false,
      webmail: data.data?.webmail || false,
      mx_records: data.data?.mx_records || false,
      smtp_server: data.data?.smtp_server || false,
      smtp_check: data.data?.smtp_check || false,
      accept_all: data.data?.accept_all || false,
      block: data.data?.block || false
    };
    
  } catch (error) {
    console.error(`Email verification failed for ${email}:`, error.message);
    return {
      email: email,
      result: 'error',
      error: error.message
    };
  }
}

// Process each business
for (let i = 0; i < businesses.length; i++) {
  const business = businesses[i].json;
  const businessName = business.business_name || 'Unknown Business';
  const website = business.website || business.url;
  
  console.log(`Processing ${i + 1}/${businesses.length}: ${businessName}`);
  
  const domain = extractDomain(website);
  
  if (!domain) {
    console.log(`No valid domain found for ${businessName} (website: ${website})`);
    
    results.push({
      ...business,
      hunter_contacts: [],
      hunter_metadata: {
        error: 'No valid domain found',
        website_provided: website,
        extraction_date: new Date().toISOString()
      }
    });
    
    continue;
  }
  
  try {
    // Search for contacts by domain
    const searchResult = await searchContactsByDomain(domain, businessName);
    
    console.log(`Found ${searchResult.contacts.length} contacts for ${businessName} (${domain})`);
    
    // Log contact details
    searchResult.contacts.forEach((contact, idx) => {
      console.log(`  ${idx + 1}. ${contact.name} - ${contact.title} (${contact.email}) - Confidence: ${contact.confidence}%`);
    });
    
    // Optionally verify the primary email if found
    let emailVerification = null;
    if (searchResult.contacts.length > 0 && searchResult.contacts[0].email) {
      console.log(`Verifying primary email: ${searchResult.contacts[0].email}`);
      emailVerification = await verifyEmail(searchResult.contacts[0].email);
    }
    
    // Enrich the business data
    const enrichedBusiness = {
      ...business,
      hunter_contacts: searchResult.contacts,
      primary_contact: searchResult.contacts[0] || null,
      hunter_domain_info: searchResult.domain_info,
      hunter_metadata: {
        domain_searched: domain,
        extraction_date: new Date().toISOString(),
        total_contacts_found: searchResult.contacts.length,
        error: searchResult.error || null,
        api_usage: searchResult.api_usage,
        primary_email_verification: emailVerification
      }
    };
    
    results.push(enrichedBusiness);
    
    // Add delay to respect rate limits (Hunter.io allows 100 requests/minute for free plan)
    if (i < businesses.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
    }
    
  } catch (error) {
    console.error(`Failed to process ${businessName}:`, error.message);
    
    results.push({
      ...business,
      hunter_contacts: [],
      primary_contact: null,
      hunter_metadata: {
        domain_searched: domain,
        error: error.message,
        extraction_date: new Date().toISOString(),
        total_contacts_found: 0
      }
    });
  }
}

console.log('=== HUNTER.IO EXTRACTION SUMMARY ===');
console.log('Total businesses processed:', results.length);
console.log('Businesses with Hunter contacts:', results.filter(b => b.hunter_contacts && b.hunter_contacts.length > 0).length);
console.log('Total Hunter contacts found:', results.reduce((sum, b) => sum + (b.hunter_contacts?.length || 0), 0));
console.log('Businesses with errors:', results.filter(b => b.hunter_metadata?.error).length);

// Calculate average confidence score
const allContacts = results.flatMap(b => b.hunter_contacts || []);
const avgConfidence = allContacts.length > 0 ? 
  allContacts.reduce((sum, c) => sum + (c.confidence || 0), 0) / allContacts.length : 0;
console.log('Average confidence score:', avgConfidence.toFixed(1) + '%');

return results.map(business => ({ json: business }));
