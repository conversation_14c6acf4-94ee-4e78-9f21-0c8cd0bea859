#!/usr/bin/env python3
"""
Cross-deduplicate B3 dataset against B2 dataset by removing domains that already exist in B2.
This ensures no duplicate domains exist across both B2 and B3 datasets.
"""

import pandas as pd
import os
from pathlib import Path

def cross_deduplicate_b3_vs_b2():
    """
    Remove domains from B3 that already exist in B2 dataset.
    """
    print("CROSS-DEDUPLICATION: B3 vs B2 DATASETS")
    print("="*50)
    
    # File paths
    b2_file = "../B2/merged_data_final.csv"
    b3_file = "b3_merged_deduplicated.csv"
    output_file = "b3_final_cross_deduplicated.csv"
    
    # Check if files exist
    if not os.path.exists(b2_file):
        print(f"❌ ERROR: B2 file not found: {b2_file}")
        return
    
    if not os.path.exists(b3_file):
        print(f"❌ ERROR: B3 file not found: {b3_file}")
        return
    
    print(f"📁 Loading B2 dataset: {b2_file}")
    try:
        b2_df = pd.read_csv(b2_file, low_memory=False)
        print(f"   ✅ B2 loaded: {len(b2_df):,} rows, {len(b2_df.columns)} columns")
        
        # Get B2 domains
        if 'domain' not in b2_df.columns:
            print(f"❌ ERROR: 'domain' column not found in B2 dataset")
            return
        
        b2_domains = set(b2_df['domain'].dropna().str.strip())
        print(f"   📊 B2 unique domains: {len(b2_domains):,}")
        
    except Exception as e:
        print(f"❌ ERROR loading B2 file: {e}")
        return
    
    print(f"\n📁 Loading B3 dataset: {b3_file}")
    try:
        b3_df = pd.read_csv(b3_file, low_memory=False)
        print(f"   ✅ B3 loaded: {len(b3_df):,} rows, {len(b3_df.columns)} columns")
        
        # Get B3 domains
        if 'domain' not in b3_df.columns:
            print(f"❌ ERROR: 'domain' column not found in B3 dataset")
            return
        
        b3_domains = set(b3_df['domain'].dropna().str.strip())
        print(f"   📊 B3 unique domains: {len(b3_domains):,}")
        
    except Exception as e:
        print(f"❌ ERROR loading B3 file: {e}")
        return
    
    # Find overlapping domains
    print(f"\n🔍 ANALYZING DOMAIN OVERLAP")
    print("-" * 30)
    
    overlapping_domains = b2_domains.intersection(b3_domains)
    print(f"Overlapping domains found: {len(overlapping_domains):,}")
    
    if len(overlapping_domains) == 0:
        print("✅ No overlapping domains found! B3 dataset is already unique vs B2.")
        # Still save a copy for consistency
        b3_df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"💾 Saved copy as: {output_file}")
        return b3_df
    
    # Show some examples of overlapping domains
    print(f"\nExamples of overlapping domains:")
    for i, domain in enumerate(list(overlapping_domains)[:10]):
        print(f"  {i+1}. {domain}")
    if len(overlapping_domains) > 10:
        print(f"  ... and {len(overlapping_domains) - 10} more")
    
    # Remove overlapping domains from B3
    print(f"\n🗑️  REMOVING OVERLAPPING DOMAINS FROM B3")
    print("-" * 40)
    
    original_b3_count = len(b3_df)
    
    # Create mask for rows to keep (domains NOT in B2)
    mask_keep = ~b3_df['domain'].isin(b2_domains)
    b3_filtered = b3_df[mask_keep].copy()
    
    removed_count = original_b3_count - len(b3_filtered)
    
    print(f"Original B3 rows: {original_b3_count:,}")
    print(f"Rows removed: {removed_count:,}")
    print(f"Remaining rows: {len(b3_filtered):,}")
    print(f"Removal rate: {(removed_count / original_b3_count) * 100:.2f}%")
    print(f"Retention rate: {(len(b3_filtered) / original_b3_count) * 100:.2f}%")
    
    # Verify no overlaps remain
    remaining_b3_domains = set(b3_filtered['domain'].dropna().str.strip())
    final_overlap = b2_domains.intersection(remaining_b3_domains)
    
    print(f"\n✅ VERIFICATION")
    print("-" * 15)
    print(f"Remaining B3 unique domains: {len(remaining_b3_domains):,}")
    print(f"Final overlap with B2: {len(final_overlap)}")
    print(f"Cross-deduplication successful: {len(final_overlap) == 0}")
    
    # Save the cross-deduplicated dataset
    print(f"\n💾 SAVING CROSS-DEDUPLICATED DATASET")
    print("-" * 35)
    
    b3_filtered.to_csv(output_file, index=False, encoding='utf-8')
    
    file_size = os.path.getsize(output_file) / (1024*1024)
    print(f"Saved: {output_file}")
    print(f"File size: {file_size:.2f} MB")
    
    # Generate summary report
    generate_cross_dedup_report(b2_df, b3_df, b3_filtered, overlapping_domains)
    
    print(f"\n🎉 CROSS-DEDUPLICATION COMPLETED!")
    print(f"📁 Final B3 dataset: {output_file}")
    print(f"📊 Report: b3_cross_dedup_report.txt")
    
    return b3_filtered

def generate_cross_dedup_report(b2_df, b3_original, b3_final, overlapping_domains):
    """
    Generate a comprehensive cross-deduplication report.
    """
    report_content = f"""B3 vs B2 CROSS-DEDUPLICATION REPORT
====================================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
Process: Remove B3 domains that already exist in B2

DATASET SUMMARY:
===============
B2 Dataset (merged_data_final.csv):
- Total rows: {len(b2_df):,}
- Unique domains: {b2_df['domain'].nunique():,}

B3 Dataset (b3_merged_deduplicated.csv):
- Original rows: {len(b3_original):,}
- Original unique domains: {b3_original['domain'].nunique():,}

CROSS-DEDUPLICATION RESULTS:
===========================
- Overlapping domains found: {len(overlapping_domains):,}
- B3 rows removed: {len(b3_original) - len(b3_final):,}
- B3 rows retained: {len(b3_final):,}
- Retention rate: {(len(b3_final) / len(b3_original)) * 100:.2f}%

FINAL COMBINED DATASET POTENTIAL:
================================
- B2 unique domains: {b2_df['domain'].nunique():,}
- B3 unique domains (after cross-dedup): {b3_final['domain'].nunique():,}
- Total unique domains across both: {b2_df['domain'].nunique() + b3_final['domain'].nunique():,}
- Perfect separation achieved: {len(set(b2_df['domain']).intersection(set(b3_final['domain']))) == 0}

TOP OVERLAPPING DOMAINS REMOVED:
===============================
"""
    
    # Add examples of removed domains
    if len(overlapping_domains) > 0:
        # Get counts of overlapping domains in original B3
        overlap_counts = b3_original[b3_original['domain'].isin(overlapping_domains)]['domain'].value_counts()
        
        for i, (domain, count) in enumerate(overlap_counts.head(20).items()):
            report_content += f"{i+1}. {domain}: {count} businesses removed\n"
    else:
        report_content += "No overlapping domains found.\n"
    
    # Add source file distribution for final B3
    report_content += f"\nFINAL B3 SOURCE FILE DISTRIBUTION:\n"
    report_content += f"==================================\n"
    source_stats = b3_final['source_file'].value_counts()
    for source, count in source_stats.items():
        report_content += f"- {source}: {count:,} rows\n"
    
    # Save report
    with open('b3_cross_dedup_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📊 Cross-deduplication report saved: b3_cross_dedup_report.txt")

def main():
    """
    Main function to execute cross-deduplication.
    """
    try:
        result_df = cross_deduplicate_b3_vs_b2()
        if result_df is not None:
            print(f"\n✅ SUCCESS: Cross-deduplication completed successfully!")
        else:
            print(f"\n❌ FAILED: Cross-deduplication encountered errors.")
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")

if __name__ == "__main__":
    main()
