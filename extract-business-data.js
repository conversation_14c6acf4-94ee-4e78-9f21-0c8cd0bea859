// Enhanced business data extraction from scraped HTML
const fs = require('fs');

function extractBusinessData() {
  try {
    const rawData = fs.readFileSync('logs/output.json', 'utf8');
    const scrapedData = JSON.parse(rawData);

    const extractedBusinesses = [];

    if (Array.isArray(scrapedData)) {
      scrapedData.forEach((item, index) => {
        if (item.data) {
          const business = extractDetailedBusinessInfo(item.data, item.cid);
          extractedBusinesses.push(business);
        }
      });
    } else if (scrapedData.data) {
      const business = extractDetailedBusinessInfo(scrapedData.data, scrapedData.cid);
      extractedBusinesses.push(business);
    }

    // Save extracted data
    fs.writeFileSync('extracted-businesses.json', JSON.stringify(extractedBusinesses, null, 2));

    console.log('=== BUSINESS DATA EXTRACTION COMPLETE ===\n');
    console.log(`📊 Total businesses extracted: ${extractedBusinesses.length}\n`);

    extractedBusinesses.forEach((business, index) => {
      console.log(`--- Business ${index + 1} ---`);
      console.log(`🏢 Name: ${business.business_name}`);
      console.log(`📞 Phone: ${business.phone || 'Not found'}`);
      console.log(`📧 Email: ${business.email || 'Not found'}`);
      console.log(`🌐 Website: ${business.website || 'Not found'}`);
      console.log(`📍 Address: ${business.address || 'Not found'}`);
      console.log(`📝 Description: ${business.description ? business.description.substring(0, 100) + '...' : 'Not found'}`);
      console.log(`🏷️  Business Type: ${business.business_type || 'Not specified'}`);
      console.log(`⏰ Hours: ${business.hours || 'Not found'}`);
      console.log(`💰 Price Range: ${business.price_range || 'Not specified'}`);
      console.log(`🌍 Area Served: ${business.area_served || 'Not specified'}`);
      console.log(`👥 Social Media: ${business.social_media.length > 0 ? business.social_media.join(', ') : 'None found'}`);
      console.log('');
    });

    console.log(`✅ Data saved to: extracted-businesses.json`);

  } catch (error) {
    console.error('❌ Error extracting business data:', error.message);
  }
}

function extractDetailedBusinessInfo(htmlContent, cid = null) {
  const business = {
    cid: cid,
    business_name: null,
    phone: null,
    email: null,
    website: null,
    address: null,
    description: null,
    business_type: null,
    hours: null,
    price_range: null,
    area_served: null,
    social_media: [],
    services: [],
    contact_persons: [],
    business_insights: {},
    specializations: [],
    extraction_metadata: {
      extraction_date: new Date().toISOString(),
      source: 'scraped_html',
      content_length: htmlContent.length
    }
  };

  try {
    // Extract title and business name
    const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch) {
      business.business_name = titleMatch[1].trim().split(':')[0].trim();
    }

    // Extract meta description
    const descMatch = htmlContent.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
    if (descMatch) {
      business.description = descMatch[1].trim();
    }

    // Extract phone numbers (multiple patterns)
    const phonePatterns = [
      /tel:([+\d\s\-\(\)\.]+)/gi,
      /(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})/g,
      /"telephone":\s*"([^"]+)"/gi
    ];

    for (const pattern of phonePatterns) {
      const matches = htmlContent.match(pattern);
      if (matches && matches.length > 0) {
        business.phone = matches[0].replace(/tel:/i, '').trim();
        break;
      }
    }

    // Extract email addresses
    const emailPatterns = [
      /mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi,
      /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g,
      /"email":\s*"([^"]+)"/gi
    ];

    for (const pattern of emailPatterns) {
      const matches = htmlContent.match(pattern);
      if (matches && matches.length > 0) {
        business.email = matches[0].replace(/mailto:/i, '').trim();
        break;
      }
    }

    // Extract canonical URL
    const canonicalMatch = htmlContent.match(/<link[^>]*rel=["\']canonical["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i);
    if (canonicalMatch) {
      business.website = canonicalMatch[1].trim();
    }

    // Extract JSON-LD structured data
    const jsonLdMatches = htmlContent.match(/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/gis);
    if (jsonLdMatches) {
      jsonLdMatches.forEach(match => {
        try {
          const jsonContent = match.replace(/<script[^>]*>/i, '').replace(/<\/script>/i, '');
          const jsonData = JSON.parse(jsonContent);

          if (jsonData['@graph']) {
            jsonData['@graph'].forEach(item => {
              extractFromStructuredData(item, business);
            });
          } else {
            extractFromStructuredData(jsonData, business);
          }
        } catch (e) {
          // Skip invalid JSON
        }
      });
    }

    // Extract social media links
    const socialPatterns = [
      /https?:\/\/(www\.)?(facebook|twitter|instagram|linkedin|youtube)\.com\/[^\s"'<>]+/gi
    ];

    socialPatterns.forEach(pattern => {
      const matches = htmlContent.match(pattern);
      if (matches) {
        business.social_media.push(...matches);
      }
    });

    // Remove duplicates from social media
    business.social_media = [...new Set(business.social_media)];

    // Enhanced content analysis
    const contentAnalysis = analyzeWebsiteContent(htmlContent, business.description);

    // Merge analysis results
    business.services = contentAnalysis.services;
    business.contact_persons = contentAnalysis.contact_persons;
    business.business_insights = contentAnalysis.insights;
    business.specializations = contentAnalysis.specializations;

  } catch (error) {
    console.log(`⚠️  Error extracting data for CID ${cid}:`, error.message);
  }

  return business;
}

function extractFromStructuredData(jsonData, business) {
  // Extract business name
  if (jsonData.name && !business.business_name) {
    business.business_name = jsonData.name;
  }

  // Extract phone
  if (jsonData.telephone && !business.phone) {
    business.phone = jsonData.telephone;
  }

  // Extract email
  if (jsonData.email && !business.email) {
    business.email = jsonData.email;
  }

  // Extract URL
  if (jsonData.url && !business.website) {
    business.website = jsonData.url;
  }

  // Extract description
  if (jsonData.description && !business.description) {
    business.description = jsonData.description;
  }

  // Extract business type
  if (jsonData['@type'] && !business.business_type) {
    business.business_type = jsonData['@type'];
  }

  // Extract address
  if (jsonData.address) {
    if (typeof jsonData.address === 'string') {
      business.address = jsonData.address;
    } else if (jsonData.address.streetAddress) {
      const addr = jsonData.address;
      business.address = `${addr.streetAddress || ''} ${addr.addressLocality || ''} ${addr.addressRegion || ''} ${addr.postalCode || ''}`.trim();
    }
  }

  // Extract hours
  if (jsonData.openingHoursSpecification) {
    const hours = jsonData.openingHoursSpecification;
    if (hours.opens && hours.closes) {
      business.hours = `${hours.opens} - ${hours.closes}`;
    }
  }

  // Extract price range
  if (jsonData.priceRange) {
    business.price_range = jsonData.priceRange;
  }

  // Extract area served
  if (jsonData.areaServed) {
    business.area_served = jsonData.areaServed;
  }

  // Extract social media from sameAs
  if (jsonData.sameAs && Array.isArray(jsonData.sameAs)) {
    business.social_media.push(...jsonData.sameAs);
  }
}

function analyzeWebsiteContent(htmlContent, description) {
  const analysis = {
    services: [],
    contact_persons: [],
    insights: {},
    specializations: []
  };

  // Combine HTML content and description for analysis
  const fullText = htmlContent + ' ' + (description || '');

  // Extract contact persons (names with titles)
  const namePatterns = [
    /([A-Z][a-z]+\s+[A-Z][a-z]+)[\s,]*\(?([^)]*(?:manager|director|owner|president|ceo|founder|lead|senior|principal|architect|engineer|designer|coordinator|specialist|supervisor)[^)]*)\)?/gi,
    /(?:meet|contact|speak with|call)\s+([A-Z][a-z]+\s+[A-Z][a-z]+)/gi,
    /([A-Z][a-z]+\s+[A-Z][a-z]+)[\s,]*-[\s]*([^.!?]+(?:manager|director|owner|president|ceo|founder|lead|senior|principal|architect|engineer|designer|coordinator|specialist|supervisor)[^.!?]*)/gi
  ];

  namePatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(fullText)) !== null) {
      const name = match[1].trim();
      const title = match[2] ? match[2].trim() : 'Contact Person';

      if (name.length > 5 && name.length < 50 && !name.includes('General') && !name.includes('Company')) {
        analysis.contact_persons.push({
          name: name,
          title: title,
          source: 'content_analysis'
        });
      }
    }
  });

  // Extract business insights
  const insightPatterns = {
    years_in_business: /(?:since|established|founded|serving|operating)[\s\w]*(\d{4})/gi,
    certifications: /(licensed|bonded|insured|certified|accredited|member of|BBB|better business bureau)/gi,
    project_range: /projects?\s+(?:from\s+)?\$?([\d,]+k?)\s*(?:to\s+\$?([\d,]+[km]?))?/gi,
    team_size: /(\d+)\s*(?:\+)?\s*(?:employees?|staff|team members?|professionals?|contractors?)/gi,
    service_area: /serving\s+([^.!?]+(?:county|city|state|area|region|borough|manhattan|brooklyn|queens|bronx|nyc|new york))/gi
  };

  Object.entries(insightPatterns).forEach(([key, pattern]) => {
    const matches = fullText.match(pattern);
    if (matches && matches.length > 0) {
      analysis.insights[key] = matches.map(m => m.trim()).slice(0, 3); // Limit to 3 matches
    }
  });

  // Extract specialized services (more detailed than basic keywords)
  const specializationPatterns = [
    /(?:speciali[sz]e in|expert in|focus on|known for)\s+([^.!?]+)/gi,
    /(luxury|high-end|custom|commercial|residential|historic|green|sustainable|eco-friendly)\s+(renovation|construction|remodeling|building|design)/gi,
    /([^.!?]*(?:kitchen|bathroom|basement|attic|office|retail|restaurant|hotel|apartment|condo|townhouse|mansion)[^.!?]*(?:renovation|remodeling|construction|design))/gi
  ];

  specializationPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(fullText)) !== null) {
      const specialization = match[1] ? match[1].trim() : match[0].trim();
      if (specialization.length > 10 && specialization.length < 100) {
        analysis.specializations.push(specialization);
      }
    }
  });

  // Basic service keywords (enhanced)
  const serviceKeywords = [
    'renovation', 'construction', 'remodeling', 'contractor', 'building',
    'plumbing', 'electrical', 'roofing', 'flooring', 'painting',
    'kitchen', 'bathroom', 'basement', 'commercial', 'residential',
    'design', 'architecture', 'permits', 'demolition', 'framing',
    'drywall', 'tiling', 'carpentry', 'masonry', 'hvac', 'windows',
    'doors', 'cabinets', 'countertops', 'lighting', 'landscaping'
  ];

  serviceKeywords.forEach(keyword => {
    const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
    if (regex.test(fullText)) {
      analysis.services.push(keyword);
    }
  });

  // Remove duplicates
  analysis.services = [...new Set(analysis.services)];
  analysis.specializations = [...new Set(analysis.specializations)];
  analysis.contact_persons = analysis.contact_persons.filter((person, index, self) =>
    index === self.findIndex(p => p.name === person.name)
  );

  return analysis;
}

// Run the extraction
extractBusinessData();
