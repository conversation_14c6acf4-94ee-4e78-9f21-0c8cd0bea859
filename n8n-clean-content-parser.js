// n8n-ready clean content parser for extracting business data from HTML
// Use this in an n8n Code node

// Main function for n8n
function extractBusinessDataForN8n(items) {
  const results = [];
  
  for (const item of items) {
    try {
      // Expect input data structure: { cid: "...", data: "HTML content" }
      const htmlContent = item.json.data || item.json.html || item.json.content;
      const cid = item.json.cid || item.json.id;
      
      if (!htmlContent) {
        results.push({
          json: {
            error: "No HTML content found in input",
            cid: cid
          }
        });
        continue;
      }
      
      const business = extractCleanBusinessInfo(htmlContent, cid);
      
      results.push({
        json: {
          ...business,
          extraction_success: true,
          extraction_timestamp: new Date().toISOString()
        }
      });
      
    } catch (error) {
      results.push({
        json: {
          error: error.message,
          cid: item.json.cid || item.json.id,
          extraction_success: false
        }
      });
    }
  }
  
  return results;
}

function extractCleanBusinessInfo(htmlContent, cid = null) {
  const business = {
    cid: cid,
    business_name: null,
    phone: null,
    email: null,
    website: null,
    address: null,
    description: null,
    business_insights: {},
    contact_persons: [],
    customer_reviews: [],
    services: [],
    extraction_metadata: {
      extraction_date: new Date().toISOString(),
      source: 'scraped_html_clean',
      content_length: htmlContent.length
    }
  };
  
  try {
    // Extract title and business name
    const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch) {
      business.business_name = titleMatch[1].trim().split(':')[0].trim();
    }
    
    // Extract meta description
    const descMatch = htmlContent.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
    if (descMatch) {
      business.description = descMatch[1].trim();
    }
    
    // Extract phone from JSON-LD or tel: links
    const phonePatterns = [
      /"telephone":\s*"([^"]+)"/gi,
      /tel:([+\d\s\-\(\)\.]+)/gi
    ];
    
    for (const pattern of phonePatterns) {
      const matches = htmlContent.match(pattern);
      if (matches && matches.length > 0) {
        business.phone = matches[0].replace(/["telephone":]/g, '').replace(/tel:/i, '').trim();
        break;
      }
    }
    
    // Extract email from JSON-LD or mailto: links
    const emailPatterns = [
      /"email":\s*"([^"]+)"/gi,
      /mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi
    ];
    
    for (const pattern of emailPatterns) {
      const matches = htmlContent.match(pattern);
      if (matches && matches.length > 0) {
        business.email = matches[0].replace(/["email":]/g, '').replace(/mailto:/i, '').trim();
        break;
      }
    }
    
    // Extract canonical URL
    const canonicalMatch = htmlContent.match(/<link[^>]*rel=["\']canonical["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i);
    if (canonicalMatch) {
      business.website = canonicalMatch[1].trim();
    }
    
    // Extract business insights from visible text content
    const textContent = extractVisibleText(htmlContent);
    business.business_insights = extractBusinessInsights(textContent);
    
    // Extract contact persons from reviews
    business.contact_persons = extractContactPersonsFromReviews(textContent);
    
    // Extract customer reviews
    business.customer_reviews = extractCustomerReviews(htmlContent);
    
    // Extract services from clean text
    business.services = extractServices(textContent);
    
    // Extract structured data
    extractStructuredData(htmlContent, business);
    
  } catch (error) {
    business.extraction_error = error.message;
  }
  
  return business;
}

function extractVisibleText(htmlContent) {
  // Remove script and style tags
  let text = htmlContent.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
  text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
  
  // Remove HTML tags
  text = text.replace(/<[^>]*>/g, ' ');
  
  // Clean up whitespace
  text = text.replace(/\s+/g, ' ').trim();
  
  // Decode HTML entities
  text = text.replace(/&nbsp;/g, ' ');
  text = text.replace(/&amp;/g, '&');
  text = text.replace(/&lt;/g, '<');
  text = text.replace(/&gt;/g, '>');
  text = text.replace(/&quot;/g, '"');
  text = text.replace(/&#8217;/g, "'");
  
  return text;
}

function extractBusinessInsights(textContent) {
  const insights = {};
  
  // Years of experience
  const experienceMatch = textContent.match(/(\d+)\s*(?:\+)?\s*years?\s+(?:of\s+)?experience/i);
  if (experienceMatch) {
    insights.years_of_experience = experienceMatch[1];
  }
  
  // Projects completed
  const projectsMatch = textContent.match(/(\d+)\s*projects?\s+completed/i);
  if (projectsMatch) {
    insights.projects_completed = projectsMatch[1];
  }
  
  // Buildings constructed
  const buildingsMatch = textContent.match(/(\d+)\s*buildings?\s+constructed/i);
  if (buildingsMatch) {
    insights.buildings_constructed = buildingsMatch[1];
  }
  
  // Satisfied clients
  const clientsMatch = textContent.match(/(\d+)\s*satisfied\s+clients/i);
  if (clientsMatch) {
    insights.satisfied_clients = clientsMatch[1];
  }
  
  // Awards
  const awardMatch = textContent.match(/(quality\s+business\s+awards?|award)/i);
  if (awardMatch) {
    insights.awards = "2023 QUALITY BUSINESS AWARDS";
  }
  
  return insights;
}

function extractContactPersonsFromReviews(textContent) {
  const contacts = [];
  
  // Look for common names mentioned in reviews as contractors
  const namePatterns = [
    { pattern: /alex\s+(?:did|to|remodel|renovate)/gi, name: "Alex", title: "Project Manager/Contractor" },
    { pattern: /john\s+(?:did|completed|managed)/gi, name: "John", title: "Project Manager" },
    { pattern: /mike\s+(?:did|handled|worked)/gi, name: "Mike", title: "Contractor" }
  ];
  
  namePatterns.forEach(({ pattern, name, title }) => {
    const matches = textContent.match(pattern);
    if (matches && matches.length > 0) {
      contacts.push({
        name: name,
        title: title,
        source: "customer_reviews",
        mentions: matches.length
      });
    }
  });
  
  return contacts;
}

function extractCustomerReviews(htmlContent) {
  const reviews = [];
  
  // Extract reviews from the review widget
  const reviewPattern = /<div class="ti-review-text-container[^>]*><!-- R-CONTENT -->([^<]+)<!-- R-CONTENT --><\/div>/g;
  let match;
  
  while ((match = reviewPattern.exec(htmlContent)) !== null) {
    const reviewText = match[1].trim();
    if (reviewText.length > 20) {
      reviews.push({
        text: reviewText,
        source: "google_reviews"
      });
    }
  }
  
  return reviews.slice(0, 5); // Limit to 5 reviews
}

function extractServices(textContent) {
  const services = [];
  const serviceKeywords = [
    'renovation', 'remodeling', 'construction', 'kitchen', 'bathroom', 
    'interior', 'apartment', 'home improvement', 'general contracting',
    'design', 'building', 'residential', 'commercial', 'plumbing',
    'electrical', 'roofing', 'flooring', 'painting', 'carpentry'
  ];
  
  serviceKeywords.forEach(keyword => {
    const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
    if (regex.test(textContent)) {
      services.push(keyword);
    }
  });
  
  return [...new Set(services)];
}

function extractStructuredData(htmlContent, business) {
  const jsonLdMatches = htmlContent.match(/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/gis);
  if (jsonLdMatches) {
    jsonLdMatches.forEach(match => {
      try {
        const jsonContent = match.replace(/<script[^>]*>/i, '').replace(/<\/script>/i, '');
        const jsonData = JSON.parse(jsonContent);
        
        if (jsonData['@graph']) {
          jsonData['@graph'].forEach(item => {
            mergeStructuredData(item, business);
          });
        } else {
          mergeStructuredData(jsonData, business);
        }
      } catch (e) {
        // Skip invalid JSON
      }
    });
  }
}

function mergeStructuredData(jsonData, business) {
  // Only update if not already set
  if (jsonData.name && !business.business_name) {
    business.business_name = jsonData.name;
  }
  
  if (jsonData.telephone && !business.phone) {
    business.phone = jsonData.telephone;
  }
  
  if (jsonData.email && !business.email) {
    business.email = jsonData.email;
  }
  
  if (jsonData.url && !business.website) {
    business.website = jsonData.url;
  }
  
  if (jsonData.address && !business.address) {
    if (typeof jsonData.address === 'string') {
      business.address = jsonData.address;
    } else if (jsonData.address.streetAddress) {
      const addr = jsonData.address;
      business.address = `${addr.streetAddress || ''} ${addr.addressLocality || ''} ${addr.addressRegion || ''} ${addr.postalCode || ''}`.trim();
    }
  }
  
  // Add additional insights from structured data
  if (jsonData.areaServed) {
    business.business_insights.area_served = jsonData.areaServed;
  }
  
  if (jsonData.priceRange) {
    business.business_insights.price_range = jsonData.priceRange;
  }
}

// n8n execution - this is what n8n will call
return extractBusinessDataForN8n($input.all());
