#!/usr/bin/env python3
"""
Script to split large CSV files into smaller chunks (max 50MB each).
"""

import pandas as pd
import os
import math
from pathlib import Path

def get_file_size_mb(file_path):
    """Get file size in MB."""
    return os.path.getsize(file_path) / (1024 * 1024)

def split_csv_file(file_path, max_size_mb=50):
    """
    Split a CSV file into smaller chunks based on file size.
    """
    print(f"\n📁 Processing: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    current_size = get_file_size_mb(file_path)
    print(f"Current size: {current_size:.2f} MB")
    
    if current_size <= max_size_mb:
        print(f"✅ File is already under {max_size_mb}MB - no splitting needed")
        return
    
    # Calculate number of chunks needed
    num_chunks = math.ceil(current_size / max_size_mb)
    print(f"Will split into {num_chunks} chunks")
    
    # Read the file
    print("📖 Reading file...")
    try:
        df = pd.read_csv(file_path, low_memory=False)
        total_rows = len(df)
        print(f"Total rows: {total_rows:,}")
        
        # Calculate rows per chunk
        rows_per_chunk = math.ceil(total_rows / num_chunks)
        print(f"Rows per chunk: {rows_per_chunk:,}")
        
        # Create base filename without extension
        base_path = Path(file_path)
        base_name = base_path.stem
        directory = base_path.parent
        
        # Split and save chunks
        for i in range(num_chunks):
            start_idx = i * rows_per_chunk
            end_idx = min((i + 1) * rows_per_chunk, total_rows)
            
            chunk_df = df.iloc[start_idx:end_idx].copy()
            chunk_filename = f"{base_name}_part{i+1}.csv"
            chunk_path = directory / chunk_filename
            
            print(f"💾 Saving chunk {i+1}/{num_chunks}: {chunk_filename}")
            print(f"   Rows: {len(chunk_df):,} (from {start_idx:,} to {end_idx-1:,})")
            
            chunk_df.to_csv(chunk_path, index=False, encoding='utf-8')
            
            chunk_size = get_file_size_mb(chunk_path)
            print(f"   Size: {chunk_size:.2f} MB")
        
        # Create a summary file
        summary_filename = f"{base_name}_split_summary.txt"
        summary_path = directory / summary_filename
        
        summary_content = f"""FILE SPLIT SUMMARY
==================

Original file: {file_path}
Original size: {current_size:.2f} MB
Original rows: {total_rows:,}

Split into {num_chunks} parts:
"""
        
        for i in range(num_chunks):
            chunk_filename = f"{base_name}_part{i+1}.csv"
            chunk_path = directory / chunk_filename
            if chunk_path.exists():
                chunk_size = get_file_size_mb(chunk_path)
                start_idx = i * rows_per_chunk
                end_idx = min((i + 1) * rows_per_chunk, total_rows)
                summary_content += f"- {chunk_filename}: {chunk_size:.2f} MB ({end_idx - start_idx:,} rows)\n"
        
        summary_content += f"\nTo recombine files:\n"
        summary_content += f"```python\n"
        summary_content += f"import pandas as pd\n"
        summary_content += f"parts = []\n"
        for i in range(num_chunks):
            chunk_filename = f"{base_name}_part{i+1}.csv"
            summary_content += f"parts.append(pd.read_csv('{chunk_filename}'))\n"
        summary_content += f"combined = pd.concat(parts, ignore_index=True)\n"
        summary_content += f"combined.to_csv('{base_name}_recombined.csv', index=False)\n"
        summary_content += f"```\n"
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        print(f"📋 Summary saved: {summary_filename}")
        print(f"✅ File split completed successfully!")
        
        # Optionally, ask if user wants to remove original file
        print(f"\n⚠️  Original file ({current_size:.2f} MB) is still present.")
        print(f"   You may want to remove it after verifying the split files work correctly.")
        
    except Exception as e:
        print(f"❌ Error processing file: {e}")

def main():
    """
    Main function to split large files.
    """
    print("LARGE FILE SPLITTER")
    print("="*50)
    print("Splitting files larger than 50MB into smaller chunks")
    
    # List of files to check and potentially split
    files_to_check = [
        "B2/merged_data_final.csv",
        "hotel and buildings/hotel_buildings_merged_data.csv", 
        "hotel and buildings/hotel_buildings_merged_deduplicated.csv",
        "hotel and buildings/hotel_buildings_final_cross_deduplicated.csv"
    ]
    
    print(f"\nChecking {len(files_to_check)} files...")
    
    large_files = []
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size_mb = get_file_size_mb(file_path)
            if size_mb > 50:
                large_files.append((file_path, size_mb))
                print(f"📋 {file_path}: {size_mb:.2f} MB (needs splitting)")
            else:
                print(f"✅ {file_path}: {size_mb:.2f} MB (OK)")
        else:
            print(f"❌ {file_path}: Not found")
    
    if not large_files:
        print(f"\n🎉 All files are under 50MB - no splitting needed!")
        return
    
    print(f"\n🔧 Found {len(large_files)} files that need splitting:")
    for file_path, size_mb in large_files:
        print(f"   - {file_path}: {size_mb:.2f} MB")
    
    # Split each large file
    for file_path, size_mb in large_files:
        split_csv_file(file_path, max_size_mb=50)
    
    print(f"\n🎉 ALL FILES PROCESSED!")
    print(f"📁 Check the respective directories for split files and summaries.")

if __name__ == "__main__":
    main()
