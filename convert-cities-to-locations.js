// Script to convert cities-1000-data.json to simple locations array format
const fs = require('fs');

try {
  // Read the cities data
  const citiesData = JSON.parse(fs.readFileSync('cities-1000-data.json', 'utf8'));
  
  // Convert to simple "City State" format
  const locations = citiesData.map(city => {
    // Handle special cases for state abbreviations
    const stateAbbreviations = {
      'Alabama': 'AL',
      'Alaska': 'AK',
      'Arizona': 'AZ',
      'Arkansas': 'AR',
      'California': 'CA',
      'Colorado': 'CO',
      'Connecticut': 'CT',
      'Delaware': 'DE',
      'District of Columbia': 'DC',
      'Florida': 'FL',
      'Georgia': 'GA',
      'Hawaii': 'HI',
      'Idaho': 'ID',
      'Illinois': 'IL',
      'Indiana': 'IN',
      'Iowa': 'IA',
      'Kansas': 'KS',
      'Kentucky': 'KY',
      'Louisiana': 'LA',
      'Maine': 'ME',
      'Maryland': 'MD',
      'Massachusetts': 'MA',
      'Michigan': 'MI',
      'Minnesota': 'MN',
      'Mississippi': 'MS',
      'Missouri': 'MO',
      'Montana': 'MT',
      'Nebraska': 'NE',
      'Nevada': 'NV',
      'New Hampshire': 'NH',
      'New Jersey': 'NJ',
      'New Mexico': 'NM',
      'New York': 'NY',
      'North Carolina': 'NC',
      'North Dakota': 'ND',
      'Ohio': 'OH',
      'Oklahoma': 'OK',
      'Oregon': 'OR',
      'Pennsylvania': 'PA',
      'Rhode Island': 'RI',
      'South Carolina': 'SC',
      'South Dakota': 'SD',
      'Tennessee': 'TN',
      'Texas': 'TX',
      'Utah': 'UT',
      'Vermont': 'VT',
      'Virginia': 'VA',
      'Washington': 'WA',
      'West Virginia': 'WV',
      'Wisconsin': 'WI',
      'Wyoming': 'WY'
    };
    
    const stateAbbr = stateAbbreviations[city.state] || city.state;
    return `${city.city} ${stateAbbr}`;
  });
  
  // Format as JavaScript array
  const output = `const locations = [\n  '${locations.join("',\n  '")}'\n];`;
  
  // Write to file
  fs.writeFileSync('locations-1000.js', output);
  
  console.log(`✅ Successfully converted ${locations.length} cities to locations format`);
  console.log('📁 Output saved to: locations-1000.js');
  console.log('\n📊 Sample locations:');
  locations.slice(0, 10).forEach((location, index) => {
    console.log(`   ${index + 1}. ${location}`);
  });
  
} catch (error) {
  console.error('❌ Error:', error.message);
}
