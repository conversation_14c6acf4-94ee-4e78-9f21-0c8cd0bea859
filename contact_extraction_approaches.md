# Contact Person Extraction Approaches for n8n Workflow

## 🎯 Current Status
Your workflow already extracts:
- ✅ Phone numbers
- ✅ Email addresses  
- ✅ Business names
- ✅ Addresses

**Goal**: Add contact person names and titles

---

## 🚀 Approach 1: Enhanced Pattern Matching (Recommended)

**File**: `enhanced_contact_extraction_node.js`

**What it does**:
- Uses advanced regex patterns to find names and titles
- Looks in specific sections (Contact, About Us, Team)
- Extracts from email signatures
- Validates names to avoid false positives

**Pros**:
- No additional API costs
- Works with existing scraped content
- Fast processing
- Good accuracy for structured websites

**Cons**:
- May miss some contacts on unstructured sites
- Requires fine-tuning for different website layouts

**Implementation**: Add as new Code node after website scraping

---

## 🤖 Approach 2: AI-Powered Extraction (Most Accurate)

**What it does**:
- Uses OpenAI/Claude API to intelligently extract contact info
- Understands context and relationships
- Can handle unstructured content

**Example n8n node**:
```javascript
// AI Contact Extraction Node
const openaiApiKey = 'your-openai-api-key';
const businesses = $input.all();

const results = [];

for (const business of businesses) {
  const content = business.json.content || business.json.data || '';
  
  // Truncate content to fit API limits
  const truncatedContent = content.substring(0, 8000);
  
  const prompt = `
Extract contact person information from this business website content.
Return a JSON array of contacts with name, title, email, and phone.
Only include real people, not generic contact info.

Website content:
${truncatedContent}

Return format:
[{"name": "John Smith", "title": "Owner", "email": "<EMAIL>", "phone": "(*************"}]
`;

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_tokens: 500
      })
    });
    
    const data = await response.json();
    const extractedContacts = JSON.parse(data.choices[0].message.content);
    
    results.push({
      ...business.json,
      ai_extracted_contacts: extractedContacts,
      primary_contact: extractedContacts[0] || null
    });
    
  } catch (error) {
    console.log('AI extraction failed:', error);
    results.push({
      ...business.json,
      ai_extracted_contacts: [],
      extraction_error: error.message
    });
  }
}

return results.map(business => ({ json: business }));
```

**Pros**:
- Highest accuracy
- Handles any website structure
- Understands context and relationships
- Can extract additional details

**Cons**:
- Costs money per API call
- Slower processing
- Requires API key management
- Rate limits

---

## 🔍 Approach 3: Structured Data Extraction

**What it does**:
- Looks for JSON-LD, microdata, and schema.org markup
- Extracts from meta tags and structured data

**Example n8n node**:
```javascript
// Structured Data Contact Extraction
const businesses = $input.all();
const results = [];

function extractStructuredData(content) {
  const contacts = [];
  
  // Extract JSON-LD data
  const jsonLdRegex = /<script[^>]*type=["']application\/ld\+json["'][^>]*>([\s\S]*?)<\/script>/gi;
  let match;
  
  while ((match = jsonLdRegex.exec(content)) !== null) {
    try {
      const jsonData = JSON.parse(match[1]);
      
      // Look for Person or Organization data
      if (jsonData['@type'] === 'Person' || jsonData['@type'] === 'Organization') {
        const contact = {
          name: jsonData.name,
          title: jsonData.jobTitle || jsonData.description,
          email: jsonData.email,
          phone: jsonData.telephone,
          source: 'json-ld'
        };
        
        if (contact.name) contacts.push(contact);
      }
      
      // Look for employees array
      if (jsonData.employee && Array.isArray(jsonData.employee)) {
        jsonData.employee.forEach(emp => {
          contacts.push({
            name: emp.name,
            title: emp.jobTitle,
            email: emp.email,
            phone: emp.telephone,
            source: 'json-ld-employee'
          });
        });
      }
      
    } catch (e) {
      // Invalid JSON, skip
    }
  }
  
  // Extract from meta tags
  const metaRegex = /<meta[^>]*name=["'](author|contact|person)["'][^>]*content=["']([^"']+)["'][^>]*>/gi;
  while ((match = metaRegex.exec(content)) !== null) {
    contacts.push({
      name: match[2],
      title: 'Contact Person',
      source: 'meta-tag'
    });
  }
  
  return contacts;
}

businesses.forEach(business => {
  const content = business.json.content || business.json.data || '';
  const structuredContacts = extractStructuredData(content);
  
  results.push({
    ...business.json,
    structured_contacts: structuredContacts,
    primary_contact: structuredContacts[0] || null
  });
});

return results.map(business => ({ json: business }));
```

**Pros**:
- Very accurate when available
- Fast processing
- No API costs
- Standardized format

**Cons**:
- Only works if websites have structured data
- Many small business sites don't use it
- Limited coverage

---

## 🌐 Approach 4: Social Media Integration

**What it does**:
- Scrapes LinkedIn company pages
- Extracts from Facebook business pages
- Gets contact info from social profiles

**Example workflow addition**:
```javascript
// Social Media Contact Extraction
const businesses = $input.all();
const results = [];

async function getLinkedInContacts(companyName) {
  // This would require LinkedIn API or web scraping
  // Example structure:
  const linkedinUrl = `https://www.linkedin.com/company/${companyName.toLowerCase().replace(/\s+/g, '-')}`;
  
  // Scrape LinkedIn page (requires proper setup)
  // Return contacts found on LinkedIn
  return [];
}

for (const business of businesses) {
  const businessName = business.json.business_name;
  
  try {
    const linkedinContacts = await getLinkedInContacts(businessName);
    
    results.push({
      ...business.json,
      linkedin_contacts: linkedinContacts,
      social_extraction_date: new Date().toISOString()
    });
    
  } catch (error) {
    results.push({
      ...business.json,
      linkedin_contacts: [],
      social_extraction_error: error.message
    });
  }
}

return results.map(business => ({ json: business }));
```

**Pros**:
- Rich contact information
- Professional titles and roles
- Additional context about people

**Cons**:
- Complex to implement
- Rate limiting issues
- May violate terms of service
- Requires additional APIs

---

## 📞 Approach 5: Contact Finding APIs

**What it does**:
- Uses services like Hunter.io, Apollo.io, ZoomInfo
- Provides verified contact information
- Often includes phone numbers and titles

**Example with Hunter.io**:
```javascript
// Hunter.io Contact Finding
const hunterApiKey = 'your-hunter-api-key';
const businesses = $input.all();
const results = [];

for (const business of businesses) {
  const domain = business.json.website?.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
  
  if (!domain) {
    results.push({
      ...business.json,
      hunter_contacts: [],
      hunter_error: 'No domain found'
    });
    continue;
  }
  
  try {
    const response = await fetch(`https://api.hunter.io/v2/domain-search?domain=${domain}&api_key=${hunterApiKey}`);
    const data = await response.json();
    
    const contacts = data.data?.emails?.map(email => ({
      name: `${email.first_name} ${email.last_name}`,
      title: email.position,
      email: email.value,
      confidence: email.confidence,
      source: 'hunter.io'
    })) || [];
    
    results.push({
      ...business.json,
      hunter_contacts: contacts,
      primary_contact: contacts[0] || null
    });
    
  } catch (error) {
    results.push({
      ...business.json,
      hunter_contacts: [],
      hunter_error: error.message
    });
  }
}

return results.map(business => ({ json: business }));
```

**Pros**:
- High accuracy and verification
- Professional contact databases
- Often includes direct phone numbers
- Good for B2B contacts

**Cons**:
- Costs money per lookup
- May not have small local businesses
- Rate limits
- Requires API subscriptions

---

## 🎯 Recommended Implementation Strategy

1. **Start with Approach 1** (Enhanced Pattern Matching) - Free and effective
2. **Add Approach 3** (Structured Data) - Quick wins for modern sites  
3. **Consider Approach 2** (AI) for higher accuracy if budget allows
4. **Use Approach 5** (APIs) for critical contacts or verification

Would you like me to help implement any of these approaches?
