{"name": "My workflow", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [200, -120], "id": "49363d2b-44b1-46e1-8165-a778b30b2a39", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"batchSize": "=1", "options": {}}, "id": "9c1a3d2a-c994-43dd-971d-1341b7f453fe", "name": "Loop Search Queries", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [560, -120]}, {"parameters": {"jsCode": "return [\n  {\n    \"city\": \"New York\",\n    \"growth_from_2000_to_2013\": \"4.8%\",\n    \"latitude\": 40.7127837,\n    \"longitude\": -74.0059413,\n    \"population\": \"8405837\",\n    \"rank\": \"1\",\n    \"state\": \"New York\"\n  },\n  {\n    \"city\": \"Los Angeles\",\n    \"growth_from_2000_to_2013\": \"4.8%\",\n    \"latitude\": 34.0522342,\n    \"longitude\": -118.2436849,\n    \"population\": \"3884307\",\n    \"rank\": \"2\",\n    \"state\": \"California\"\n  },\n  {\n    \"city\": \"Chicago\",\n    \"growth_from_2000_to_2013\": \"-6.1%\",\n    \"latitude\": 41.8781136,\n    \"longitude\": -87.6297982,\n    \"population\": \"2718782\",\n    \"rank\": \"3\",\n    \"state\": \"Illinois\"\n  },\n  {\n    \"city\": \"Houston\",\n    \"growth_from_2000_to_2013\": \"11.0%\",\n    \"latitude\": 29.7604267,\n    \"longitude\": -95.3698028,\n    \"population\": \"2195914\",\n    \"rank\": \"4\",\n    \"state\": \"Texas\"\n  },\n  {\n    \"city\": \"Philadelphia\",\n    \"growth_from_2000_to_2013\": \"2.6%\",\n    \"latitude\": 39.9525839,\n    \"longitude\": -75.1652215,\n    \"population\": \"1553165\",\n    \"rank\": \"5\",\n    \"state\": \"Pennsylvania\"\n  },\n  {\n    \"city\": \"Phoenix\",\n    \"growth_from_2000_to_2013\": \"14.0%\",\n    \"latitude\": 33.4483771,\n    \"longitude\": -112.0740373,\n    \"population\": \"1513367\",\n    \"rank\": \"6\",\n    \"state\": \"Arizona\"\n  },\n  {\n    \"city\": \"San Antonio\",\n    \"growth_from_2000_to_2013\": \"21.0%\",\n    \"latitude\": 29.4241219,\n    \"longitude\": -98.49362819999999,\n    \"population\": \"1409019\",\n    \"rank\": \"7\",\n    \"state\": \"Texas\"\n  },\n  {\n    \"city\": \"San Diego\",\n    \"growth_from_2000_to_2013\": \"10.5%\",\n    \"latitude\": 32.715738,\n    \"longitude\": -117.1610838,\n    \"population\": \"1355896\",\n    \"rank\": \"8\",\n    \"state\": \"California\"\n  },\n  {\n    \"city\": \"Dallas\",\n    \"growth_from_2000_to_2013\": \"5.6%\",\n    \"latitude\": 32.7766642,\n    \"longitude\": -96.79698789999999,\n    \"population\": \"1257676\",\n    \"rank\": \"9\",\n    \"state\": \"Texas\"\n  },\n  {\n    \"city\": \"San Jose\",\n    \"growth_from_2000_to_2013\": \"10.5%\",\n    \"latitude\": 37.3382082,\n    \"longitude\": -121.8863286,\n    \"population\": \"998537\",\n    \"rank\": \"10\",\n    \"state\": \"California\"\n  },\n]"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [380, -120], "id": "ff8ba655-9fde-4d18-80c9-38002dd4a88d", "name": "Top Cities"}, {"parameters": {"jsCode": "console.log($input.all());\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, -40], "id": "b39de756-9187-4c79-9186-a5c066c8a71e", "name": "Code1"}, {"parameters": {"fieldToSplitOut": "places", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1240, -40], "id": "28cf4b27-f041-4eb1-9525-14e8ff44a554", "name": "Split Out1"}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "cid", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1440, -40], "id": "d54b16cc-1214-4f0c-876b-d1c313c4561f", "name": "Remove Duplicates"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f935205f-1b13-43e2-89cb-e5508f9c7d0d", "leftValue": "={{ $json.website }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1880, -40], "id": "31a4a010-3bf0-4ec3-9d1b-726f11ce5871", "name": "If"}, {"parameters": {"url": "=https://api.scraperapi.com", "sendQuery": true, "queryParameters": {"parameters": [{"name": "api_key", "value": "55cfdc4fb10777b228a2a10224d86a9a"}, {"name": "url", "value": "={{ $json.website }}"}, {"name": "autoparse", "value": "true"}]}, "options": {"allowUnauthorizedCerts": true, "response": {"response": {"fullResponse": true, "neverError": true, "responseFormat": "text"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2380, -40], "id": "0f822bdc-d801-44a5-89aa-f4d169ffd0d4", "name": "ScraperAPI"}, {"parameters": {"method": "POST", "url": "https://google.serper.dev/places", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-API-KEY", "value": "c8f2c4b3b683e919d68aa4d9631ff88c16eb7d4e"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "q", "value": "=contractor near {{ $json.city }} {{ $json.state }}"}, {"name": "gl", "value": "us"}, {"name": "location", "value": "United States"}, {"name": "page", "value": "={{ $json.page || 1 }}"}]}, "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "page", "value": "={{ $request.body.page++ }}"}]}, "paginationCompleteWhen": "other", "completeExpression": "={{ $response.body.places.isEmpty() }}"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1020, -40], "id": "e7a8b096-499d-44eb-bc04-1bb92c367081", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.1, "position": [2400, -300], "id": "4dd7866c-c699-43ba-9fe8-b10448e51e56", "name": "Information Extractor"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2160, -40], "id": "e17a7ce4-4a21-4e90-a068-d9e963d6868a", "name": "Loop Items with Website"}, {"parameters": {"assignments": {"assignments": [{"id": "95528c7e-7c7b-48a2-a6ff-a3d016cd2a32", "name": "cid", "value": "={{ $('Loop Items with Website').item.json.cid }}", "type": "string"}, {"id": "9bfcc5fe-bd5d-437a-abae-ad65b81dffcb", "name": "data", "value": "={{ $json.data }}", "type": "string"}, {"id": "c36ac834-1992-4465-96f7-fe32d9e870d1", "name": "headers", "value": "={{ $json.headers }}", "type": "object"}, {"id": "5932d7d4-29a5-433b-a9a9-0c7924ec4ce6", "name": "statusCode", "value": "={{ $json.statusCode }}", "type": "number"}, {"id": "44048918-e492-45f7-a214-5bfa8a9a098e", "name": "statusMessage", "value": "={{ $json.statusMessage }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2600, -40], "id": "c9c2fe04-ef7a-4d19-bbd0-bc1939059eec", "name": "Add CID"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1IjsRRm-e9-zPNmunEYQ4XfkOkHJk3bG9-YaAvx7Z_N8", "mode": "id"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Top Cities", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1IjsRRm-e9-zPNmunEYQ4XfkOkHJk3bG9-YaAvx7Z_N8/edit#gid=*********"}, "columns": {"mappingMode": "autoMapInputData", "value": {}, "matchingColumns": ["cid"], "schema": [{"id": "position", "displayName": "position", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "latitude", "displayName": "latitude", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "longitude", "displayName": "longitude", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "ratingCount", "displayName": "ratingCount", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phoneNumber", "displayName": "phoneNumber", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "cid", "displayName": "cid", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "bookingLinks", "displayName": "bookingLinks", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "priceLevel", "displayName": "priceLevel", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [3240, -40], "id": "1bdd6f01-e4bb-47e1-af85-8fd4a2ecf658", "name": "Update Top Cities Sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "pg6LVyUiKjrgGbLx", "name": "Google Sheets account"}}}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1IjsRRm-e9-zPNmunEYQ4XfkOkHJk3bG9-YaAvx7Z_N8", "mode": "id"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Top Cities", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1IjsRRm-e9-zPNmunEYQ4XfkOkHJk3bG9-YaAvx7Z_N8/edit#gid=*********"}, "columns": {"mappingMode": "autoMapInputData", "value": {}, "matchingColumns": ["cid"], "schema": [{"id": "position", "displayName": "position", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "latitude", "displayName": "latitude", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "longitude", "displayName": "longitude", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "ratingCount", "displayName": "ratingCount", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "phoneNumber", "displayName": "phoneNumber", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "cid", "displayName": "cid", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "bookingLinks", "displayName": "bookingLinks", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "priceLevel", "displayName": "priceLevel", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1660, -40], "id": "4b1af4b7-081c-42e8-b625-77186d6b057b", "name": "Append or Update Top Cities Sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "pg6LVyUiKjrgGbLx", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// n8n-ready clean content parser for extracting business data from HTML\n// Use this in an n8n Code node\n\n// Main function for n8n\nfunction extractBusinessDataForN8n(items) {\n  const results = [];\n  \n  for (const item of items) {\n    try {\n      // Expect input data structure: { cid: \"...\", data: \"HTML content\" }\n      const htmlContent = item.json.data || item.json.html || item.json.content;\n      const cid = item.json.cid || item.json.id;\n      \n      if (!htmlContent) {\n        results.push({\n          json: {\n            error: \"No HTML content found in input\",\n            cid: cid\n          }\n        });\n        continue;\n      }\n      \n      const business = extractCleanBusinessInfo(htmlContent, cid);\n      \n      results.push({\n        json: {\n          ...business,\n          extraction_success: true,\n          extraction_timestamp: new Date().toISOString()\n        }\n      });\n      \n    } catch (error) {\n      results.push({\n        json: {\n          error: error.message,\n          cid: item.json.cid || item.json.id,\n          extraction_success: false\n        }\n      });\n    }\n  }\n  \n  return results;\n}\n\nfunction extractCleanBusinessInfo(htmlContent, cid = null) {\n  const business = {\n    cid: cid,\n    business_name: null,\n    phone: null,\n    email: null,\n    website: null,\n    address: null,\n    description: null,\n    business_insights: {},\n    contact_persons: [],\n    customer_reviews: [],\n    services: [],\n    extraction_metadata: {\n      extraction_date: new Date().toISOString(),\n      source: 'scraped_html_clean',\n      content_length: htmlContent.length\n    }\n  };\n  \n  try {\n    // Extract title and business name\n    const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\\/title>/i);\n    if (titleMatch) {\n      business.business_name = titleMatch[1].trim().split(':')[0].trim();\n    }\n    \n    // Extract meta description\n    const descMatch = htmlContent.match(/<meta[^>]*name=[\"\\']description[\"\\'][^>]*content=[\"\\']([^\"']+)[\"\\'][^>]*>/i);\n    if (descMatch) {\n      business.description = descMatch[1].trim();\n    }\n    \n    // Extract phone from JSON-LD or tel: links\n    const phonePatterns = [\n      /\"telephone\":\\s*\"([^\"]+)\"/gi,\n      /tel:([+\\d\\s\\-\\(\\)\\.]+)/gi\n    ];\n    \n    for (const pattern of phonePatterns) {\n      const matches = htmlContent.match(pattern);\n      if (matches && matches.length > 0) {\n        business.phone = matches[0].replace(/[\"telephone\":]/g, '').replace(/tel:/i, '').trim();\n        break;\n      }\n    }\n    \n    // Extract email from JSON-LD or mailto: links\n    const emailPatterns = [\n      /\"email\":\\s*\"([^\"]+)\"/gi,\n      /mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})/gi\n    ];\n    \n    for (const pattern of emailPatterns) {\n      const matches = htmlContent.match(pattern);\n      if (matches && matches.length > 0) {\n        business.email = matches[0].replace(/[\"email\":]/g, '').replace(/mailto:/i, '').trim();\n        break;\n      }\n    }\n    \n    // Extract canonical URL\n    const canonicalMatch = htmlContent.match(/<link[^>]*rel=[\"\\']canonical[\"\\'][^>]*href=[\"\\']([^\"']+)[\"\\'][^>]*>/i);\n    if (canonicalMatch) {\n      business.website = canonicalMatch[1].trim();\n    }\n    \n    // Extract business insights from visible text content\n    const textContent = extractVisibleText(htmlContent);\n    business.business_insights = extractBusinessInsights(textContent);\n    \n    // Extract contact persons from reviews\n    business.contact_persons = extractContactPersonsFromReviews(textContent);\n    \n    // Extract customer reviews\n    business.customer_reviews = extractCustomerReviews(htmlContent);\n    \n    // Extract services from clean text\n    business.services = extractServices(textContent);\n    \n    // Extract structured data\n    extractStructuredData(htmlContent, business);\n    \n  } catch (error) {\n    business.extraction_error = error.message;\n  }\n  \n  return business;\n}\n\nfunction extractVisibleText(htmlContent) {\n  // Remove script and style tags\n  let text = htmlContent.replace(/<script[^>]*>[\\s\\S]*?<\\/script>/gi, '');\n  text = text.replace(/<style[^>]*>[\\s\\S]*?<\\/style>/gi, '');\n  \n  // Remove HTML tags\n  text = text.replace(/<[^>]*>/g, ' ');\n  \n  // Clean up whitespace\n  text = text.replace(/\\s+/g, ' ').trim();\n  \n  // Decode HTML entities\n  text = text.replace(/&nbsp;/g, ' ');\n  text = text.replace(/&amp;/g, '&');\n  text = text.replace(/&lt;/g, '<');\n  text = text.replace(/&gt;/g, '>');\n  text = text.replace(/&quot;/g, '\"');\n  text = text.replace(/&#8217;/g, \"'\");\n  \n  return text;\n}\n\nfunction extractBusinessInsights(textContent) {\n  const insights = {};\n  \n  // Years of experience\n  const experienceMatch = textContent.match(/(\\d+)\\s*(?:\\+)?\\s*years?\\s+(?:of\\s+)?experience/i);\n  if (experienceMatch) {\n    insights.years_of_experience = experienceMatch[1];\n  }\n  \n  // Projects completed\n  const projectsMatch = textContent.match(/(\\d+)\\s*projects?\\s+completed/i);\n  if (projectsMatch) {\n    insights.projects_completed = projectsMatch[1];\n  }\n  \n  // Buildings constructed\n  const buildingsMatch = textContent.match(/(\\d+)\\s*buildings?\\s+constructed/i);\n  if (buildingsMatch) {\n    insights.buildings_constructed = buildingsMatch[1];\n  }\n  \n  // Satisfied clients\n  const clientsMatch = textContent.match(/(\\d+)\\s*satisfied\\s+clients/i);\n  if (clientsMatch) {\n    insights.satisfied_clients = clientsMatch[1];\n  }\n  \n  // Awards\n  const awardMatch = textContent.match(/(quality\\s+business\\s+awards?|award)/i);\n  if (awardMatch) {\n    insights.awards = \"2023 QUALITY BUSINESS AWARDS\";\n  }\n  \n  return insights;\n}\n\nfunction extractContactPersonsFromReviews(textContent) {\n  const contacts = [];\n  \n  // Look for common names mentioned in reviews as contractors\n  const namePatterns = [\n    { pattern: /alex\\s+(?:did|to|remodel|renovate)/gi, name: \"Alex\", title: \"Project Manager/Contractor\" },\n    { pattern: /john\\s+(?:did|completed|managed)/gi, name: \"John\", title: \"Project Manager\" },\n    { pattern: /mike\\s+(?:did|handled|worked)/gi, name: \"Mike\", title: \"Contractor\" }\n  ];\n  \n  namePatterns.forEach(({ pattern, name, title }) => {\n    const matches = textContent.match(pattern);\n    if (matches && matches.length > 0) {\n      contacts.push({\n        name: name,\n        title: title,\n        source: \"customer_reviews\",\n        mentions: matches.length\n      });\n    }\n  });\n  \n  return contacts;\n}\n\nfunction extractCustomerReviews(htmlContent) {\n  const reviews = [];\n  \n  // Extract reviews from the review widget\n  const reviewPattern = /<div class=\"ti-review-text-container[^>]*><!-- R-CONTENT -->([^<]+)<!-- R-CONTENT --><\\/div>/g;\n  let match;\n  \n  while ((match = reviewPattern.exec(htmlContent)) !== null) {\n    const reviewText = match[1].trim();\n    if (reviewText.length > 20) {\n      reviews.push({\n        text: reviewText,\n        source: \"google_reviews\"\n      });\n    }\n  }\n  \n  return reviews.slice(0, 5); // Limit to 5 reviews\n}\n\nfunction extractServices(textContent) {\n  const services = [];\n  const serviceKeywords = [\n    'renovation', 'remodeling', 'construction', 'kitchen', 'bathroom', \n    'interior', 'apartment', 'home improvement', 'general contracting',\n    'design', 'building', 'residential', 'commercial', 'plumbing',\n    'electrical', 'roofing', 'flooring', 'painting', 'carpentry'\n  ];\n  \n  serviceKeywords.forEach(keyword => {\n    const regex = new RegExp(`\\\\b${keyword}\\\\b`, 'gi');\n    if (regex.test(textContent)) {\n      services.push(keyword);\n    }\n  });\n  \n  return [...new Set(services)];\n}\n\nfunction extractStructuredData(htmlContent, business) {\n  const jsonLdMatches = htmlContent.match(/<script[^>]*type=[\"\\']application\\/ld\\+json[\"\\'][^>]*>(.*?)<\\/script>/gis);\n  if (jsonLdMatches) {\n    jsonLdMatches.forEach(match => {\n      try {\n        const jsonContent = match.replace(/<script[^>]*>/i, '').replace(/<\\/script>/i, '');\n        const jsonData = JSON.parse(jsonContent);\n        \n        if (jsonData['@graph']) {\n          jsonData['@graph'].forEach(item => {\n            mergeStructuredData(item, business);\n          });\n        } else {\n          mergeStructuredData(jsonData, business);\n        }\n      } catch (e) {\n        // Skip invalid JSON\n      }\n    });\n  }\n}\n\nfunction mergeStructuredData(jsonData, business) {\n  // Only update if not already set\n  if (jsonData.name && !business.business_name) {\n    business.business_name = jsonData.name;\n  }\n  \n  if (jsonData.telephone && !business.phone) {\n    business.phone = jsonData.telephone;\n  }\n  \n  if (jsonData.email && !business.email) {\n    business.email = jsonData.email;\n  }\n  \n  if (jsonData.url && !business.website) {\n    business.website = jsonData.url;\n  }\n  \n  if (jsonData.address && !business.address) {\n    if (typeof jsonData.address === 'string') {\n      business.address = jsonData.address;\n    } else if (jsonData.address.streetAddress) {\n      const addr = jsonData.address;\n      business.address = `${addr.streetAddress || ''} ${addr.addressLocality || ''} ${addr.addressRegion || ''} ${addr.postalCode || ''}`.trim();\n    }\n  }\n  \n  // Add additional insights from structured data\n  if (jsonData.areaServed) {\n    business.business_insights.area_served = jsonData.areaServed;\n  }\n  \n  if (jsonData.priceRange) {\n    business.business_insights.price_range = jsonData.priceRange;\n  }\n}\n\n// n8n execution - this is what n8n will call\nreturn extractBusinessDataForN8n($input.all());\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2820, -40], "id": "80cab817-9b03-418b-be91-78d90275fbff", "name": "Extract Scraper Data"}, {"parameters": {"assignments": {"assignments": [{"id": "95c8ed27-7cb7-40e1-a7f9-b26ab3e101ea", "name": "cid", "value": "={{ $json.cid }}", "type": "string"}, {"id": "6984296a-ba30-4fc7-b5ab-07e1e4cd4c1d", "name": "scraped_business_name", "value": "={{ $json.business_name }}", "type": "string"}, {"id": "e4bec515-cebf-4a7d-89c0-22c52839f6ab", "name": "scraped_phone", "value": "={{ $json.phone }}", "type": "string"}, {"id": "6c839067-44a7-4fa4-8afb-eec9117f9ebb", "name": "email", "value": "={{ $json.email }}", "type": "string"}, {"id": "7a9b4a9e-fa51-4113-9513-54a22f937ae1", "name": "description", "value": "={{ $json.description }}", "type": "string"}, {"id": "6677b572-9072-4664-9441-8c1bf796567b", "name": "business_insights", "value": "={{ $json.business_insights }}", "type": "object"}, {"id": "d3aa9804-b466-4024-9b41-b440ce1a4eff", "name": "contact_persons", "value": "={{ $json.contact_persons }}", "type": "array"}, {"id": "2533c06f-17fc-4731-8249-a75721c4c942", "name": "services", "value": "={{ $json.services }}", "type": "array"}, {"id": "c955d89e-eff8-4b69-b37c-204582662ef3", "name": "extraction_metadata", "value": "={{ $json.extraction_metadata }}", "type": "object"}, {"id": "b5291c48-0bc1-46a3-ac3f-1530b994866f", "name": "extraction_success", "value": "={{ $json.extraction_success }}", "type": "boolean"}, {"id": "e5700979-631b-4c9a-b165-acc103954eca", "name": "extraction_timestamp", "value": "={{ $json.extraction_timestamp }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [3040, -40], "id": "a5617d3c-f6cb-470f-b9ce-a15e2f50b280", "name": "Set Scraped Data"}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Top Cities", "type": "main", "index": 0}]]}, "Loop Search Queries": {"main": [[], [{"node": "Code1", "type": "main", "index": 0}]]}, "Top Cities": {"main": [[{"node": "Loop Search Queries", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Append or Update Top Cities Sheet", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Loop Items with Website", "type": "main", "index": 0}]]}, "SerperDev": {"main": [[{"node": "Loop Search Queries", "type": "main", "index": 0}, {"node": "Split Out1", "type": "main", "index": 0}]]}, "ScraperAPI": {"main": [[{"node": "Add CID", "type": "main", "index": 0}]]}, "Loop Items with Website": {"main": [[], [{"node": "ScraperAPI", "type": "main", "index": 0}]]}, "Add CID": {"main": [[{"node": "Extract Scraper Data", "type": "main", "index": 0}]]}, "Update Top Cities Sheet": {"main": [[{"node": "Loop Items with Website", "type": "main", "index": 0}]]}, "Append or Update Top Cities Sheet": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Extract Scraper Data": {"main": [[{"node": "Set Scraped Data", "type": "main", "index": 0}]]}, "Set Scraped Data": {"main": [[{"node": "Update Top Cities Sheet", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e79c1649-6f45-41db-b996-d7f444a73691", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f0c1230faa584ad7a426801d804cca8e0d8678d13d6f8bef8f679dc4db8541ba"}, "id": "zUdlmozsrGZMNmiK", "tags": []}