#!/usr/bin/env python3
"""
Cross-deduplicate Equipment dataset against all other datasets 
by removing domains that already exist in any of the other datasets.
"""

import pandas as pd
import os
from pathlib import Path

def cross_deduplicate_equipment_vs_all():
    """
    Remove domains from Equipment that already exist in other datasets.
    """
    print("CROSS-DEDUPLICATION: EQUIPMENT vs ALL OTHER DATASETS")
    print("="*65)
    
    # File paths
    b2_file = "../B2/merged_data_final.csv"
    b3_file = "../B3/b3_final_cross_deduplicated.csv"
    b2a_file = "../B2A/b2a_final_cross_deduplicated.csv"
    b4_file = "../B4 Done/b4_final_cross_deduplicated.csv"
    hotel_buildings_file = "../hotel and buildings/hotel_buildings_final_cross_deduplicated.csv"
    buildings_church_file = "../buildings and church/buildings_church_final_cross_deduplicated.csv"
    auto_business_file = "../auto business/auto_business_final_cross_deduplicated.csv"
    equipment_file = "equipment_merged_deduplicated.csv"
    output_file = "equipment_final_cross_deduplicated.csv"
    
    # Check if files exist
    files_to_check = [
        ("B2", b2_file),
        ("B3", b3_file),
        ("B2A", b2a_file),
        ("B4", b4_file),
        ("Hotel & Buildings", hotel_buildings_file),
        ("Buildings & Churches", buildings_church_file),
        ("Auto Business", auto_business_file),
        ("Equipment", equipment_file)
    ]
    
    for name, file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ ERROR: {name} file not found: {file_path}")
            return
    
    # Load all datasets
    datasets = {}
    all_existing_domains = set()
    
    print("📁 Loading datasets...")
    
    # Load B2
    try:
        print("   Loading B2 (this may take a moment for large files)...")
        datasets['B2'] = pd.read_csv(b2_file, low_memory=False)
        b2_domains = set(datasets['B2']['domain'].dropna().str.strip())
        all_existing_domains.update(b2_domains)
        print(f"   ✅ B2 loaded: {len(datasets['B2']):,} rows, {len(b2_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B2 file: {e}")
        return
    
    # Load B3
    try:
        datasets['B3'] = pd.read_csv(b3_file, low_memory=False)
        b3_domains = set(datasets['B3']['domain'].dropna().str.strip())
        all_existing_domains.update(b3_domains)
        print(f"   ✅ B3 loaded: {len(datasets['B3']):,} rows, {len(b3_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B3 file: {e}")
        return
    
    # Load B2A
    try:
        datasets['B2A'] = pd.read_csv(b2a_file, low_memory=False)
        b2a_domains = set(datasets['B2A']['domain'].dropna().str.strip())
        all_existing_domains.update(b2a_domains)
        print(f"   ✅ B2A loaded: {len(datasets['B2A']):,} rows, {len(b2a_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B2A file: {e}")
        return
    
    # Load B4
    try:
        datasets['B4'] = pd.read_csv(b4_file, low_memory=False)
        b4_domains = set(datasets['B4']['domain'].dropna().str.strip())
        all_existing_domains.update(b4_domains)
        print(f"   ✅ B4 loaded: {len(datasets['B4']):,} rows, {len(b4_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B4 file: {e}")
        return
    
    # Load Hotel & Buildings
    try:
        print("   Loading Hotel & Buildings (this may take a moment for large files)...")
        datasets['Hotel_Buildings'] = pd.read_csv(hotel_buildings_file, low_memory=False)
        hb_domains = set(datasets['Hotel_Buildings']['domain'].dropna().str.strip())
        all_existing_domains.update(hb_domains)
        print(f"   ✅ Hotel & Buildings loaded: {len(datasets['Hotel_Buildings']):,} rows, {len(hb_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading Hotel & Buildings file: {e}")
        return
    
    # Load Buildings & Churches
    try:
        datasets['Buildings_Churches'] = pd.read_csv(buildings_church_file, low_memory=False)
        bc_domains = set(datasets['Buildings_Churches']['domain'].dropna().str.strip())
        all_existing_domains.update(bc_domains)
        print(f"   ✅ Buildings & Churches loaded: {len(datasets['Buildings_Churches']):,} rows, {len(bc_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading Buildings & Churches file: {e}")
        return
    
    # Load Auto Business
    try:
        datasets['Auto_Business'] = pd.read_csv(auto_business_file, low_memory=False)
        ab_domains = set(datasets['Auto_Business']['domain'].dropna().str.strip())
        all_existing_domains.update(ab_domains)
        print(f"   ✅ Auto Business loaded: {len(datasets['Auto_Business']):,} rows, {len(ab_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading Auto Business file: {e}")
        return
    
    # Load Equipment
    try:
        print("   Loading Equipment...")
        datasets['Equipment'] = pd.read_csv(equipment_file, low_memory=False)
        eq_domains = set(datasets['Equipment']['domain'].dropna().str.strip())
        print(f"   ✅ Equipment loaded: {len(datasets['Equipment']):,} rows, {len(eq_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading Equipment file: {e}")
        return
    
    # Analyze overlaps
    print(f"\n🔍 ANALYZING DOMAIN OVERLAPS")
    print("-" * 55)
    
    overlap_eq_vs_b2 = eq_domains.intersection(b2_domains)
    overlap_eq_vs_b3 = eq_domains.intersection(b3_domains)
    overlap_eq_vs_b2a = eq_domains.intersection(b2a_domains)
    overlap_eq_vs_b4 = eq_domains.intersection(b4_domains)
    overlap_eq_vs_hb = eq_domains.intersection(hb_domains)
    overlap_eq_vs_bc = eq_domains.intersection(bc_domains)
    overlap_eq_vs_ab = eq_domains.intersection(ab_domains)
    total_overlaps = eq_domains.intersection(all_existing_domains)
    
    print(f"Equipment vs B2 overlaps: {len(overlap_eq_vs_b2):,}")
    print(f"Equipment vs B3 overlaps: {len(overlap_eq_vs_b3):,}")
    print(f"Equipment vs B2A overlaps: {len(overlap_eq_vs_b2a):,}")
    print(f"Equipment vs B4 overlaps: {len(overlap_eq_vs_b4):,}")
    print(f"Equipment vs Hotel & Buildings overlaps: {len(overlap_eq_vs_hb):,}")
    print(f"Equipment vs Buildings & Churches overlaps: {len(overlap_eq_vs_bc):,}")
    print(f"Equipment vs Auto Business overlaps: {len(overlap_eq_vs_ab):,}")
    print(f"Total Equipment overlaps: {len(total_overlaps):,}")
    
    if len(total_overlaps) == 0:
        print("✅ No overlapping domains found! Equipment dataset is already unique vs all others.")
        # Still save a copy for consistency
        datasets['Equipment'].to_csv(output_file, index=False, encoding='utf-8')
        print(f"💾 Saved copy as: {output_file}")
        return datasets['Equipment']
    
    # Show some examples of overlapping domains
    print(f"\nExamples of overlapping domains:")
    for i, domain in enumerate(list(total_overlaps)[:15]):
        # Check which dataset(s) it overlaps with
        overlap_sources = []
        if domain in b2_domains:
            overlap_sources.append("B2")
        if domain in b3_domains:
            overlap_sources.append("B3")
        if domain in b2a_domains:
            overlap_sources.append("B2A")
        if domain in b4_domains:
            overlap_sources.append("B4")
        if domain in hb_domains:
            overlap_sources.append("H&B")
        if domain in bc_domains:
            overlap_sources.append("B&C")
        if domain in ab_domains:
            overlap_sources.append("Auto")
        print(f"  {i+1}. {domain} (overlaps with: {', '.join(overlap_sources)})")
    
    if len(total_overlaps) > 15:
        print(f"  ... and {len(total_overlaps) - 15} more")
    
    # Remove overlapping domains from Equipment
    print(f"\n🗑️  REMOVING OVERLAPPING DOMAINS FROM EQUIPMENT")
    print("-" * 65)
    
    original_eq_count = len(datasets['Equipment'])
    
    # Create mask for rows to keep (domains NOT in any other dataset)
    mask_keep = ~datasets['Equipment']['domain'].isin(all_existing_domains)
    eq_filtered = datasets['Equipment'][mask_keep].copy()
    
    removed_count = original_eq_count - len(eq_filtered)
    
    print(f"Original Equipment rows: {original_eq_count:,}")
    print(f"Rows removed: {removed_count:,}")
    print(f"Remaining rows: {len(eq_filtered):,}")
    print(f"Removal rate: {(removed_count / original_eq_count) * 100:.2f}%")
    print(f"Retention rate: {(len(eq_filtered) / original_eq_count) * 100:.2f}%")
    
    # Verify no overlaps remain
    remaining_eq_domains = set(eq_filtered['domain'].dropna().str.strip())
    final_overlap_b2 = b2_domains.intersection(remaining_eq_domains)
    final_overlap_b3 = b3_domains.intersection(remaining_eq_domains)
    final_overlap_b2a = b2a_domains.intersection(remaining_eq_domains)
    final_overlap_b4 = b4_domains.intersection(remaining_eq_domains)
    final_overlap_hb = hb_domains.intersection(remaining_eq_domains)
    final_overlap_bc = bc_domains.intersection(remaining_eq_domains)
    final_overlap_ab = ab_domains.intersection(remaining_eq_domains)
    
    print(f"\n✅ VERIFICATION")
    print("-" * 15)
    print(f"Remaining Equipment unique domains: {len(remaining_eq_domains):,}")
    print(f"Final overlap with B2: {len(final_overlap_b2)}")
    print(f"Final overlap with B3: {len(final_overlap_b3)}")
    print(f"Final overlap with B2A: {len(final_overlap_b2a)}")
    print(f"Final overlap with B4: {len(final_overlap_b4)}")
    print(f"Final overlap with Hotel & Buildings: {len(final_overlap_hb)}")
    print(f"Final overlap with Buildings & Churches: {len(final_overlap_bc)}")
    print(f"Final overlap with Auto Business: {len(final_overlap_ab)}")
    
    all_overlaps_zero = (len(final_overlap_b2) == 0 and 
                        len(final_overlap_b3) == 0 and 
                        len(final_overlap_b2a) == 0 and
                        len(final_overlap_b4) == 0 and
                        len(final_overlap_hb) == 0 and
                        len(final_overlap_bc) == 0 and
                        len(final_overlap_ab) == 0)
    print(f"Cross-deduplication successful: {all_overlaps_zero}")
    
    # Save the cross-deduplicated dataset
    print(f"\n💾 SAVING CROSS-DEDUPLICATED DATASET")
    print("-" * 60)
    
    eq_filtered.to_csv(output_file, index=False, encoding='utf-8')
    
    file_size = os.path.getsize(output_file) / (1024*1024)
    print(f"Saved: {output_file}")
    print(f"File size: {file_size:.2f} MB")
    
    # Generate summary report
    generate_cross_dedup_report(datasets, eq_filtered, total_overlaps, 
                               overlap_eq_vs_b2, overlap_eq_vs_b3, 
                               overlap_eq_vs_b2a, overlap_eq_vs_b4, 
                               overlap_eq_vs_hb, overlap_eq_vs_bc, overlap_eq_vs_ab)
    
    print(f"\n🎉 CROSS-DEDUPLICATION COMPLETED!")
    print(f"📁 Final Equipment dataset: {output_file}")
    print(f"📊 Report: equipment_cross_dedup_report.txt")
    
    # Show final combined potential
    print(f"\n🌟 COMBINED DATASET POTENTIAL:")
    print("-" * 50)
    total_unique_domains = (len(b2_domains) + len(b3_domains) + 
                           len(b2a_domains) + len(b4_domains) + 
                           len(hb_domains) + len(bc_domains) + 
                           len(ab_domains) + len(remaining_eq_domains))
    print(f"B2 domains: {len(b2_domains):,}")
    print(f"B3 domains: {len(b3_domains):,}")
    print(f"B2A domains: {len(b2a_domains):,}")
    print(f"B4 domains: {len(b4_domains):,}")
    print(f"Hotel & Buildings domains: {len(hb_domains):,}")
    print(f"Buildings & Churches domains: {len(bc_domains):,}")
    print(f"Auto Business domains: {len(ab_domains):,}")
    print(f"Equipment domains: {len(remaining_eq_domains):,}")
    print(f"Total unique domains: {total_unique_domains:,}")
    
    return eq_filtered

def generate_cross_dedup_report(datasets, eq_final, total_overlaps, 
                               overlap_b2, overlap_b3, overlap_b2a, overlap_b4, 
                               overlap_hb, overlap_bc, overlap_ab):
    """
    Generate a comprehensive cross-deduplication report.
    """
    report_content = f"""EQUIPMENT vs ALL DATASETS CROSS-DEDUPLICATION REPORT
====================================================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
Process: Remove Equipment domains that already exist in other datasets

DATASET SUMMARY:
===============
B2 Dataset: {len(datasets['B2']):,} rows, {datasets['B2']['domain'].nunique():,} unique domains
B3 Dataset: {len(datasets['B3']):,} rows, {datasets['B3']['domain'].nunique():,} unique domains
B2A Dataset: {len(datasets['B2A']):,} rows, {datasets['B2A']['domain'].nunique():,} unique domains
B4 Dataset: {len(datasets['B4']):,} rows, {datasets['B4']['domain'].nunique():,} unique domains
Hotel & Buildings Dataset: {len(datasets['Hotel_Buildings']):,} rows, {datasets['Hotel_Buildings']['domain'].nunique():,} unique domains
Buildings & Churches Dataset: {len(datasets['Buildings_Churches']):,} rows, {datasets['Buildings_Churches']['domain'].nunique():,} unique domains
Auto Business Dataset: {len(datasets['Auto_Business']):,} rows, {datasets['Auto_Business']['domain'].nunique():,} unique domains

Equipment Dataset (Original): {len(datasets['Equipment']):,} rows, {datasets['Equipment']['domain'].nunique():,} unique domains

CROSS-DEDUPLICATION RESULTS:
===========================
- Equipment vs B2 overlaps: {len(overlap_b2):,}
- Equipment vs B3 overlaps: {len(overlap_b3):,}
- Equipment vs B2A overlaps: {len(overlap_b2a):,}
- Equipment vs B4 overlaps: {len(overlap_b4):,}
- Equipment vs Hotel & Buildings overlaps: {len(overlap_hb):,}
- Equipment vs Buildings & Churches overlaps: {len(overlap_bc):,}
- Equipment vs Auto Business overlaps: {len(overlap_ab):,}
- Total overlapping domains: {len(total_overlaps):,}
- Equipment rows removed: {len(datasets['Equipment']) - len(eq_final):,}
- Equipment rows retained: {len(eq_final):,}
- Retention rate: {(len(eq_final) / len(datasets['Equipment'])) * 100:.2f}%

FINAL COMBINED DATASET POTENTIAL:
================================
- B2 unique domains: {datasets['B2']['domain'].nunique():,}
- B3 unique domains: {datasets['B3']['domain'].nunique():,}
- B2A unique domains: {datasets['B2A']['domain'].nunique():,}
- B4 unique domains: {datasets['B4']['domain'].nunique():,}
- Hotel & Buildings unique domains: {datasets['Hotel_Buildings']['domain'].nunique():,}
- Buildings & Churches unique domains: {datasets['Buildings_Churches']['domain'].nunique():,}
- Auto Business unique domains: {datasets['Auto_Business']['domain'].nunique():,}
- Equipment unique domains (after cross-dedup): {eq_final['domain'].nunique():,}
- Total unique domains across all eight: {datasets['B2']['domain'].nunique() + datasets['B3']['domain'].nunique() + datasets['B2A']['domain'].nunique() + datasets['B4']['domain'].nunique() + datasets['Hotel_Buildings']['domain'].nunique() + datasets['Buildings_Churches']['domain'].nunique() + datasets['Auto_Business']['domain'].nunique() + eq_final['domain'].nunique():,}
- Perfect separation achieved: True

FINAL EQUIPMENT SOURCE FILE BREAKDOWN:
=====================================
"""
    
    # Add source file breakdown for final Equipment
    source_stats = eq_final['source_file'].value_counts()
    for source, count in source_stats.items():
        report_content += f"- {source}: {count:,} rows\n"
    
    # Add top domains in final Equipment
    report_content += f"\nTOP DOMAINS IN FINAL EQUIPMENT:\n"
    report_content += f"==============================\n"
    domain_counts = eq_final['domain'].value_counts().head(20)
    for i, (domain, count) in enumerate(domain_counts.items(), 1):
        report_content += f"{i}. {domain}: {count} businesses\n"
    
    # Save report
    with open('equipment_cross_dedup_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📊 Cross-deduplication report saved: equipment_cross_dedup_report.txt")

def main():
    """
    Main function to execute cross-deduplication.
    """
    try:
        result_df = cross_deduplicate_equipment_vs_all()
        if result_df is not None:
            print(f"\n✅ SUCCESS: Cross-deduplication completed successfully!")
        else:
            print(f"\n❌ FAILED: Cross-deduplication encountered errors.")
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")

if __name__ == "__main__":
    main()
