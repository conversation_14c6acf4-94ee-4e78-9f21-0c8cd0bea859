#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to merge multiple XLSX files into a single CSV file.
"""

import pandas as pd
import glob
import os
from pathlib import Path

def merge_xlsx_files_to_csv():
    """
    Merge all XLSX files in the current directory into a single CSV file.
    """
    # Get all XLSX files in the current directory
    xlsx_files = glob.glob("*.xlsx")
    
    if not xlsx_files:
        print("No XLSX files found in the current directory.")
        return
    
    print(f"Found {len(xlsx_files)} XLSX files:")
    for file in xlsx_files:
        print(f"  - {file}")
    
    # List to store all dataframes
    all_dataframes = []
    
    # Process each XLSX file
    for file in xlsx_files:
        try:
            print(f"\nProcessing: {file}")
            
            # Read the XLSX file
            df = pd.read_excel(file)
            
            # Add a column to identify the source file
            df['source_file'] = file
            
            print(f"  - Shape: {df.shape}")
            print(f"  - Columns: {len(df.columns)}")
            
            all_dataframes.append(df)
            
        except Exception as e:
            print(f"  - Error reading {file}: {str(e)}")
            continue
    
    if not all_dataframes:
        print("No valid XLSX files could be processed.")
        return
    
    # Combine all dataframes
    print(f"\nCombining {len(all_dataframes)} dataframes...")
    
    # Use concat with ignore_index=True to reset index
    combined_df = pd.concat(all_dataframes, ignore_index=True, sort=False)
    
    print(f"Combined dataframe shape: {combined_df.shape}")
    print(f"Total columns: {len(combined_df.columns)}")
    
    # Save to CSV
    output_file = "merged_data.csv"
    print(f"\nSaving to: {output_file}")
    
    combined_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"Successfully merged {len(xlsx_files)} XLSX files into {output_file}")
    
    # Display summary statistics
    print(f"\nSummary:")
    print(f"  - Total rows: {len(combined_df):,}")
    print(f"  - Total columns: {len(combined_df.columns)}")
    print(f"  - Output file size: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    # Show source file distribution
    print(f"\nRows per source file:")
    source_counts = combined_df['source_file'].value_counts()
    for source, count in source_counts.items():
        print(f"  - {source}: {count:,} rows")

if __name__ == "__main__":
    merge_xlsx_files_to_csv()
