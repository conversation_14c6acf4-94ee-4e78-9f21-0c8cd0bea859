import json

def add_batch_numbers():
    # Read the cities data
    with open('cities-1000-data.json', 'r') as f:
        cities = json.load(f)
    
    # Add batch numbers cycling from 1 to 10
    for i, city in enumerate(cities):
        batch_number = (i % 10) + 1  # This gives us 1-10 cycling
        city['batch'] = batch_number
    
    # Write back to file with proper formatting
    with open('cities-1000-data.json', 'w') as f:
        json.dump(cities, f, indent=2)
    
    print(f"Successfully added batch numbers to {len(cities)} cities")
    print("Batch distribution:")
    
    # Show batch distribution
    batch_counts = {}
    for city in cities:
        batch = city['batch']
        batch_counts[batch] = batch_counts.get(batch, 0) + 1
    
    for batch in sorted(batch_counts.keys()):
        print(f"Batch {batch}: {batch_counts[batch]} cities")

if __name__ == "__main__":
    add_batch_numbers()
