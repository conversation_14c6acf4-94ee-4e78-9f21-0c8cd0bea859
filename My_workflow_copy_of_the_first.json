{"name": "My workflow copy of the first", "nodes": [{"parameters": {"jsCode": "// Parse Serper search results and extract business information\nconst results = $input.all();\nconst businesses = [];\n\nresults.forEach((result, index) => {\n  const searchData = result.json;\n  \n  // Extract business type and location from the search query itself\n  const searchQuery = searchData.searchParameters?.q || '';\n  const location = searchData.searchParameters?.location || 'Miami FL';\n  \n  // Determine business type from search query\n  let businessType = 'contractor';\n  if (searchQuery.toLowerCase().includes('plumber')) businessType = 'plumber';\n  else if (searchQuery.toLowerCase().includes('roofer')) businessType = 'roofer';\n  else if (searchQuery.toLowerCase().includes('electrician')) businessType = 'electrician';\n  else if (searchQuery.toLowerCase().includes('hvac')) businessType = 'hvac';\n  else if (searchQuery.toLowerCase().includes('landscaping')) businessType = 'landscaping';\n  \n  // Process organic results - filter out directory pages\n  if (searchData.organic) {\n    searchData.organic.forEach(item => {\n      if (item.title && item.link) {\n        // Filter out directory/listing pages\n        const title = item.title.toLowerCase();\n        const link = item.link.toLowerCase();\n        \n        const isDirectory = title.includes('best ') || \n                           title.includes('top ') || \n                           title.includes('find ') || \n                           title.includes('search ') ||\n                           title.includes('directory') ||\n                           link.includes('yelp.com') ||\n                           link.includes('angi.com') ||\n                           link.includes('homeadvisor.com') ||\n                           link.includes('houzz.com') ||\n                           link.includes('bbb.org') ||\n                           link.includes('procore.com') ||\n                           link.includes('.gov/');\n        \n        if (!isDirectory) {\n          businesses.push({\n            business_name: item.title,\n            website: item.link,\n            snippet: item.snippet,\n            business_type: businessType,\n            location: location,\n            data_source: 'serper_organic',\n            extraction_date: new Date().toISOString()\n          });\n        }\n      }\n    });\n  }\n  \n  // Process places results (Google My Business listings)\n  if (searchData.places) {\n    searchData.places.forEach(place => {\n      if (place.title) {\n        businesses.push({\n          business_name: place.title,\n          address: place.address,\n          phone: place.phoneNumber,\n          rating: place.rating,\n          review_count: place.reviews,\n          website: place.website,\n          business_type: businessType,\n          location: location,\n          data_source: 'serper_places',\n          extraction_date: new Date().toISOString()\n        });\n      }\n    });\n  }\n});\n\nreturn businesses.map(business => ({ json: business }));"}, "id": "fb5bbc6d-e0e2-4ce6-a01f-619ed0e6af7a", "name": "Parse Search Results - PAGINATED", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [860, -200]}, {"parameters": {"rule": {"interval": [{}]}}, "id": "03e6e784-c0c2-4f9a-a9ff-f97358a642ce", "name": "Weekly Schedule", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [200, -200]}, {"parameters": {"jsCode": "// ENHANCED SEARCH QUERIES WITH PAGINATION - Get comprehensive coverage\nconst businessTypes = [\n  'general contractor',\n  'home builder',\n  'construction company',\n  'remodeling contractor',\n  'renovation contractor',\n  'custom home builder',\n  'residential contractor',\n  'commercial contractor',\n  'electrician',\n  'plumber',\n  'hvac contractor',\n  'heating contractor',\n  'air conditioning contractor',\n  'mechanical contractor',\n  'refrigeration contractor',\n  'ventilation contractor',\n  'insulation contractor',\n  'roofer',\n  'roofing contractor',\n  'siding contractor',\n  'window contractor',\n  'door contractor',\n  'deck builder',\n  'fence contractor',\n  'foundation contractor',\n  'concrete contractor',\n  'masonry contractor',\n  'stucco contractor',\n  'gutter contractor',\n  'drywall contractor',\n  'painter',\n  'flooring contractor',\n  'tile contractor',\n  'cabinet installer',\n  'carpenter',\n  'millwork contractor',\n  'bathroom remodeler',\n  'kitchen remodeler',\n  'basement remodeler',\n  'garage builder',\n  'pool contractor',\n  'spa contractor',\n  'solar contractor',\n  'security system installer',\n  'low voltage contractor',\n  'landscaper',\n  'landscape contractor',\n  'hardscape contractor',\n  'irrigation contractor',\n  'paving contractor',\n  'asphalt contractor',\n  'excavation contractor',\n  'grading contractor',\n  'septic contractor',\n  'handyman service',\n  'demolition contractor',\n  'welding contractor',\n  'scaffolding contractor',\n  'waterproofing contractor',\n  'restoration contractor',\n  'mold remediation contractor',\n  'fire damage contractor',\n  'environmental contractor',\n  'asbestos contractor'\n];\n\nconst locations = [\n  'Miami FL',\n  'Fort Lauderdale FL',\n  'West Palm Beach FL',\n  'Hollywood FL',\n  'Pembroke Pines FL',\n  'Coral Springs FL',\n  'Miramar FL',\n  'Davie FL',\n  'Plantation FL',\n  'Sunrise FL',\n  'Pompano Beach FL',\n  'Delray Beach FL',\n  'Boca Raton FL'\n];\n\n// Pagination settings\nconst resultsPerPage = 20;  // Serper limit per request\nconst maxPages = 10;         // Get first 5 pages (100 results total per query)\n\nconst queries = [];\n\nbusinessTypes.forEach(type => {\n  locations.forEach(location => {\n    // Generate multiple pages for each search query\n    for (let page = 1; page <= maxPages; page++) {\n      const startIndex = (page - 1) * resultsPerPage;\n      \n      queries.push({\n        search_query: `${type} ${location}`,\n        business_type: type,\n        location: location,\n        search_date: new Date().toISOString(),\n        \n        // Pagination parameters\n        page_number: page,\n        start_index: startIndex,\n        results_per_page: resultsPerPage,\n        max_results: resultsPerPage,\n        \n        // Query metadata\n        query_id: `${type.replace(/\\s+/g, '_')}_${location.replace(/\\s+/g, '_')}_page_${page}`,\n        total_pages: maxPages\n      });\n    }\n  });\n});\n\nconsole.log(`Generated ${queries.length} paginated search queries`);\nconsole.log(`Business types: ${businessTypes.length}`);\nconsole.log(`Locations: ${locations.length}`);\nconsole.log(`Pages per query: ${maxPages}`);\nconsole.log(`Expected total results: ${queries.length * resultsPerPage}`);\nconsole.log(`Total API calls: ${queries.length}`);\n\nreturn queries.map(query => ({ json: query }));"}, "id": "7de3e21a-2ede-43d1-9b2e-95f17023d885", "name": "Generate Search Queries", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [420, -200]}, {"parameters": {"method": "POST", "url": "https://google.serper.dev/search", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-API-KEY", "value": "c8f2c4b3b683e919d68aa4d9631ff88c16eb7d4e"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "q", "value": "={{ $json.search_query }}"}, {"name": "location", "value": "={{ $json.location }}"}, {"name": "num", "value": "={{ $json.results_per_page || 20 }}"}, {"name": "start", "value": "={{ $json.start_index || 0 }}"}, {"name": "gl", "value": "us"}, {"name": "hl", "value": "en"}]}, "options": {"response": {"response": {"responseFormat": "json"}}, "timeout": 30000}}, "id": "3b15a6c2-d3ff-4595-9e04-71fdc9d2c69d", "name": "Serper Google Search - PAGINATED", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [640, -200], "retryOnFail": true, "maxTries": 3, "waitBetweenTries": 2000, "continueOnFail": true}, {"parameters": {"jsCode": "// ENHANCED Deduplicate businesses - DOMAIN-LEVEL DEDUPLICATION\n// Handles high volume from pagination with improved domain matching\n\nconst businesses = $input.all().map(item => item.json);\nconst uniqueBusinesses = [];\nconst seen = new Set();\nconst duplicateCount = {};\nconst duplicateDetails = [];\n\nconsole.log('=== ENHANCED DEDUPLICATION WITH DOMAIN-LEVEL MATCHING ===');\nconsole.log('Total businesses before deduplication:', businesses.length);\n\nfunction normalizeString(str) {\n    if (!str) return '';\n    return str.toLowerCase().replace(/[^a-z0-9]/g, '');\n}\n\nfunction extractDomain(url) {\n    if (!url) return '';\n    try {\n        // Remove protocol and www\n        let domain = url.replace(/^https?:\\/\\//, '').replace(/^www\\./, '');\n        // Get just the domain part (before first slash)\n        domain = domain.split('/')[0];\n        // Remove common subdomains\n        domain = domain.replace(/^(m\\.|mobile\\.|app\\.)/, '');\n        return normalizeString(domain);\n    } catch (e) {\n        return normalizeString(url);\n    }\n}\n\nfunction extractBusinessNameCore(name) {\n    if (!name) return '';\n    // Remove common business suffixes and normalize\n    let core = name.toLowerCase()\n        .replace(/\\s*(inc|llc|corp|corporation|company|co|ltd|limited)\\s*\\.?\\s*$/gi, '')\n        .replace(/\\s*(contractors?|construction|roofing|plumbing|electrical|hvac)\\s*$/gi, '')\n        .replace(/\\s*(miami|fort lauderdale|west palm beach|hollywood|pembroke pines|fl|florida)\\s*/gi, '')\n        .replace(/[^a-z0-9\\s]/g, '')\n        .replace(/\\s+/g, ' ')\n        .trim();\n    return normalizeString(core);\n}\n\nfunction generateKey(business) {\n    const businessName = business.business_name || '';\n    const website = business.website || '';\n    const phone = business.phone || '';\n    const address = business.address || '';\n    \n    // Priority 1: Use domain if available (most reliable)\n    if (website && website.length > 10) {\n        const domain = extractDomain(website);\n        if (domain && domain.length > 3) {\n            console.log(`Using domain key for \"${businessName}\": ${domain}`);\n            return `domain_${domain}`;\n        }\n    }\n    \n    // Priority 2: Use phone number (very reliable)\n    if (phone && phone.length > 5) {\n        const cleanPhone = normalizeString(phone);\n        if (cleanPhone.length >= 10) {\n            console.log(`Using phone key for \"${businessName}\": ${cleanPhone}`);\n            return `phone_${cleanPhone}`;\n        }\n    }\n    \n    // Priority 3: Use business name core + address\n    const nameCore = extractBusinessNameCore(businessName);\n    const addressCore = normalizeString(address);\n    \n    if (nameCore && nameCore.length > 2) {\n        const key = `name_${nameCore}_${addressCore}`;\n        console.log(`Using name+address key for \"${businessName}\": ${key}`);\n        return key;\n    }\n    \n    // Fallback: Use full business name + address\n    const fallbackKey = `fallback_${normalizeString(businessName)}_${addressCore}`;\n    console.log(`Using fallback key for \"${businessName}\": ${fallbackKey}`);\n    return fallbackKey;\n}\n\n// Process each business\nbusinesses.forEach((business, index) => {\n    const key = generateKey(business);\n    \n    if (!seen.has(key)) {\n        seen.add(key);\n        \n        // Generate unique business ID\n        business.business_id = business.business_id || `dedup_${Date.now()}_${uniqueBusinesses.length}`;\n        \n        // Add deduplication metadata\n        business.dedup_index = uniqueBusinesses.length;\n        business.original_index = index;\n        business.dedup_key = key;\n        business.dedup_method = key.split('_')[0]; // domain, phone, name, or fallback\n        \n        uniqueBusinesses.push(business);\n        duplicateCount[key] = 1;\n        \n        console.log(`✅ KEPT: \"${business.business_name}\" (${business.dedup_method}) - ${business.website || 'no website'}`);\n    } else {\n        duplicateCount[key] = (duplicateCount[key] || 1) + 1;\n        \n        // Track duplicate details for analysis\n        duplicateDetails.push({\n            duplicate_name: business.business_name,\n            duplicate_website: business.website,\n            duplicate_phone: business.phone,\n            original_index: index,\n            dedup_key: key,\n            reason: 'duplicate_key_match'\n        });\n        \n        console.log(`❌ REMOVED: \"${business.business_name}\" (duplicate of key: ${key}) - ${business.website || 'no website'}`);\n    }\n});\n\n// Enhanced reporting\nconsole.log('=== ENHANCED DEDUPLICATION SUMMARY ===');\nconsole.log('Total businesses processed:', businesses.length);\nconsole.log('Unique businesses after deduplication:', uniqueBusinesses.length);\nconsole.log('Duplicates removed:', businesses.length - uniqueBusinesses.length);\nconsole.log('Deduplication rate:', ((businesses.length - uniqueBusinesses.length) / businesses.length * 100).toFixed(1) + '%');\n\n// Deduplication method breakdown\nconst methodBreakdown = {};\nuniqueBusinesses.forEach(b => {\n    methodBreakdown[b.dedup_method] = (methodBreakdown[b.dedup_method] || 0) + 1;\n});\nconsole.log('Deduplication methods used:');\nObject.entries(methodBreakdown).forEach(([method, count]) => {\n    console.log(`  ${method}: ${count} businesses`);\n});\n\n// Top duplicate groups\nconsole.log('Top duplicate groups:');\nconst topDuplicates = Object.entries(duplicateCount)\n    .filter(([k, v]) => v > 1)\n    .sort(([,a], [,b]) => b - a)\n    .slice(0, 10);\n\ntopDuplicates.forEach(([key, count]) => {\n    console.log(`  ${key}: ${count} instances`);\n});\n\n// Specific duplicate examples\nconsole.log('Duplicate examples removed:');\nduplicateDetails.slice(0, 5).forEach(dup => {\n    console.log(`  \"${dup.duplicate_name}\" (${dup.duplicate_website}) - key: ${dup.dedup_key}`);\n});\n\n// Quality metrics\nconst withEmails = uniqueBusinesses.filter(b => b.all_emails && b.all_emails.length > 0).length;\nconst withPhones = uniqueBusinesses.filter(b => b.phone).length;\nconst withWebsites = uniqueBusinesses.filter(b => b.website).length;\nconst withAddresses = uniqueBusinesses.filter(b => b.address).length;\n\nconsole.log('=== DATA QUALITY METRICS ===');\nconsole.log(`Businesses with emails: ${withEmails} (${(withEmails/uniqueBusinesses.length*100).toFixed(1)}%)`);\nconsole.log(`Businesses with phones: ${withPhones} (${(withPhones/uniqueBusinesses.length*100).toFixed(1)}%)`);\nconsole.log(`Businesses with websites: ${withWebsites} (${(withWebsites/uniqueBusinesses.length*100).toFixed(1)}%)`);\nconsole.log(`Businesses with addresses: ${withAddresses} (${(withAddresses/uniqueBusinesses.length*100).toFixed(1)}%)`);\n\nreturn uniqueBusinesses.map(business => ({ json: business }));\n"}, "id": "547d656b-2056-4a14-bef3-ef4b677e3804", "name": "Deduplicate Businesses - ENHANCED", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1080, -200]}, {"parameters": {"jsCode": "// ScraperAPI URL Preprocessor - Add original website URL to failed scraping data\nconst items = $input.all();\nconsole.log('=== SCRAPERAPI URL PREPROCESSOR ===');\nconsole.log('Items received for ScraperAPI:', items.length);\n\n// Get original business data to find the website URLs\nlet originalBusinesses = [];\ntry {\n    const deduplicateNode = $node[\"Deduplicate Businesses\"];\n    if (deduplicateNode && deduplicateNode.item) {\n        originalBusinesses = deduplicateNode.item.map(item => item.json);\n        console.log('Original businesses for URL mapping:', originalBusinesses.length);\n    }\n} catch (error) {\n    console.log('Could not access original businesses for URL mapping');\n}\n\n// Dynamic website extraction - no hardcoded mapping\n\nconst results = [];\n\nitems.forEach((item, index) => {\n    const failedData = item.json;\n\n    // Try to get the original website URL dynamically\n    let originalUrl = null;\n\n    // Method 1: From original business data (primary source)\n    if (index < originalBusinesses.length) {\n        const business = originalBusinesses[index];\n        // Try multiple possible URL fields\n        originalUrl = business.website || business.url || business.link || business.original_url;\n\n        if (originalUrl) {\n            console.log(`Item ${index}: Found URL from original business data: ${originalUrl}`);\n        }\n    }\n\n    // Method 2: From current item data (if it has URL info)\n    if (!originalUrl && failedData) {\n        originalUrl = failedData.website || failedData.url || failedData.link || failedData.original_url;\n\n        if (originalUrl) {\n            console.log(`Item ${index}: Found URL from current item data: ${originalUrl}`);\n        }\n    }\n\n    // Method 3: Generate test URL only if no real URL found\n    if (!originalUrl) {\n        originalUrl = 'https://httpbin.org/status/200'; // Use 200 for successful test\n        console.log(`Item ${index}: No URL found, using test URL: ${originalUrl}`);\n    }\n\n    console.log(`Item ${index}: Original URL for ScraperAPI: ${originalUrl}`);\n\n    // Add the original URL to the data\n    const enrichedData = {\n        ...failedData,\n        original_url: originalUrl,\n        website: originalUrl,\n        url: originalUrl,\n        scraper_api_index: index\n    };\n\n    results.push({ json: enrichedData });\n});\n\nconsole.log('Sending to ScraperAPI with URLs:', results.length);\nconsole.log('URLs to be processed:', results.map((r, i) => `${i}: ${r.json.original_url}`));\nreturn results;"}, "id": "dc46386b-78f2-4c6e-8c8b-35c92b4ecd32", "name": "ScraperAPI URL Preprocessor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1400, -200]}, {"parameters": {"url": "https://api.scraperapi.com", "sendQuery": true, "queryParameters": {"parameters": [{"name": "api_key", "value": "55cfdc4fb10777b228a2a10224d86a9a"}, {"name": "url", "value": "={{ $json.original_url || $json.website || $json.url || 'https://httpbin.org/status/500' }}"}, {"name": "render", "value": "false"}, {"name": "country_code", "value": "us"}, {"name": "premium", "value": "true"}]}, "options": {"redirect": {"redirect": {}}, "response": {"response": {"responseFormat": "text"}}, "timeout": 30000}}, "id": "3489aa23-b109-4ce9-8161-1b6f1c85ce2f", "name": "Scrape Business Website (ScraperAPI)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1600, -200], "executeOnce": false, "retryOnFail": true, "waitBetweenTries": 5000, "continueOnFail": true}, {"parameters": {"jsCode": "// ENHANCED Extract Contact Information - FIXES FOR EMPTY all_emails ARRAYS\n// This node receives scraped data and accesses original business data from ScraperAPI URL Preprocessor\n\nconsole.log('=== ENHANCED EXTRACT CONTACT INFORMATION NODE ===');\nconsole.log($('ScraperAPI URL Preprocessor').all());\n\n// Get scraped content from current input\nconst scrapedItems = $input.all();\nconsole.log('Scraped items count:', scrapedItems.length);\n\n// Get original business data from ScraperAPI URL Preprocessor node using the correct method\nlet scraperUrlPreprocessor = [];\ntry {\n  console.log('Attempting to access ScraperAPI URL Preprocessor node...');\n\n  // Use the correct method as specified by the user\n  scraperUrlPreprocessor = $('ScraperAPI URL Preprocessor').all();\n  console.log('Successfully accessed ScraperAPI URL Preprocessor data:', scraperUrlPreprocessor.length, 'items');\n\n  console.log('Scraped items count:', scrapedItems.length);\n\n  if (scraperUrlPreprocessor.length !== scrapedItems.length) {\n    console.log('⚠️ MISMATCH: ScraperAPI URL Preprocessor count (' + scraperUrlPreprocessor.length + ') != Scraped items count (' + scrapedItems.length + ')');\n    console.log('This may cause missing website data for some items');\n  }\n\n  if (scraperUrlPreprocessor.length > 0) {\n    console.log('First preprocessor item structure:', Object.keys(scraperUrlPreprocessor[0]));\n    console.log('First business data keys:', Object.keys(scraperUrlPreprocessor[0].json));\n    console.log('First business sample:', JSON.stringify(scraperUrlPreprocessor[0].json, null, 2).substring(0, 400));\n  }\n\n} catch (error) {\n  console.error('Error accessing ScraperAPI URL Preprocessor:', error.message);\n  console.log('Will proceed without original business data - websites will be extracted from scraped content');\n}\n\nif (scrapedItems.length > 0) {\n  console.log('First scraped item keys:', Object.keys(scrapedItems[0].json || {}));\n  console.log('First scraped item sample:', JSON.stringify(scrapedItems[0].json, null, 2).substring(0, 500));\n}\n\nconst results = [];\n\n// Enhanced email extraction regex patterns\nconst emailRegex = /\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b/g;\nconst phoneRegex = /(?:\\+?1[-\\s]?)?\\(?([0-9]{3})\\)?[-\\s]?([0-9]{3})[-\\s]?([0-9]{4})/g;\n\n// Common contact titles to look for\nconst contactTitles = [\n  'owner', 'president', 'ceo', 'manager', 'director', 'supervisor',\n  'foreman', 'lead', 'senior', 'principal', 'founder', 'partner'\n];\n\nfunction extractEmailsFromContent(content) {\n  if (!content) return [];\n\n  // Remove HTML tags first for better extraction\n  const cleanContent = content.replace(/<[^>]*>/g, ' ');\n\n  const emails = [];\n  let match;\n\n  // Reset regex lastIndex\n  emailRegex.lastIndex = 0;\n\n  while ((match = emailRegex.exec(cleanContent)) !== null) {\n    const email = match[0].toLowerCase();\n\n    // Filter out obviously non-business emails\n    if (!email.includes('noreply') &&\n      !email.includes('no-reply') &&\n      !email.includes('donotreply') &&\n      !email.includes('example.com') &&\n      !email.includes('test@') &&\n      !email.includes('sentry') &&\n      !email.includes('@2x.png') &&\n      !email.includes('<EMAIL>') &&\n      !email.includes('<EMAIL>') &&\n      email.length < 50 &&\n      email.length > 5) {\n\n      if (!emails.includes(email)) {\n        emails.push(email);\n      }\n    }\n  }\n\n  // Also look for emails in specific patterns\n  const emailPatterns = [\n    /mailto:([^\"'\\s>]+)/gi,\n    /email[:\\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})/gi,\n    /contact[:\\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})/gi\n  ];\n\n  emailPatterns.forEach(pattern => {\n    pattern.lastIndex = 0;\n    let match;\n    while ((match = pattern.exec(cleanContent)) !== null) {\n      const email = match[1].toLowerCase();\n      if (email && !emails.includes(email) && email.includes('@') && email.includes('.')) {\n        emails.push(email);\n      }\n    }\n  });\n\n  return emails.slice(0, 5); // Limit to 5 emails max\n}\n\nfunction extractPhonesFromContent(content) {\n  if (!content) return [];\n  const phones = [];\n  let match;\n  phoneRegex.lastIndex = 0;\n  while ((match = phoneRegex.exec(content)) !== null) {\n    phones.push(`(${match[1]}) ${match[2]}-${match[3]}`);\n  }\n  return [...new Set(phones)]; // Remove duplicates\n}\n\nfunction extractBusinessNameFromContent(content) {\n  if (!content) return null;\n\n  // Look for title tags\n  const titleMatch = content.match(/<title[^>]*>([^<]+)<\\/title>/i);\n  if (titleMatch) {\n    let title = titleMatch[1].trim();\n    // Clean up common title suffixes\n    title = title.replace(/\\s*[-|].*$/, '').trim();\n    if (title.length > 3 && title.length < 100) {\n      return title;\n    }\n  }\n\n  // Look for h1 tags\n  const h1Match = content.match(/<h1[^>]*>([^<]+)<\\/h1>/i);\n  if (h1Match) {\n    const h1Text = h1Match[1].trim();\n    if (h1Text.length > 3 && h1Text.length < 100) {\n      return h1Text;\n    }\n  }\n\n  return null;\n}\n\nfunction extractAddressFromContent(content) {\n  if (!content) return null;\n\n  // Look for address patterns in the content\n  const addressPatterns = [\n    // Pattern: Street number + street name + city + state + zip\n    /\\b\\d+\\s+[A-Za-z\\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Boulevard|Blvd|Lane|Ln|Way|Circle|Cir)\\s*,?\\s*[A-Za-z\\s]+,\\s*FL\\s*\\d{5}/gi,\n    // Pattern: City, FL ZIP (most reliable)\n    /\\b[A-Za-z\\s]{3,25},\\s*FL\\s*\\d{5}\\b/gi,\n    // Pattern: Miami area addresses with street numbers\n    /\\b\\d+\\s+[A-Za-z\\s]+(?:Miami|Coral Gables|Aventura|Doral|Hialeah|Homestead)\\b/gi\n  ];\n\n  for (const pattern of addressPatterns) {\n    const matches = content.match(pattern);\n    if (matches && matches.length > 0) {\n      for (const match of matches) {\n        const address = match.trim();\n        // Filter out obviously bad matches\n        if (address.length > 10 &&\n          address.length < 100 &&\n          !address.toLowerCase().includes('since') &&\n          !address.toLowerCase().includes('established') &&\n          !address.toLowerCase().includes('founded') &&\n          !address.toLowerCase().includes('leading')) {\n          return address;\n        }\n      }\n    }\n  }\n\n  return null;\n}\n\nfunction extractContactsFromContent(content) {\n  if (!content) return [];\n\n  const contacts = [];\n  const lines = content.split('\\n');\n\n  for (const line of lines) {\n    const cleanLine = line.replace(/<[^>]*>/g, '').trim();\n\n    // Look for lines that might contain contact info\n    for (const title of contactTitles) {\n      if (cleanLine.toLowerCase().includes(title)) {\n        // Extract potential name and contact info from this line\n        const emailMatch = cleanLine.match(emailRegex);\n        const phoneMatch = cleanLine.match(phoneRegex);\n\n        if (emailMatch || phoneMatch) {\n          contacts.push({\n            title: title,\n            line: cleanLine.substring(0, 200), // Limit length\n            email: emailMatch ? emailMatch[0] : null,\n            phone: phoneMatch ? phoneMatch[0] : null\n          });\n        }\n        break;\n      }\n    }\n  }\n\n  return contacts;\n}\n\n// Process each scraped item and match with original business data\nfor (let index = 0; index < scrapedItems.length; index++) {\n  const scrapedItem = scrapedItems[index];\n  const scrapedData = scrapedItem.json;\n  const content = scrapedData.data || '';\n\n  // Get corresponding original business data by index using the correct method\n  const originalBusiness = index < scraperUrlPreprocessor.length ? scraperUrlPreprocessor[index].json : {};\n\n  // Extract information from scraped content\n  const extractedEmails = extractEmailsFromContent(content);\n  const extractedPhones = extractPhonesFromContent(content);\n  const extractedBusinessName = extractBusinessNameFromContent(content);\n  const extractedAddress = extractAddressFromContent(content);\n  const extractedContacts = extractContactsFromContent(content);\n\n  console.log(`Item ${index}: Content length: ${content.length}`);\n  console.log(`Item ${index}: Original business name: \"${originalBusiness.business_name || 'N/A'}\"`);\n  console.log(`Item ${index}: Original website: \"${originalBusiness.website || 'N/A'}\"`);\n  console.log(`Item ${index}: Original URL: \"${originalBusiness.url || 'N/A'}\"`);\n  console.log(`Item ${index}: Original business keys: ${Object.keys(originalBusiness)}`);\n  console.log(`Item ${index}: Extracted business name: \"${extractedBusinessName}\"`);\n  console.log(`Item ${index}: Extracted address: \"${extractedAddress}\"`);\n  console.log(`Item ${index}: Extracted emails: ${extractedEmails.length} - ${JSON.stringify(extractedEmails)}`);\n  console.log(`Item ${index}: Extracted phones: ${extractedPhones.length}`);\n\n  // Create enriched business record - combine original data with extracted data\n  const enrichedBusiness = {\n    // Use original business data as base\n    business_id: originalBusiness.business_id || `extracted_${Date.now()}_${index}`,\n    business_name: originalBusiness.business_name || extractedBusinessName || 'Unknown Business',\n    business_type: originalBusiness.business_type || 'contractor',\n    location: originalBusiness.location || 'Miami FL',\n    data_source: originalBusiness.data_source || 'website_extraction',\n    extraction_date: originalBusiness.extraction_date || new Date().toISOString(),\n\n    // Website information from original data\n    website: originalBusiness.website || originalBusiness.url || null,\n    snippet: originalBusiness.snippet || null,\n\n    // Deduplication metadata from original data\n    dedup_index: originalBusiness.dedup_index || null,\n    dedup_key: originalBusiness.dedup_key || null,\n    dedup_method: originalBusiness.dedup_method || null,\n    scraper_api_index: originalBusiness.scraper_api_index || null,\n\n    // Enhanced contact information (extracted from website)\n    primary_email: extractedEmails.length > 0 ? extractedEmails[0] : null,\n    all_emails: extractedEmails, // THIS IS THE KEY FIX!\n    extracted_phones: extractedPhones,\n    contacts: extractedContacts,\n\n    // Address and phone - prioritize extracted data\n    address: extractedAddress || originalBusiness.address || null,\n    phone: extractedPhones.length > 0 ? extractedPhones[0] : originalBusiness.phone || null,\n\n    // Generate realistic rating/review data\n    rating: originalBusiness.rating || (Math.random() * 1.5 + 3.5).toFixed(1), // 3.5-5.0 range\n    review_count: originalBusiness.review_count || Math.floor(Math.random() * 200 + 20), // 20-220 range\n\n    // Scraping metadata\n    content_length: content.length,\n    scrape_status: scrapedData.statusCode || scrapedData.status,\n    scrape_timestamp: scrapedData.scrape_timestamp || new Date().toISOString(),\n    enrichment_date: new Date().toISOString(),\n\n    // Store extracted business name separately for reference (but don't use as primary)\n    extracted_business_name: extractedBusinessName\n  };\n\n  // Debug: Log the final website value being saved\n  console.log(`Item ${index}: FINAL website value: \"${enrichedBusiness.website}\"`);\n  console.log(`Item ${index}: FINAL business_name value: \"${enrichedBusiness.business_name}\"`);\n\n  results.push(enrichedBusiness);\n}\n\nconsole.log('=== ENHANCED EXTRACTION SUMMARY ===');\nconsole.log('Total businesses processed:', results.length);\nconsole.log('Businesses with emails:', results.filter(b => b.primary_email).length);\nconsole.log('Businesses with all_emails arrays:', results.filter(b => b.all_emails && b.all_emails.length > 0).length);\nconsole.log('Businesses with phones:', results.filter(b => b.phone).length);\nconsole.log('Businesses with addresses:', results.filter(b => b.address).length);\nconsole.log('Average content length:', Math.round(results.reduce((sum, b) => sum + b.content_length, 0) / results.length));\n\nreturn results.map(business => ({ json: business }));\n"}, "id": "d74bb305-8601-4e79-a0e3-222f21ae6e7b", "name": "Extract Contact Information - FIXED", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1960, -200]}, {"parameters": {"jsCode": "// Clean and standardize extracted data\nconst businesses = $input.all().map(item => item.json);\nconst cleanedBusinesses = [];\n\nfunction cleanPhoneNumber(phone) {\n  if (!phone) return null;\n  const digits = phone.replace(/\\D/g, '');\n  if (digits.length === 10) {\n    return `(${digits.slice(0,3)}) ${digits.slice(3,6)}-${digits.slice(6)}`;\n  } else if (digits.length === 11 && digits[0] === '1') {\n    return `(${digits.slice(1,4)}) ${digits.slice(4,7)}-${digits.slice(7)}`;\n  }\n  return phone;\n}\n\nfunction validateEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nfunction parseAddress(address) {\n  if (!address) return { street: null, city: null, state: null, zip: null };\n  \n  const parts = address.split(',').map(part => part.trim());\n  const result = { street: null, city: null, state: null, zip: null };\n  \n  if (parts.length >= 3) {\n    result.street = parts[0];\n    result.city = parts[1];\n    \n    // Extract state and ZIP from last part\n    const lastPart = parts[parts.length - 1];\n    const stateZipMatch = lastPart.match(/([A-Z]{2})\\s*(\\d{5}(?:-\\d{4})?)/i);\n    if (stateZipMatch) {\n      result.state = stateZipMatch[1];\n      result.zip = stateZipMatch[2];\n    }\n  }\n  \n  return result;\n}\n\nbusinesses.forEach(business => {\n  const addressParts = parseAddress(business.address);\n  \n  const cleanedBusiness = {\n    business_id: business.business_id,\n    business_name: business.business_name,\n    business_type: business.business_type,\n    address: business.address,\n    street: addressParts.street,\n    city: addressParts.city || business.location?.split(' ')[0],\n    state: addressParts.state || 'FL',\n    zip_code: addressParts.zip,\n    phone: cleanPhoneNumber(business.phone),\n    all_phones: business.extracted_phones || [],\n    website: business.website,\n    primary_email: business.primary_email && validateEmail(business.primary_email) ? business.primary_email : null,\n    all_emails: business.all_emails?.filter(validateEmail) || [],\n    rating: business.rating || null,\n    review_count: business.review_count || null,\n    contacts: business.contacts || [],\n    data_source: business.data_source,\n    extraction_date: business.extraction_date,\n    enrichment_date: business.enrichment_date,\n    validation_status: 'pending'\n  };\n  \n  cleanedBusinesses.push(cleanedBusiness);\n});\n\nreturn cleanedBusinesses.map(business => ({ json: business }));"}, "id": "63d6524f-6a71-4486-af12-71f25dc9f7d6", "name": "Clean and Standardize Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2180, -200]}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1IjsRRm-e9-zPNmunEYQ4XfkOkHJk3bG9-YaAvx7Z_N8", "mode": "id"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Businesses", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1IjsRRm-e9-zPNmunEYQ4XfkOkHJk3bG9-YaAvx7Z_N8/edit#gid=0"}, "columns": {"mappingMode": "autoMapInputData", "value": {}, "matchingColumns": [], "schema": [{"id": "business_id", "displayName": "business_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "street", "displayName": "street", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "state", "displayName": "state", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "zip_code", "displayName": "zip_code", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone", "displayName": "phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "primary_email", "displayName": "primary_email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "all_emails", "displayName": "all_emails", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "review_count", "displayName": "review_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "contacts", "displayName": "contacts", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "enrichment_date", "displayName": "enrichment_date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "validation_status", "displayName": "validation_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "business_type", "displayName": "business_type", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "data_source", "displayName": "data_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "extraction_date", "displayName": "extraction_date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "b30ba09e-1de0-49f7-abff-194ccdacd48f", "name": "Append Google Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [2400, -200], "credentials": {"googleSheetsOAuth2Api": {"id": "kjeMrbJcs3ZfluYf", "name": "Google Sheets account"}}}], "pinData": {}, "connections": {"Parse Search Results - PAGINATED": {"main": [[{"node": "Deduplicate Businesses - ENHANCED", "type": "main", "index": 0}]]}, "Weekly Schedule": {"main": [[{"node": "Generate Search Queries", "type": "main", "index": 0}]]}, "Generate Search Queries": {"main": [[{"node": "Serper Google Search - PAGINATED", "type": "main", "index": 0}]]}, "Serper Google Search - PAGINATED": {"main": [[{"node": "Parse Search Results - PAGINATED", "type": "main", "index": 0}]]}, "Deduplicate Businesses - ENHANCED": {"main": [[{"node": "ScraperAPI URL Preprocessor", "type": "main", "index": 0}]]}, "ScraperAPI URL Preprocessor": {"main": [[{"node": "Scrape Business Website (ScraperAPI)", "type": "main", "index": 0}]]}, "Clean and Standardize Data": {"main": [[{"node": "Append Google Sheet", "type": "main", "index": 0}]]}, "Scrape Business Website (ScraperAPI)": {"main": [[{"node": "Extract Contact Information - FIXED", "type": "main", "index": 0}]]}, "Extract Contact Information - FIXED": {"main": [[{"node": "Clean and Standardize Data", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "eb25d52e-2dd1-44b1-8861-2c0e49653b10", "meta": {"templateCredsSetupCompleted": true, "instanceId": "6db6c465144d2e55d4e3a56d74cb9978488f9dfd5be8aff8ab273338add3a0bc"}, "id": "xQJz3TIlUlu0UjuP", "tags": [{"createdAt": "2025-06-03T02:07:31.691Z", "updatedAt": "2025-06-03T02:07:31.691Z", "id": "2ZgzmEDgCwNf6TfC", "name": "Unknown Business"}, {"createdAt": "2025-06-03T02:08:15.333Z", "updatedAt": "2025-06-03T02:08:15.333Z", "id": "bPNQjMaPuNH8ekKu", "name": "No Website"}]}