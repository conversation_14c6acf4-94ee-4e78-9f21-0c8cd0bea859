#!/usr/bin/env python3
"""
Generate Detailed Summary for Deduplicated Professional Services Records
========================================================================

This script generates a comprehensive summary of the deduplicated records.

Author: Augment Agent
Date: 2025-06-26
"""

import pandas as pd
from datetime import datetime

def generate_detailed_summary():
    """Generate detailed summary of deduplicated records."""
    
    print("Loading deduplicated records...")
    df = pd.read_csv("rank_professional_services_deduplicated_records.csv", low_memory=False)
    
    # Generate summary report
    report_content = []
    report_content.append("DEDUPLICATED PROFESSIONAL SERVICES RECORDS SUMMARY")
    report_content.append("=" * 55)
    report_content.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_content.append("")
    
    # Overall statistics
    report_content.append("OVERALL STATISTICS")
    report_content.append("-" * 18)
    report_content.append(f"Total deduplicated records: {len(df):,}")
    report_content.append(f"Records with domains: {df['domain'].notna().sum():,} ({df['domain'].notna().sum()/len(df)*100:.1f}%)")
    report_content.append(f"Records with info@ emails: {df['info@ email'].notna().sum():,} ({df['info@ email'].notna().sum()/len(df)*100:.1f}%)")
    report_content.append(f"Records with phone numbers: {df['phoneNumber'].notna().sum():,} ({df['phoneNumber'].notna().sum()/len(df)*100:.1f}%)")
    report_content.append(f"Records with addresses: {df['address'].notna().sum():,} ({df['address'].notna().sum()/len(df)*100:.1f}%)")
    report_content.append("")
    
    # Breakdown by category
    report_content.append("BREAKDOWN BY SERVICE CATEGORY")
    report_content.append("-" * 30)
    category_counts = df['source_category'].value_counts()
    for i, (category, count) in enumerate(category_counts.items(), 1):
        percentage = count / len(df) * 100
        report_content.append(f"{i:2d}. {category}: {count:,} records ({percentage:.1f}%)")
    report_content.append("")
    
    # Top domains
    report_content.append("TOP 20 DOMAINS")
    report_content.append("-" * 13)
    domain_counts = df['domain'].value_counts().head(20)
    for i, (domain, count) in enumerate(domain_counts.items(), 1):
        report_content.append(f"{i:2d}. {domain}: {count} records")
    report_content.append("")
    
    # Geographic distribution (top states)
    report_content.append("TOP 20 STATES (from addresses)")
    report_content.append("-" * 29)
    # Extract state from address (simple approach)
    states = df['address'].str.extract(r', ([A-Z]{2}) \d')[0].value_counts().head(20)
    for i, (state, count) in enumerate(states.items(), 1):
        percentage = count / len(df) * 100
        report_content.append(f"{i:2d}. {state}: {count:,} records ({percentage:.1f}%)")
    report_content.append("")
    
    # Data quality metrics
    report_content.append("DATA QUALITY METRICS")
    report_content.append("-" * 19)
    report_content.append(f"Records with ratings: {df['rating'].notna().sum():,} ({df['rating'].notna().sum()/len(df)*100:.1f}%)")
    report_content.append(f"Records with websites: {df['website'].notna().sum():,} ({df['website'].notna().sum()/len(df)*100:.1f}%)")
    report_content.append(f"Records with coordinates: {df[['latitude', 'longitude']].notna().all(axis=1).sum():,} ({df[['latitude', 'longitude']].notna().all(axis=1).sum()/len(df)*100:.1f}%)")
    report_content.append("")
    
    # File information
    report_content.append("OUTPUT FILE INFORMATION")
    report_content.append("-" * 23)
    report_content.append(f"Filename: rank_professional_services_deduplicated_records.csv")
    report_content.append(f"File size: ~510 MB")
    report_content.append(f"Columns: {len(df.columns)}")
    report_content.append(f"Column names: {', '.join(df.columns.tolist())}")
    
    # Save report
    report_text = '\n'.join(report_content)
    with open('deduplicated_records_summary.txt', 'w') as f:
        f.write(report_text)
    
    print("Summary report saved to: deduplicated_records_summary.txt")
    print("\nQuick Summary:")
    print(f"- Total records: {len(df):,}")
    print(f"- Records with domains: {df['domain'].notna().sum():,}")
    print(f"- Records with emails: {df['info@ email'].notna().sum():,}")
    print(f"- Top category: {category_counts.index[0]} ({category_counts.iloc[0]:,} records)")

if __name__ == "__main__":
    generate_detailed_summary()
