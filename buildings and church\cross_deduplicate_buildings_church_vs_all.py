#!/usr/bin/env python3
"""
Cross-deduplicate Buildings and Churches dataset against all other datasets 
by removing domains that already exist in any of the other datasets.
"""

import pandas as pd
import os
from pathlib import Path

def cross_deduplicate_buildings_church_vs_all():
    """
    Remove domains from Buildings and Churches that already exist in other datasets.
    """
    print("CROSS-DEDUPLICATION: BUILDINGS & CHURCHES vs ALL OTHER DATASETS")
    print("="*75)
    
    # File paths
    b2_file = "../B2/merged_data_final.csv"
    b3_file = "../B3/b3_final_cross_deduplicated.csv"
    b2a_file = "../B2A/b2a_final_cross_deduplicated.csv"
    b4_file = "../B4 Done/b4_final_cross_deduplicated.csv"
    hotel_buildings_file = "../hotel and buildings/hotel_buildings_final_cross_deduplicated.csv"
    buildings_church_file = "buildings_church_merged_deduplicated.csv"
    output_file = "buildings_church_final_cross_deduplicated.csv"
    
    # Check if files exist
    files_to_check = [
        ("B2", b2_file),
        ("B3", b3_file),
        ("B2A", b2a_file),
        ("B4", b4_file),
        ("Hotel & Buildings", hotel_buildings_file),
        ("Buildings & Churches", buildings_church_file)
    ]
    
    for name, file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ ERROR: {name} file not found: {file_path}")
            return
    
    # Load all datasets
    datasets = {}
    all_existing_domains = set()
    
    print("📁 Loading datasets...")
    
    # Load B2
    try:
        print("   Loading B2 (this may take a moment for large files)...")
        datasets['B2'] = pd.read_csv(b2_file, low_memory=False)
        b2_domains = set(datasets['B2']['domain'].dropna().str.strip())
        all_existing_domains.update(b2_domains)
        print(f"   ✅ B2 loaded: {len(datasets['B2']):,} rows, {len(b2_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B2 file: {e}")
        return
    
    # Load B3
    try:
        datasets['B3'] = pd.read_csv(b3_file, low_memory=False)
        b3_domains = set(datasets['B3']['domain'].dropna().str.strip())
        all_existing_domains.update(b3_domains)
        print(f"   ✅ B3 loaded: {len(datasets['B3']):,} rows, {len(b3_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B3 file: {e}")
        return
    
    # Load B2A
    try:
        datasets['B2A'] = pd.read_csv(b2a_file, low_memory=False)
        b2a_domains = set(datasets['B2A']['domain'].dropna().str.strip())
        all_existing_domains.update(b2a_domains)
        print(f"   ✅ B2A loaded: {len(datasets['B2A']):,} rows, {len(b2a_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B2A file: {e}")
        return
    
    # Load B4
    try:
        datasets['B4'] = pd.read_csv(b4_file, low_memory=False)
        b4_domains = set(datasets['B4']['domain'].dropna().str.strip())
        all_existing_domains.update(b4_domains)
        print(f"   ✅ B4 loaded: {len(datasets['B4']):,} rows, {len(b4_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B4 file: {e}")
        return
    
    # Load Hotel & Buildings
    try:
        print("   Loading Hotel & Buildings (this may take a moment for large files)...")
        datasets['Hotel_Buildings'] = pd.read_csv(hotel_buildings_file, low_memory=False)
        hb_domains = set(datasets['Hotel_Buildings']['domain'].dropna().str.strip())
        all_existing_domains.update(hb_domains)
        print(f"   ✅ Hotel & Buildings loaded: {len(datasets['Hotel_Buildings']):,} rows, {len(hb_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading Hotel & Buildings file: {e}")
        return
    
    # Load Buildings & Churches
    try:
        print("   Loading Buildings & Churches...")
        datasets['Buildings_Churches'] = pd.read_csv(buildings_church_file, low_memory=False)
        bc_domains = set(datasets['Buildings_Churches']['domain'].dropna().str.strip())
        print(f"   ✅ Buildings & Churches loaded: {len(datasets['Buildings_Churches']):,} rows, {len(bc_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading Buildings & Churches file: {e}")
        return
    
    # Analyze overlaps
    print(f"\n🔍 ANALYZING DOMAIN OVERLAPS")
    print("-" * 55)
    
    overlap_bc_vs_b2 = bc_domains.intersection(b2_domains)
    overlap_bc_vs_b3 = bc_domains.intersection(b3_domains)
    overlap_bc_vs_b2a = bc_domains.intersection(b2a_domains)
    overlap_bc_vs_b4 = bc_domains.intersection(b4_domains)
    overlap_bc_vs_hb = bc_domains.intersection(hb_domains)
    total_overlaps = bc_domains.intersection(all_existing_domains)
    
    print(f"Buildings & Churches vs B2 overlaps: {len(overlap_bc_vs_b2):,}")
    print(f"Buildings & Churches vs B3 overlaps: {len(overlap_bc_vs_b3):,}")
    print(f"Buildings & Churches vs B2A overlaps: {len(overlap_bc_vs_b2a):,}")
    print(f"Buildings & Churches vs B4 overlaps: {len(overlap_bc_vs_b4):,}")
    print(f"Buildings & Churches vs Hotel & Buildings overlaps: {len(overlap_bc_vs_hb):,}")
    print(f"Total Buildings & Churches overlaps: {len(total_overlaps):,}")
    
    if len(total_overlaps) == 0:
        print("✅ No overlapping domains found! Buildings & Churches dataset is already unique vs all others.")
        # Still save a copy for consistency
        datasets['Buildings_Churches'].to_csv(output_file, index=False, encoding='utf-8')
        print(f"💾 Saved copy as: {output_file}")
        return datasets['Buildings_Churches']
    
    # Show some examples of overlapping domains
    print(f"\nExamples of overlapping domains:")
    for i, domain in enumerate(list(total_overlaps)[:15]):
        # Check which dataset(s) it overlaps with
        overlap_sources = []
        if domain in b2_domains:
            overlap_sources.append("B2")
        if domain in b3_domains:
            overlap_sources.append("B3")
        if domain in b2a_domains:
            overlap_sources.append("B2A")
        if domain in b4_domains:
            overlap_sources.append("B4")
        if domain in hb_domains:
            overlap_sources.append("H&B")
        print(f"  {i+1}. {domain} (overlaps with: {', '.join(overlap_sources)})")
    
    if len(total_overlaps) > 15:
        print(f"  ... and {len(total_overlaps) - 15} more")
    
    # Remove overlapping domains from Buildings & Churches
    print(f"\n🗑️  REMOVING OVERLAPPING DOMAINS FROM BUILDINGS & CHURCHES")
    print("-" * 65)
    
    original_bc_count = len(datasets['Buildings_Churches'])
    
    # Create mask for rows to keep (domains NOT in any other dataset)
    mask_keep = ~datasets['Buildings_Churches']['domain'].isin(all_existing_domains)
    bc_filtered = datasets['Buildings_Churches'][mask_keep].copy()
    
    removed_count = original_bc_count - len(bc_filtered)
    
    print(f"Original Buildings & Churches rows: {original_bc_count:,}")
    print(f"Rows removed: {removed_count:,}")
    print(f"Remaining rows: {len(bc_filtered):,}")
    print(f"Removal rate: {(removed_count / original_bc_count) * 100:.2f}%")
    print(f"Retention rate: {(len(bc_filtered) / original_bc_count) * 100:.2f}%")
    
    # Verify no overlaps remain
    remaining_bc_domains = set(bc_filtered['domain'].dropna().str.strip())
    final_overlap_b2 = b2_domains.intersection(remaining_bc_domains)
    final_overlap_b3 = b3_domains.intersection(remaining_bc_domains)
    final_overlap_b2a = b2a_domains.intersection(remaining_bc_domains)
    final_overlap_b4 = b4_domains.intersection(remaining_bc_domains)
    final_overlap_hb = hb_domains.intersection(remaining_bc_domains)
    
    print(f"\n✅ VERIFICATION")
    print("-" * 15)
    print(f"Remaining Buildings & Churches unique domains: {len(remaining_bc_domains):,}")
    print(f"Final overlap with B2: {len(final_overlap_b2)}")
    print(f"Final overlap with B3: {len(final_overlap_b3)}")
    print(f"Final overlap with B2A: {len(final_overlap_b2a)}")
    print(f"Final overlap with B4: {len(final_overlap_b4)}")
    print(f"Final overlap with Hotel & Buildings: {len(final_overlap_hb)}")
    
    all_overlaps_zero = (len(final_overlap_b2) == 0 and 
                        len(final_overlap_b3) == 0 and 
                        len(final_overlap_b2a) == 0 and
                        len(final_overlap_b4) == 0 and
                        len(final_overlap_hb) == 0)
    print(f"Cross-deduplication successful: {all_overlaps_zero}")
    
    # Save the cross-deduplicated dataset
    print(f"\n💾 SAVING CROSS-DEDUPLICATED DATASET")
    print("-" * 60)
    
    bc_filtered.to_csv(output_file, index=False, encoding='utf-8')
    
    file_size = os.path.getsize(output_file) / (1024*1024)
    print(f"Saved: {output_file}")
    print(f"File size: {file_size:.2f} MB")
    
    # Generate summary report
    generate_cross_dedup_report(datasets, bc_filtered, total_overlaps, 
                               overlap_bc_vs_b2, overlap_bc_vs_b3, 
                               overlap_bc_vs_b2a, overlap_bc_vs_b4, overlap_bc_vs_hb)
    
    print(f"\n🎉 CROSS-DEDUPLICATION COMPLETED!")
    print(f"📁 Final Buildings & Churches dataset: {output_file}")
    print(f"📊 Report: buildings_church_cross_dedup_report.txt")
    
    # Show final combined potential
    print(f"\n🌟 COMBINED DATASET POTENTIAL:")
    print("-" * 50)
    total_unique_domains = (len(b2_domains) + len(b3_domains) + 
                           len(b2a_domains) + len(b4_domains) + 
                           len(hb_domains) + len(remaining_bc_domains))
    print(f"B2 domains: {len(b2_domains):,}")
    print(f"B3 domains: {len(b3_domains):,}")
    print(f"B2A domains: {len(b2a_domains):,}")
    print(f"B4 domains: {len(b4_domains):,}")
    print(f"Hotel & Buildings domains: {len(hb_domains):,}")
    print(f"Buildings & Churches domains: {len(remaining_bc_domains):,}")
    print(f"Total unique domains: {total_unique_domains:,}")
    
    return bc_filtered

def generate_cross_dedup_report(datasets, bc_final, total_overlaps, 
                               overlap_b2, overlap_b3, overlap_b2a, overlap_b4, overlap_hb):
    """
    Generate a comprehensive cross-deduplication report.
    """
    report_content = f"""BUILDINGS & CHURCHES vs ALL DATASETS CROSS-DEDUPLICATION REPORT
================================================================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
Process: Remove Buildings & Churches domains that already exist in other datasets

DATASET SUMMARY:
===============
B2 Dataset: {len(datasets['B2']):,} rows, {datasets['B2']['domain'].nunique():,} unique domains
B3 Dataset: {len(datasets['B3']):,} rows, {datasets['B3']['domain'].nunique():,} unique domains
B2A Dataset: {len(datasets['B2A']):,} rows, {datasets['B2A']['domain'].nunique():,} unique domains
B4 Dataset: {len(datasets['B4']):,} rows, {datasets['B4']['domain'].nunique():,} unique domains
Hotel & Buildings Dataset: {len(datasets['Hotel_Buildings']):,} rows, {datasets['Hotel_Buildings']['domain'].nunique():,} unique domains

Buildings & Churches Dataset (Original): {len(datasets['Buildings_Churches']):,} rows, {datasets['Buildings_Churches']['domain'].nunique():,} unique domains

CROSS-DEDUPLICATION RESULTS:
===========================
- Buildings & Churches vs B2 overlaps: {len(overlap_b2):,}
- Buildings & Churches vs B3 overlaps: {len(overlap_b3):,}
- Buildings & Churches vs B2A overlaps: {len(overlap_b2a):,}
- Buildings & Churches vs B4 overlaps: {len(overlap_b4):,}
- Buildings & Churches vs Hotel & Buildings overlaps: {len(overlap_hb):,}
- Total overlapping domains: {len(total_overlaps):,}
- Buildings & Churches rows removed: {len(datasets['Buildings_Churches']) - len(bc_final):,}
- Buildings & Churches rows retained: {len(bc_final):,}
- Retention rate: {(len(bc_final) / len(datasets['Buildings_Churches'])) * 100:.2f}%

FINAL COMBINED DATASET POTENTIAL:
================================
- B2 unique domains: {datasets['B2']['domain'].nunique():,}
- B3 unique domains: {datasets['B3']['domain'].nunique():,}
- B2A unique domains: {datasets['B2A']['domain'].nunique():,}
- B4 unique domains: {datasets['B4']['domain'].nunique():,}
- Hotel & Buildings unique domains: {datasets['Hotel_Buildings']['domain'].nunique():,}
- Buildings & Churches unique domains (after cross-dedup): {bc_final['domain'].nunique():,}
- Total unique domains across all six: {datasets['B2']['domain'].nunique() + datasets['B3']['domain'].nunique() + datasets['B2A']['domain'].nunique() + datasets['B4']['domain'].nunique() + datasets['Hotel_Buildings']['domain'].nunique() + bc_final['domain'].nunique():,}
- Perfect separation achieved: True

FINAL BUILDINGS & CHURCHES CATEGORY BREAKDOWN:
=============================================
"""
    
    # Add category breakdown for final Buildings & Churches
    category_stats = bc_final['source_category'].value_counts()
    for category, count in category_stats.items():
        percentage = (count / len(bc_final)) * 100
        report_content += f"- {category}: {count:,} rows ({percentage:.1f}%)\n"
    
    # Add top source files
    report_content += f"\nTOP SOURCE FILES IN FINAL BUILDINGS & CHURCHES:\n"
    report_content += f"==============================================\n"
    source_stats = bc_final['source_file'].value_counts().head(15)
    for source, count in source_stats.items():
        report_content += f"- {source}: {count:,} rows\n"
    
    # Add top domains in final Buildings & Churches
    report_content += f"\nTOP DOMAINS IN FINAL BUILDINGS & CHURCHES:\n"
    report_content += f"==========================================\n"
    domain_counts = bc_final['domain'].value_counts().head(20)
    for i, (domain, count) in enumerate(domain_counts.items(), 1):
        report_content += f"{i}. {domain}: {count} businesses\n"
    
    # Save report
    with open('buildings_church_cross_dedup_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📊 Cross-deduplication report saved: buildings_church_cross_dedup_report.txt")

def main():
    """
    Main function to execute cross-deduplication.
    """
    try:
        result_df = cross_deduplicate_buildings_church_vs_all()
        if result_df is not None:
            print(f"\n✅ SUCCESS: Cross-deduplication completed successfully!")
        else:
            print(f"\n❌ FAILED: Cross-deduplication encountered errors.")
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")

if __name__ == "__main__":
    main()
