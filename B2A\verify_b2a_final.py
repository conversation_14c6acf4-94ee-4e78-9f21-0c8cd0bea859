#!/usr/bin/env python3
"""
Final verification of B2A processing results.
"""

import pandas as pd
import os

def verify_b2a_final():
    print("B2A FINAL VERIFICATION")
    print("="*30)
    
    # Check all files exist
    files_to_check = [
        ("B2A merged", "b2a_merged_data.csv"),
        ("B2A deduplicated", "b2a_merged_deduplicated.csv"),
        ("B2A final", "b2a_final_cross_deduplicated.csv"),
        ("B2 final", "../B2/merged_data_final.csv"),
        ("B3 final", "../B3/b3_final_cross_deduplicated.csv")
    ]
    
    print("FILE EXISTENCE CHECK:")
    print("-" * 25)
    for name, path in files_to_check:
        if os.path.exists(path):
            size_mb = os.path.getsize(path) / (1024*1024)
            print(f"✅ {name}: {size_mb:.2f} MB")
        else:
            print(f"❌ {name}: NOT FOUND")
    
    # Load final datasets
    try:
        print(f"\nLOADING FINAL DATASETS:")
        print("-" * 25)
        
        b2_df = pd.read_csv("../B2/merged_data_final.csv", low_memory=False)
        b3_df = pd.read_csv("../B3/b3_final_cross_deduplicated.csv", low_memory=False)
        b2a_df = pd.read_csv("b2a_final_cross_deduplicated.csv", low_memory=False)
        
        print(f"B2 final: {len(b2_df):,} rows")
        print(f"B3 final: {len(b3_df):,} rows")
        print(f"B2A final: {len(b2a_df):,} rows")
        
    except Exception as e:
        print(f"❌ Error loading files: {e}")
        return
    
    # Extract domains
    b2_domains = set(b2_df['domain'].dropna())
    b3_domains = set(b3_df['domain'].dropna())
    b2a_domains = set(b2a_df['domain'].dropna())
    
    print(f"\nDOMAIN ANALYSIS:")
    print("-" * 20)
    print(f"B2 unique domains: {len(b2_domains):,}")
    print(f"B3 unique domains: {len(b3_domains):,}")
    print(f"B2A unique domains: {len(b2a_domains):,}")
    
    # Check for any overlaps (should be zero)
    overlap_b2_b3 = b2_domains.intersection(b3_domains)
    overlap_b2_b2a = b2_domains.intersection(b2a_domains)
    overlap_b3_b2a = b3_domains.intersection(b2a_domains)
    
    print(f"\nOVERLAP VERIFICATION:")
    print("-" * 25)
    print(f"B2 vs B3 overlap: {len(overlap_b2_b3)} domains")
    print(f"B2 vs B2A overlap: {len(overlap_b2_b2a)} domains")
    print(f"B3 vs B2A overlap: {len(overlap_b3_b2a)} domains")
    
    all_overlaps_zero = (len(overlap_b2_b3) == 0 and 
                        len(overlap_b2_b2a) == 0 and 
                        len(overlap_b3_b2a) == 0)
    
    print(f"Perfect separation achieved: {all_overlaps_zero}")
    
    # Combined statistics
    total_unique = len(b2_domains) + len(b3_domains) + len(b2a_domains)
    
    print(f"\nCOMBINED DATASET SUMMARY:")
    print("-" * 30)
    print(f"Total unique domains: {total_unique:,}")
    print(f"B2 contribution: {len(b2_domains):,} ({len(b2_domains)/total_unique*100:.1f}%)")
    print(f"B3 contribution: {len(b3_domains):,} ({len(b3_domains)/total_unique*100:.1f}%)")
    print(f"B2A contribution: {len(b2a_domains):,} ({len(b2a_domains)/total_unique*100:.1f}%)")
    
    # B2A source file breakdown
    print(f"\nB2A SOURCE FILE BREAKDOWN:")
    print("-" * 30)
    source_counts = b2a_df['source_file'].value_counts()
    for source, count in source_counts.items():
        percentage = (count / len(b2a_df)) * 100
        print(f"{source}: {count:,} ({percentage:.1f}%)")
    
    # Sample data from each dataset
    print(f"\nSAMPLE DATA FROM EACH DATASET:")
    print("-" * 35)
    
    print("B2 Sample:")
    for _, row in b2_df[['title', 'domain']].head(3).iterrows():
        print(f"  {row['title'][:40]}... -> {row['domain']}")
    
    print("B3 Sample:")
    for _, row in b3_df[['title', 'domain']].head(3).iterrows():
        print(f"  {row['title'][:40]}... -> {row['domain']}")
    
    print("B2A Sample:")
    for _, row in b2a_df[['title', 'domain']].head(3).iterrows():
        print(f"  {row['title'][:40]}... -> {row['domain']}")
    
    # Final status
    print(f"\n🎉 FINAL STATUS:")
    print("-" * 15)
    if all_overlaps_zero:
        print("✅ All datasets are perfectly separated")
        print("✅ Ready for combined use without duplicates")
        print(f"✅ Total addressable market: {total_unique:,} unique domains")
    else:
        print("❌ Some overlaps still exist - review needed")

if __name__ == "__main__":
    verify_b2a_final()
