{"id": "qhZvZVCoV3HLjRkq", "meta": {"instanceId": "a2b23892dd6989fda7c1209b381f5850373a7d2b85609624d7c2b7a092671d44", "templateCredsSetupCompleted": true}, "name": "Google Maps FULL", "tags": [], "nodes": [{"id": "c5d63d91-ffcc-4c05-a1ee-d78ca955fc85", "name": "Trigger - When User Sends Message", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-400, -60], "webhookId": "e5c0f357-c0a4-4ebc-9162-0382d8009539", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "e422761f-a662-4fef-81fe-de42cdb350fc", "name": "AI Agent - Lead Collection", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-160, -60], "parameters": {"options": {"systemMessage": "' UNIFIED AND OPTIMIZED PROMPT FOR DATA EXTRACTION VIA GOOGLE MAPS SCRAPER\n\n' --- 1. Task ---\n' - Collect high-quality professional leads from Google Maps, including:\n'   - Business name\n'   - Address\n'   - Phone number\n'   - Website\n'   - Email\n'   - Other relevant contact details\n' - Deliver organized, accurate, and actionable data.\n\n' --- 2. Context & Collaboration ---\n' - Tools & Sources:\n'   * Google Maps Scraper: Extracts data based on location, business type, and country code \n'     (ISO 3166 Alpha-2 in lowercase).\n'   * Website Scraper: Extracts data from provided URLs (the URL must be passed exactly as received, without quotation marks).\n'   * Google Sheets: Stores and retrieves previously extracted data.\n'   * Internet Search: Provides additional information if the scraping results are incomplete.\n' - Priorities: Accuracy and efficiency, avoiding unnecessary searches.\n\n' --- 3. Ethical Guidelines ---\n' - Only extract publicly accessible professional data.\n' - Do not collect or store personal/sensitive data.\n' - Adhere to scraping policies and data protection regulations.\n' - Error Handling:\n'   * In case of failure or incomplete results, suggest a retry, adjusted search parameters, or an alternative source.\n'   * If Google Sheets is unavailable, notify the user and propose workarounds.\n\n' --- 4. Constraints ---\n' - Country codes must follow the ISO 3166 Alpha-2 format in lowercase (e.g., \"fr\" for France).\n' - When using the Website Scraper, pass the URL exactly as provided, without quotation marks or modifications.\n' - Validate and correctly format all data (no duplicates or errors).\n' - Store results in Google Sheets in an organized and accessible manner.\n\n' --- 5. Final Requirements & Quality Checks ---\n' - Verification: Ensure the country code is always passed in lowercase to the Google Maps Scraper.\n' - URL: If a URL is provided, forward it directly to the Website Scraper without adding quotation marks.\n' - Existing Data: Check Google Sheets to see if the data is already available before performing new scraping.\n' - Supplementary: In case of partial results, propose using Internet Search to complete the information.\n\n' --- 6. Interaction ---\n' - If data already exists in Google Sheets, retrieve and present it to the user instead of launching a new scrape.\n' - If scraping fails or returns incomplete results, suggest alternative actions (e.g., web search, verifying the country code).\n\n' --- 7. Examples ---\n' BAD Example (Google Maps Scraper)\n'   User: \"Find coffee shops in Paris, France.\"\n'   AI: \"Extracting coffee shop data from Google Maps in France.\"\n'   > Issue: The country code \"fr\" was not provided.\n'\n' GOOD Example (Google Maps Scraper)\n'   User: \"Find coffee shops in Paris, France.\"\n'   AI:\n'     - \"Extracting coffee shop data from Google Maps in fr (France).\"\n'     - \"Scraped 50 businesses with names, addresses, phone numbers, and websites.\"\n'     - \"Storing results in Google Sheets under Lead_Generation_Paris_FR.\"\n'\n' BAD Example (Website Scraper)\n'   User: \"Scrape data from https://www.example.com/\"\n'   AI: \"Forwarding 'https://www.example.com/' to the Website Scraper.\"\n'   > Issue: Unnecessary quotation marks around the URL.\n'\n' GOOD Example (Website Scraper)\n'   User: \"Scrape data from https://www.example.com/\"\n'   AI:\n'     - \"Forwarding https://www.example.com to the Website Scraper.\"\n'     - \"Processing data extraction and storing results in Google Sheets.\"\n\n' --- 8. Output Format ---\n' - Responses should be concise and informative.\n' - Present data in a structured manner (e.g., business name, address, phone, website, etc.).\n' - If data already exists, clearly display the retrieved information from Google Sheets.\n\n' --- Additional Context & Details ---\n'\n' You interact with scraping APIs and databases to retrieve, update, and manage lead information.\n' Always pass country information using lowercase ISO 3166 Alpha-2 format when using the Google Maps Scraper.\n' If a URL is provided, it must be passed exactly as received, without quotation marks, to the Website Scraper.\n'\n' Known details:\n' You extract business names, addresses, phone numbers, websites, emails, and other relevant contact information.\n'\n' The URL must be passed exactly as provided (e.g., https://www.example.com/) without quotation marks or formatting changes.\n' Google Maps Scraper requires location, business type, and ISO 3166 Alpha-2 country codes to extract business listings.\n'\n' Context:\n' - System environment:\n'   You have direct integration with scraping tools, Internet search capabilities, and Google Sheets.\n'   You interact with scraping APIs and databases to retrieve, update, and manage lead information.\n'\n' Role:\n' You are a Lead Generation & Web Scraping Agent.\n' Your primary responsibility is to identify, collect, and organize relevant business leads by scraping websites, Google Maps, and performing Internet searches.\n' Ensure all extracted data is structured, accurate, and stored properly for easy access and analysis.\n' You have access to two scraping tools:\n'   1. Website Scraper – Requires only the raw URL to extract data from a specific website.\n'      - The URL must be passed exactly as provided (e.g., https://www.example.com/) without quotation marks or formatting changes.\n'   2. Google Maps Scraper – Requires location, business type, and ISO 3166 Alpha-2 country codes to extract business listings.\n\n' --- FINAL INSTRUCTIONS ---\n' 1. Adhere to all the directives and constraints above when extracting data from Google Maps (or other sources).\n' 2. Systematically check if data already exists in Google Sheets.\n' 3. In case of failure or partial results, propose an adjustment to the query or resort to Internet search.\n' 4. Ensure ethical compliance: only collect public data and do not store sensitive information.\n'\n' This prompt will guide the AI agent to efficiently extract and manage business data using Google Maps Scraper (and other mentioned tools)\n' while adhering to the structure, ISO country code standards, and ethical handling of information.\n"}}, "typeVersion": 1.8}, {"id": "8469f6f8-e56e-433f-8439-ac0f568b01b1", "name": "GPT-4o - Generate & Process Requests", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-360, 160], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "6h3DfVhNPw9I25nO", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "8f89996c-f5d1-48e3-8023-9c5d4c8db12a", "name": "Memory - Track Recent Context", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-180, 160], "parameters": {"contextWindowLength": 50}, "typeVersion": 1.3}, {"id": "ef32b577-47a2-489f-8b5a-3640126a0ff9", "name": "Tool - Scrape Google Maps Business Data", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [160, 160], "parameters": {"name": "extract_google_maps", "workflowId": {"__rl": true, "mode": "list", "value": "9rD7iD6sbXqDX44S", "cachedResultName": "Google Maps - sous 1 - Extract Google maps"}, "description": "Extract data from hundreds of places fast. Scrape Google Maps by keyword, category, location, URLs & other filters. Get addresses, contact info, opening hours, popular times, prices, menus & more. Export scraped data, run the scraper via API, schedule and monitor runs, or integrate with other tools.", "workflowInputs": {"value": {"city": "={{ $fromAI('city', ``, 'string') }}", "search": "={{ $fromAI('search', ``, 'string') }}", "countryCode": "={{ $fromAI('countryCode', ``, 'string') }}", "state/county": "={{ $fromAI('state_county', ``, 'string') }}"}, "schema": [{"id": "search", "type": "string", "display": true, "required": false, "displayName": "search", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "city", "type": "string", "display": true, "required": false, "displayName": "city", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "state/county", "type": "string", "display": true, "required": false, "displayName": "state/county", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "countryCode", "type": "string", "display": true, "removed": false, "required": false, "displayName": "countryCode", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "86a6eafe-9ffc-4d58-85f7-eef7171eeb8e", "name": "Fallback - <PERSON><PERSON> with <PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-20, 160], "parameters": {"name": "serper_search", "description": "Search the web using Serper.dev API to find additional business information and enrich lead data when primary scraping sources are incomplete", "httpRequestOptions": {"url": "https://google.serper.dev/search", "method": "POST", "headers": {"X-API-KEY": "={{ $credentials.serperApi.apiKey }}", "Content-Type": "application/json"}, "body": {"q": "={{ $fromAI('query', 'Search query for business information', 'string') }}", "num": 10}}, "placeholderDefinitions": {"values": [{"name": "query", "description": "Search query for finding business information"}]}}, "credentials": {"serperApi": {"id": "NEW_SERPER_CREDENTIAL_ID", "name": "Serper.dev account"}}, "typeVersion": 1.1}, {"id": "********-0409-4f2d-8105-9216f974f6a8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-780, -200], "parameters": {"width": 1300, "height": 540, "content": "# AI-Powered Lead Generation Workflow\n\nThis workflow extracts business data from Google Maps and associated websites using an AI agent.\n\n## Dependencies\n- **OpenAI API**\n- **Google Sheets API**\n- **Apify Actors**: Google Maps Scraper \n- **Apify Actors**: Website Content Crawler\n- **SerpAPI**: Used as a fallback to enrich data\n\n## External Setup Guide\n**Notion** : [Guide](https://automatisation.notion.site/GOOGLE-MAPS-SCRAPER-1cc3d6550fd98005a99cea02986e7b05)\n"}, "typeVersion": 1}, {"id": "0b42dfae-49e6-4117-8a5d-d1f396f22dcb", "name": "Tool - Crawl Business Website", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [340, 160], "parameters": {"name": "Website_Content_Crawler", "workflowId": {"__rl": true, "mode": "list", "value": "I7KceT8Mg1lW7BW4", "cachedResultName": "Google Maps - sous 2 - Extract Google"}, "description": "Crawl websites and extract text content to feed AI models, LLM applications, vector databases, or RAG pipelines. The Actor supports rich formatting using Markdown, cleans the HTML, downloads files, and integrates well with 🦜🔗 LangChain, LlamaIndex, and the wider LLM ecosystem.", "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "8a713b20-99e6-4df1-88ce-698ffc3c1e31", "name": "Trigger - On Subworkflow Start", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-460, 520], "parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"search\": \"carpenter\",\n  \"city\": \"san francisco\",\n  \"state/county\": \"california\",\n  \"countryCode\": \"us\"\n}"}, "typeVersion": 1.1}, {"id": "59af012d-de3f-4a44-b3ad-e587857b554d", "name": "Scrape Google Maps (via Apify)", "type": "n8n-nodes-base.httpRequest", "position": [-240, 520], "parameters": {"url": "https://api.apify.com/v2/acts/2Mdma1N6Fd0y3QEjR/run-sync-get-dataset-items", "method": "POST", "options": {}, "jsonBody": "={\n    \"city\": \"{{ $json.city }}\",\n    \"countryCode\": \"{{ $json.countryCode }}\",\n    \"locationQuery\": \"{{ $json.city }}\",\n    \"maxCrawledPlacesPerSearch\": 5,\n    \"searchStringsArray\": [\n        \"{{ $json.search }}\"\n    ],\n    \"skipClosedPlaces\": false\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer <token>"}]}}, "typeVersion": 4.2}, {"id": "b1c40871-aa57-4b15-9f19-34a07dc6c45f", "name": "Save Extracted Data to Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [-20, 520], "parameters": {"operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}, "documentId": {"__rl": true, "mode": "id", "value": "="}}, "credentials": {"googleSheetsOAuth2Api": {"id": "51us92xkOlrvArhV", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "679f2af5-0024-4a71-8c04-dcbccc8f00c8", "name": "Aggregate Business Listings", "type": "n8n-nodes-base.aggregate", "position": [200, 520], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "dff1191a-b9f4-4ba6-ba42-a479fab76a5b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-780, 380], "parameters": {"color": 4, "width": 1300, "height": 440, "content": "# 📍 Google Maps Extractor Subworkflow\n\nThis subworkflow handles business data extraction from Google Maps using the Apify Google Maps Scraper.\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Purpose\n- Automates the collection of business leads based on:\n  - Search term (e.g., plumber, agency)\n  - City and region\n  - ISO 3166 Alpha-2 country code"}, "typeVersion": 1}, {"id": "dd691f9c-15e2-4b4a-a6eb-8765905a2cb4", "name": "Scrape Website Content (via Apify)", "type": "n8n-nodes-base.httpRequest", "position": [-320, 1000], "parameters": {"url": "https://api.apify.com/v2/acts/aYG0l9s7dbB7j3gbS/run-sync-get-dataset-items", "method": "POST", "options": {}, "jsonBody": "={\n    \"aggressivePrune\": false,\n    \"clickElementsCssSelector\": \"[aria-expanded=\\\"false\\\"]\",\n    \"clientSideMinChangePercentage\": 15,\n    \"crawlerType\": \"playwright:adaptive\",\n    \"debugLog\": false,\n    \"debugMode\": false,\n    \"expandIframes\": true,\n    \"ignoreCanonicalUrl\": false,\n    \"keepUrlFragments\": false,\n    \"proxyConfiguration\": {\n        \"useApifyProxy\": true\n    },\n    \"readableTextCharThreshold\": 100,\n    \"removeCookieWarnings\": true,\n    \"removeElementsCssSelector\": \"nav, footer, script, style, noscript, svg, img[src^='data:'],\\n[role=\\\"alert\\\"],\\n[role=\\\"banner\\\"],\\n[role=\\\"dialog\\\"],\\n[role=\\\"alertdialog\\\"],\\n[role=\\\"region\\\"][aria-label*=\\\"skip\\\" i],\\n[aria-modal=\\\"true\\\"]\",\n    \"renderingTypeDetectionPercentage\": 10,\n    \"saveFiles\": false,\n    \"saveHtml\": false,\n    \"saveHtmlAsFile\": false,\n    \"saveMarkdown\": true,\n    \"saveScreenshots\": false,\n    \"startUrls\": [\n        {\n            \"url\": \"{{ $json.query }}\",\n            \"method\": \"GET\"\n        }\n    ],\n    \"useSitemaps\": false\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer **********************************************"}]}}, "typeVersion": 4.2}, {"id": "7cc813a7-e1a5-40fe-a76a-3e52438cf2f4", "name": "Save Website Data to Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [-100, 1000], "parameters": {"columns": {"value": {}, "schema": [{"id": "url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "crawl", "type": "string", "display": true, "removed": false, "required": false, "displayName": "crawl", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "metadata", "type": "string", "display": true, "removed": false, "required": false, "displayName": "metadata", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "screenshotUrl", "type": "string", "display": true, "removed": false, "required": false, "displayName": "screenshotUrl", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "markdown", "type": "string", "display": true, "removed": false, "required": false, "displayName": "markdown", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "debug", "type": "string", "display": true, "removed": false, "required": false, "displayName": "debug", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": **********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1JewfKbdS6gJhVFz0Maz6jpoDxQrByKyy77I5s7UvLD4/edit#gid=**********", "cachedResultName": "MYWEBBASE"}, "documentId": {"__rl": true, "mode": "list", "value": "1JewfKbdS6gJhVFz0Maz6jpoDxQrByKyy77I5s7UvLD4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1JewfKbdS6gJhVFz0Maz6jpoDxQrByKyy77I5s7UvLD4/edit?usp=drivesdk", "cachedResultName": "GoogleMaps_LEADS"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "51us92xkOlrvArhV", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "6c20b427-48e8-4ba1-885e-7db71406a0db", "name": "Aggregate Website Content", "type": "n8n-nodes-base.aggregate", "position": [120, 1000], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "7b1ff556-f212-4ab5-9995-deeb83f68da4", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-780, 860], "parameters": {"color": 5, "width": 1300, "height": 400, "content": "# 🌐 Website Content Crawler Subworkflow\n\nThis subworkflow processes URLs to extract readable website content using Apify's Website Content Crawler.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Purpose\n- Extracts detailed and structured content from business websites.\n- Enhances leads with enriched, on-site information."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "fd75b3e6-1dba-4e01-8c95-fdd9dd07fac4", "connections": {"Memory - Track Recent Context": {"ai_memory": [[{"node": "AI Agent - Lead Collection", "type": "ai_memory", "index": 0}]]}, "Tool - Crawl Business Website": {"ai_tool": [[{"node": "AI Agent - Lead Collection", "type": "ai_tool", "index": 0}]]}, "Scrape Google Maps (via Apify)": {"main": [[{"node": "Save Extracted Data to Google Sheets", "type": "main", "index": 0}]]}, "Trigger - On Subworkflow Start": {"main": [[{"node": "Scrape Google Maps (via Apify)", "type": "main", "index": 0}]]}, "Trigger - When User Sends Message": {"main": [[{"node": "AI Agent - Lead Collection", "type": "main", "index": 0}]]}, "Save Website Data to Google Sheets": {"main": [[{"node": "Aggregate Website Content", "type": "main", "index": 0}]]}, "Scrape Website Content (via Apify)": {"main": [[{"node": "Save Website Data to Google Sheets", "type": "main", "index": 0}]]}, "Fallback - Enrich with Serper Search": {"ai_tool": [[{"node": "AI Agent - Lead Collection", "type": "ai_tool", "index": 0}]]}, "GPT-4o - Generate & Process Requests": {"ai_languageModel": [[{"node": "AI Agent - Lead Collection", "type": "ai_languageModel", "index": 0}]]}, "Save Extracted Data to Google Sheets": {"main": [[{"node": "Aggregate Business Listings", "type": "main", "index": 0}]]}, "Tool - Scrape Google Maps Business Data": {"ai_tool": [[{"node": "AI Agent - Lead Collection", "type": "ai_tool", "index": 0}]]}}}