// ENHANCED CONTACT PERSON EXTRACTION NODE
// This node focuses specifically on extracting contact person names and titles
// Add this as a new Code node in your n8n workflow after the website scraping

console.log('=== ENHANCED CONTACT PERSON EXTRACTION NODE ===');

const scrapedItems = $input.all();
console.log('Processing items for contact person extraction:', scrapedItems.length);

const results = [];

// Enhanced contact titles and variations
const contactTitles = [
  // Executive titles
  'owner', 'president', 'ceo', 'chief executive officer', 'founder', 'co-founder',
  'managing director', 'executive director', 'general manager', 'operations manager',
  
  // Management titles
  'manager', 'director', 'supervisor', 'foreman', 'lead', 'senior', 'principal',
  'project manager', 'site manager', 'office manager', 'business manager',
  
  // Sales and customer service
  'sales manager', 'sales director', 'account manager', 'customer service manager',
  'business development', 'sales representative', 'account executive',
  
  // Technical titles
  'estimator', 'project coordinator', 'field supervisor', 'crew leader',
  'master electrician', 'master plumber', 'licensed contractor', 'journeyman',
  
  // Other common titles
  'partner', 'vice president', 'vp', 'assistant manager', 'team lead'
];

// Name patterns to help identify person names
const namePatterns = [
  // First Last format
  /\b([A-Z][a-z]{2,15})\s+([A-Z][a-z]{2,20})\b/g,
  // First Middle Last format
  /\b([A-Z][a-z]{2,15})\s+([A-Z][a-z]{1,15})\s+([A-Z][a-z]{2,20})\b/g,
  // First Last Jr./Sr. format
  /\b([A-Z][a-z]{2,15})\s+([A-Z][a-z]{2,20})\s+(Jr\.?|Sr\.?|III?|IV)\b/g
];

// Common non-name words to filter out
const excludeWords = [
  'company', 'construction', 'services', 'contractors', 'roofing', 'plumbing',
  'electrical', 'hvac', 'miami', 'florida', 'contact', 'about', 'home', 'page',
  'business', 'professional', 'licensed', 'insured', 'quality', 'experience',
  'years', 'serving', 'residential', 'commercial', 'free', 'estimate', 'call',
  'today', 'schedule', 'appointment', 'service', 'repair', 'installation'
];

function extractContactPersons(content) {
  if (!content) return [];
  
  // Remove HTML tags and normalize whitespace
  const cleanContent = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ');
  
  const contacts = [];
  const foundNames = new Set();
  
  // Method 1: Look for structured contact sections
  const contactSections = extractFromContactSections(cleanContent);
  contactSections.forEach(contact => {
    if (contact.name && !foundNames.has(contact.name.toLowerCase())) {
      contacts.push(contact);
      foundNames.add(contact.name.toLowerCase());
    }
  });
  
  // Method 2: Look for title + name patterns
  const titleNameContacts = extractTitleNamePatterns(cleanContent);
  titleNameContacts.forEach(contact => {
    if (contact.name && !foundNames.has(contact.name.toLowerCase())) {
      contacts.push(contact);
      foundNames.add(contact.name.toLowerCase());
    }
  });
  
  // Method 3: Look for email signatures
  const emailSignatures = extractFromEmailSignatures(cleanContent);
  emailSignatures.forEach(contact => {
    if (contact.name && !foundNames.has(contact.name.toLowerCase())) {
      contacts.push(contact);
      foundNames.add(contact.name.toLowerCase());
    }
  });
  
  // Method 4: Look for "Meet the Team" or "About Us" sections
  const teamMembers = extractFromTeamSections(cleanContent);
  teamMembers.forEach(contact => {
    if (contact.name && !foundNames.has(contact.name.toLowerCase())) {
      contacts.push(contact);
      foundNames.add(contact.name.toLowerCase());
    }
  });
  
  return contacts.slice(0, 10); // Limit to 10 contacts max
}

function extractFromContactSections(content) {
  const contacts = [];
  
  // Look for contact sections
  const contactSectionRegex = /(?:contact|about|team|staff|management|leadership)[\s\S]{0,500}?(?=contact|about|team|staff|management|leadership|$)/gi;
  const sections = content.match(contactSectionRegex) || [];
  
  sections.forEach(section => {
    // Look for name + title combinations in contact sections
    const lines = section.split(/[.\n\r]/).filter(line => line.trim().length > 10);
    
    lines.forEach(line => {
      const contact = extractContactFromLine(line);
      if (contact) {
        contact.source = 'contact_section';
        contacts.push(contact);
      }
    });
  });
  
  return contacts;
}

function extractTitleNamePatterns(content) {
  const contacts = [];
  
  // Pattern: Title followed by name
  contactTitles.forEach(title => {
    const titleRegex = new RegExp(`\\b${title}[:\\s,]*([A-Z][a-z]{2,15}(?:\\s+[A-Z][a-z]{1,15})?\\s+[A-Z][a-z]{2,20})`, 'gi');
    const matches = content.match(titleRegex);
    
    if (matches) {
      matches.forEach(match => {
        const nameMatch = match.match(/([A-Z][a-z]{2,15}(?:\s+[A-Z][a-z]{1,15})?\s+[A-Z][a-z]{2,20})/);
        if (nameMatch && isValidName(nameMatch[1])) {
          contacts.push({
            name: nameMatch[1].trim(),
            title: title,
            source: 'title_name_pattern',
            context: match.trim()
          });
        }
      });
    }
  });
  
  return contacts;
}

function extractFromEmailSignatures(content) {
  const contacts = [];
  
  // Look for email signature patterns
  const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
  const emails = content.match(emailRegex) || [];
  
  emails.forEach(email => {
    // Look for name and title near the email
    const emailIndex = content.indexOf(email);
    const contextBefore = content.substring(Math.max(0, emailIndex - 200), emailIndex);
    const contextAfter = content.substring(emailIndex, Math.min(content.length, emailIndex + 200));
    const context = contextBefore + email + contextAfter;
    
    const contact = extractContactFromLine(context);
    if (contact) {
      contact.email = email;
      contact.source = 'email_signature';
      contacts.push(contact);
    }
  });
  
  return contacts;
}

function extractFromTeamSections(content) {
  const contacts = [];
  
  // Look for team/about sections
  const teamSectionRegex = /(?:meet\s+(?:the\s+)?team|our\s+team|about\s+us|leadership|management|staff)[\s\S]{0,1000}?(?=meet\s+(?:the\s+)?team|our\s+team|about\s+us|leadership|management|staff|$)/gi;
  const sections = content.match(teamSectionRegex) || [];
  
  sections.forEach(section => {
    // Look for name patterns in team sections
    namePatterns.forEach(pattern => {
      pattern.lastIndex = 0;
      let match;
      while ((match = pattern.exec(section)) !== null) {
        const fullName = match[0];
        if (isValidName(fullName)) {
          // Look for title near the name
          const nameIndex = section.indexOf(fullName);
          const contextBefore = section.substring(Math.max(0, nameIndex - 100), nameIndex);
          const contextAfter = section.substring(nameIndex, Math.min(section.length, nameIndex + 100));
          
          const title = findTitleInContext(contextBefore + contextAfter);
          
          contacts.push({
            name: fullName.trim(),
            title: title || 'Team Member',
            source: 'team_section',
            context: (contextBefore + fullName + contextAfter).substring(0, 200)
          });
        }
      }
    });
  });
  
  return contacts;
}

function extractContactFromLine(line) {
  if (!line || line.length < 10) return null;
  
  // Look for name patterns
  let name = null;
  let title = null;
  
  namePatterns.forEach(pattern => {
    pattern.lastIndex = 0;
    const match = pattern.exec(line);
    if (match && isValidName(match[0])) {
      name = match[0];
    }
  });
  
  if (name) {
    title = findTitleInContext(line);
    
    return {
      name: name.trim(),
      title: title || 'Contact Person',
      context: line.trim().substring(0, 200)
    };
  }
  
  return null;
}

function findTitleInContext(context) {
  const lowerContext = context.toLowerCase();
  
  for (const title of contactTitles) {
    if (lowerContext.includes(title.toLowerCase())) {
      return title;
    }
  }
  
  return null;
}

function isValidName(name) {
  if (!name || name.length < 4 || name.length > 50) return false;
  
  const lowerName = name.toLowerCase();
  
  // Check if it contains excluded words
  for (const word of excludeWords) {
    if (lowerName.includes(word)) return false;
  }
  
  // Must have at least 2 words (first and last name)
  const words = name.trim().split(/\s+/);
  if (words.length < 2) return false;
  
  // Each word should start with capital letter
  for (const word of words) {
    if (!/^[A-Z][a-z]+/.test(word) && !/^(Jr\.?|Sr\.?|III?|IV)$/.test(word)) {
      return false;
    }
  }
  
  return true;
}

// Process each item
scrapedItems.forEach((item, index) => {
  const business = item.json;
  const content = business.content || business.data || '';
  
  console.log(`Processing item ${index}: ${business.business_name || 'Unknown'}`);
  
  // Extract contact persons
  const contactPersons = extractContactPersons(content);
  
  console.log(`Found ${contactPersons.length} contact persons for ${business.business_name || 'Unknown'}`);
  contactPersons.forEach((contact, i) => {
    console.log(`  ${i + 1}. ${contact.name} - ${contact.title} (${contact.source})`);
  });
  
  // Add contact persons to the business data
  const enrichedBusiness = {
    ...business,
    contact_persons: contactPersons,
    primary_contact: contactPersons.length > 0 ? contactPersons[0] : null,
    contact_extraction_date: new Date().toISOString(),
    total_contacts_found: contactPersons.length
  };
  
  results.push(enrichedBusiness);
});

console.log('=== CONTACT PERSON EXTRACTION SUMMARY ===');
console.log('Total businesses processed:', results.length);
console.log('Businesses with contact persons:', results.filter(b => b.contact_persons && b.contact_persons.length > 0).length);
console.log('Total contact persons found:', results.reduce((sum, b) => sum + (b.contact_persons?.length || 0), 0));

return results.map(business => ({ json: business }));
