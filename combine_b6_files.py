import pandas as pd
import os
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('b6_combine_files.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def categorize_b6_business(filename, subfolder):
    """Categorize B6 business type based on filename and subfolder."""
    filename_lower = filename.lower()

    if subfolder == "Health & Medical Services":
        # Healthcare & Medical categories
        if any(word in filename_lower for word in ['clinic', 'medical', 'health', 'hospital', 'physician', 'doctor']):
            if any(word in filename_lower for word in ['dental', 'dentist', 'denture']):
                return 'Dental Services'
            elif any(word in filename_lower for word in ['eye', 'ophthalmology', 'vision']):
                return 'Eye Care Services'
            elif any(word in filename_lower for word in ['child', 'children', 'pediatric', 'neonatal']):
                return 'Pediatric Services'
            elif any(word in filename_lower for word in ['mental', 'psycho']):
                return 'Mental Health Services'
            elif any(word in filename_lower for word in ['surgery', 'surgeon']):
                return 'Surgical Services'
            else:
                return 'General Healthcare'
        elif any(word in filename_lower for word in ['care', 'day care', 'foster']):
            return 'Care Services'
        elif any(word in filename_lower for word in ['pharmacy', 'medicine']):
            return 'Pharmacy & Medicine'
        elif any(word in filename_lower for word in ['acupuncture', 'holistic', 'homeopathic', 'ayurvedic', 'chinese medicine', 'oriental']):
            return 'Alternative Medicine'
        else:
            return 'Healthcare & Medical'

    elif subfolder == "Retail & Consumer Services":
        # Retail & Consumer categories
        if any(word in filename_lower for word in ['dealer', 'auto', 'car', 'vehicle', 'bmw', 'audi', 'acura', 'bentley']):
            return 'Automotive Sales & Service'
        elif any(word in filename_lower for word in ['store', 'shop', 'retail']):
            if any(word in filename_lower for word in ['clothing', 'apparel', 'fashion', 'bridal']):
                return 'Clothing & Fashion'
            elif any(word in filename_lower for word in ['food', 'grocery', 'beverage', 'restaurant', 'bagel']):
                return 'Food & Beverage'
            elif any(word in filename_lower for word in ['furniture', 'bed', 'bedroom']):
                return 'Furniture & Home'
            elif any(word in filename_lower for word in ['beauty', 'cosmetic', 'salon']):
                return 'Beauty & Personal Care'
            elif any(word in filename_lower for word in ['book', 'media', 'entertainment']):
                return 'Books & Entertainment'
            elif any(word in filename_lower for word in ['pet', 'animal', 'bird', 'dog']):
                return 'Pet & Animal Supplies'
            elif any(word in filename_lower for word in ['sport', 'athletic', 'outdoor', 'archery', 'baseball']):
                return 'Sports & Recreation'
            else:
                return 'General Retail'
        elif any(word in filename_lower for word in ['wholesaler', 'distributor', 'supplier']):
            return 'Wholesale & Distribution'
        elif any(word in filename_lower for word in ['repair', 'service', 'maintenance']):
            return 'Repair & Maintenance Services'
        else:
            return 'Retail & Consumer Services'

    return 'Other'

def load_and_combine_excel_files(folder_path):
    """Load and combine all Excel files from B6 folder and subfolders."""
    logger.info(f"Loading Excel files from: {folder_path}")

    all_dataframes = []
    file_stats = {}

    # Get all Excel files recursively
    excel_files = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.xlsx'):
                full_path = os.path.join(root, file)
                subfolder = os.path.basename(root) if root != folder_path else ""
                excel_files.append((full_path, file, subfolder))

    logger.info(f"Found {len(excel_files)} Excel files to process")

    for i, (file_path, filename, subfolder) in enumerate(excel_files, 1):
        try:
            logger.info(f"Processing: {i}/{len(excel_files)}. {filename} (from {subfolder})")

            # Read Excel file
            df = pd.read_excel(file_path, engine='openpyxl')

            if len(df) > 0:
                # Add metadata columns
                df['source_file'] = filename
                df['source_category'] = filename.replace('.xlsx', '')
                df['subfolder'] = subfolder
                df['business_type'] = categorize_b6_business(filename, subfolder)
                df['created_date'] = datetime.now().strftime('%Y-%m-%d')

                all_dataframes.append(df)
                file_stats[filename] = len(df)

                logger.info(f"  - Loaded {len(df)} records from {filename}")
            else:
                logger.warning(f"  - Empty file: {filename}")
                file_stats[filename] = 0

        except Exception as e:
            logger.error(f"  - Error loading {filename}: {e}")
            file_stats[filename] = 0

    if all_dataframes:
        combined_df = pd.concat(all_dataframes, ignore_index=True)
        logger.info(f"Combined dataset shape: {combined_df.shape}")
        return combined_df, file_stats
    else:
        logger.error("No data loaded from any files")
        return pd.DataFrame(), file_stats

def save_combined_data(df, output_filename):
    """Save the combined dataframe to CSV."""
    logger.info(f"Saving combined data to: {output_filename}")
    
    df.to_csv(output_filename, index=False)
    
    # Get file size in MB
    file_size_mb = os.path.getsize(output_filename) / (1024 * 1024)
    
    logger.info(f"Combined file saved: {output_filename}")
    logger.info(f"File size: {file_size_mb:.1f} MB")
    logger.info(f"Total records: {len(df):,}")
    
    return file_size_mb

def generate_combination_report(file_stats, combined_df, output_filename, file_size_mb):
    """Generate a detailed combination report."""
    report_path = 'b6_combination_report.txt'

    with open(report_path, 'w') as f:
        f.write("B6 Files Combination Report\n")
        f.write("=" * 27 + "\n")
        f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("File Loading Statistics:\n")
        f.write("-" * 25 + "\n")
        total_files = len(file_stats)
        total_records = sum(file_stats.values())
        f.write(f"Total files processed: {total_files}\n")
        f.write(f"Total records loaded: {total_records:,}\n\n")

        f.write("Files processed:\n")
        for i, (filename, count) in enumerate(file_stats.items(), 1):
            f.write(f"  {i:3d}. {filename}: {count:,} records\n")
        f.write("\n")

        f.write("Combined Dataset Information:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Output file: {output_filename}\n")
        f.write(f"Total records: {len(combined_df):,}\n")
        f.write(f"Total columns: {len(combined_df.columns)}\n")
        f.write(f"File size: {file_size_mb:.1f} MB\n\n")

        f.write("Column Information:\n")
        f.write("-" * 19 + "\n")
        for i, col in enumerate(combined_df.columns, 1):
            f.write(f"  {i:2d}. {col}\n")
        f.write("\n")

        # Business type distribution
        if 'business_type' in combined_df.columns:
            f.write("Business Type Distribution:\n")
            f.write("-" * 28 + "\n")
            business_type_counts = combined_df['business_type'].value_counts()
            for business_type, count in business_type_counts.items():
                f.write(f"  {business_type}: {count:,} records\n")
            f.write("\n")

        # Subfolder distribution
        if 'subfolder' in combined_df.columns:
            f.write("Subfolder Distribution:\n")
            f.write("-" * 23 + "\n")
            subfolder_counts = combined_df['subfolder'].value_counts()
            for subfolder, count in subfolder_counts.items():
                f.write(f"  {subfolder}: {count:,} records\n")

    logger.info(f"Combination report saved to: {report_path}")

def main():
    """Main function to combine all B6 files."""
    logger.info("Starting B6 files combination...")

    # Configuration
    folder_path = "B6"
    output_filename = "b6_combined_all_files.csv"

    try:
        # Load and combine all Excel files
        logger.info("Step 1: Loading and combining Excel files...")
        combined_df, file_stats = load_and_combine_excel_files(folder_path)
        
        if combined_df.empty:
            logger.error("No data loaded. Exiting.")
            return

        # Save combined data
        logger.info("Step 2: Saving combined data...")
        file_size_mb = save_combined_data(combined_df, output_filename)

        # Generate combination report
        logger.info("Step 3: Generating combination report...")
        generate_combination_report(file_stats, combined_df, output_filename, file_size_mb)

        logger.info("Combination completed successfully!")
        logger.info(f"Combined file: {output_filename}")
        logger.info(f"Total records: {len(combined_df):,}")
        logger.info(f"File size: {file_size_mb:.1f} MB")

    except Exception as e:
        logger.error(f"Combination failed: {e}")
        raise

if __name__ == "__main__":
    main()
