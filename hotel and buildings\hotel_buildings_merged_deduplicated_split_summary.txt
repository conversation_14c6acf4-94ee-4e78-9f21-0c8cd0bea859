FILE SPLIT SUMMARY
==================

Original file: hotel and buildings/hotel_buildings_merged_deduplicated.csv
Original size: 165.74 MB
Original rows: 590,077

Split into 4 parts:
- hotel_buildings_merged_deduplicated_part1.csv: 41.10 MB (147,520 rows)
- hotel_buildings_merged_deduplicated_part2.csv: 40.82 MB (147,520 rows)
- hotel_buildings_merged_deduplicated_part3.csv: 41.05 MB (147,520 rows)
- hotel_buildings_merged_deduplicated_part4.csv: 41.23 MB (147,517 rows)

To recombine files:
```python
import pandas as pd
parts = []
parts.append(pd.read_csv('hotel_buildings_merged_deduplicated_part1.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_deduplicated_part2.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_deduplicated_part3.csv'))
parts.append(pd.read_csv('hotel_buildings_merged_deduplicated_part4.csv'))
combined = pd.concat(parts, ignore_index=True)
combined.to_csv('hotel_buildings_merged_deduplicated_recombined.csv', index=False)
```
