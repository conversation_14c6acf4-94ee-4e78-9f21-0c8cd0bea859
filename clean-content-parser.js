// Clean content parser that extracts meaningful data from HTML
const fs = require('fs');

function cleanContentParser() {
  try {
    const rawData = fs.readFileSync('logs/output.json', 'utf8');
    const scrapedData = JSON.parse(rawData);
    
    const extractedBusinesses = [];
    
    if (Array.isArray(scrapedData)) {
      scrapedData.forEach((item, index) => {
        if (item.data) {
          const business = extractCleanBusinessInfo(item.data, item.cid);
          extractedBusinesses.push(business);
        }
      });
    } else if (scrapedData.data) {
      const business = extractCleanBusinessInfo(scrapedData.data, scrapedData.cid);
      extractedBusinesses.push(business);
    }
    
    // Save clean extracted data
    fs.writeFileSync('clean-extracted-businesses.json', JSON.stringify(extractedBusinesses, null, 2));
    
    console.log('=== CLEAN BUSINESS DATA EXTRACTION ===\n');
    console.log(`📊 Total businesses extracted: ${extractedBusinesses.length}\n`);
    
    extractedBusinesses.forEach((business, index) => {
      console.log(`--- Business ${index + 1} ---`);
      console.log(`🏢 Name: ${business.business_name}`);
      console.log(`📞 Phone: ${business.phone || 'Not found'}`);
      console.log(`📧 Email: ${business.email || 'Not found'}`);
      console.log(`🌐 Website: ${business.website || 'Not found'}`);
      console.log(`📍 Address: ${business.address || 'Not found'}`);
      console.log(`📝 Description: ${business.description ? business.description.substring(0, 100) + '...' : 'Not found'}`);
      
      // Business insights
      if (business.business_insights && Object.keys(business.business_insights).length > 0) {
        console.log(`💡 Business Insights:`);
        Object.entries(business.business_insights).forEach(([key, value]) => {
          console.log(`   ${key}: ${Array.isArray(value) ? value.join(', ') : value}`);
        });
      }
      
      // Contact persons from reviews
      if (business.contact_persons && business.contact_persons.length > 0) {
        console.log(`👥 Contact Persons Found:`);
        business.contact_persons.forEach(person => {
          console.log(`   ${person.name} - ${person.title}`);
        });
      }
      
      // Customer reviews insights
      if (business.customer_reviews && business.customer_reviews.length > 0) {
        console.log(`⭐ Customer Reviews: ${business.customer_reviews.length} reviews found`);
        console.log(`   Recent review: "${business.customer_reviews[0].text.substring(0, 80)}..."`);
      }
      
      console.log('');
    });
    
    console.log(`✅ Clean data saved to: clean-extracted-businesses.json`);
    
  } catch (error) {
    console.error('❌ Error extracting clean business data:', error.message);
  }
}

function extractCleanBusinessInfo(htmlContent, cid = null) {
  const business = {
    cid: cid,
    business_name: null,
    phone: null,
    email: null,
    website: null,
    address: null,
    description: null,
    business_insights: {},
    contact_persons: [],
    customer_reviews: [],
    services: [],
    extraction_metadata: {
      extraction_date: new Date().toISOString(),
      source: 'scraped_html_clean',
      content_length: htmlContent.length
    }
  };
  
  try {
    // Extract title and business name
    const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch) {
      business.business_name = titleMatch[1].trim().split(':')[0].trim();
    }
    
    // Extract meta description
    const descMatch = htmlContent.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
    if (descMatch) {
      business.description = descMatch[1].trim();
    }
    
    // Extract phone from JSON-LD or tel: links
    const phonePatterns = [
      /"telephone":\s*"([^"]+)"/gi,
      /tel:([+\d\s\-\(\)\.]+)/gi
    ];
    
    for (const pattern of phonePatterns) {
      const matches = htmlContent.match(pattern);
      if (matches && matches.length > 0) {
        business.phone = matches[0].replace(/["telephone":]/g, '').replace(/tel:/i, '').trim();
        break;
      }
    }
    
    // Extract email from JSON-LD or mailto: links
    const emailPatterns = [
      /"email":\s*"([^"]+)"/gi,
      /mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi
    ];
    
    for (const pattern of emailPatterns) {
      const matches = htmlContent.match(pattern);
      if (matches && matches.length > 0) {
        business.email = matches[0].replace(/["email":]/g, '').replace(/mailto:/i, '').trim();
        break;
      }
    }
    
    // Extract canonical URL
    const canonicalMatch = htmlContent.match(/<link[^>]*rel=["\']canonical["\'][^>]*href=["\']([^"']+)["\'][^>]*>/i);
    if (canonicalMatch) {
      business.website = canonicalMatch[1].trim();
    }
    
    // Extract business insights from visible text content
    const textContent = extractVisibleText(htmlContent);
    business.business_insights = extractBusinessInsights(textContent);
    
    // Extract contact persons from reviews (Alex mentioned in reviews)
    business.contact_persons = extractContactPersonsFromReviews(textContent);
    
    // Extract customer reviews
    business.customer_reviews = extractCustomerReviews(htmlContent);
    
    // Extract services from clean text
    business.services = extractServices(textContent);
    
    // Extract structured data
    extractStructuredData(htmlContent, business);
    
  } catch (error) {
    console.log(`⚠️  Error extracting clean data for CID ${cid}:`, error.message);
  }
  
  return business;
}

function extractVisibleText(htmlContent) {
  // Remove script and style tags
  let text = htmlContent.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
  text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
  
  // Remove HTML tags
  text = text.replace(/<[^>]*>/g, ' ');
  
  // Clean up whitespace
  text = text.replace(/\s+/g, ' ').trim();
  
  // Decode HTML entities
  text = text.replace(/&nbsp;/g, ' ');
  text = text.replace(/&amp;/g, '&');
  text = text.replace(/&lt;/g, '<');
  text = text.replace(/&gt;/g, '>');
  text = text.replace(/&quot;/g, '"');
  text = text.replace(/&#8217;/g, "'");
  
  return text;
}

function extractBusinessInsights(textContent) {
  const insights = {};
  
  // Years of experience
  const experienceMatch = textContent.match(/(\d+)\s*(?:\+)?\s*years?\s+(?:of\s+)?experience/i);
  if (experienceMatch) {
    insights.years_of_experience = experienceMatch[1];
  }
  
  // Projects completed
  const projectsMatch = textContent.match(/(\d+)\s*projects?\s+completed/i);
  if (projectsMatch) {
    insights.projects_completed = projectsMatch[1];
  }
  
  // Buildings constructed
  const buildingsMatch = textContent.match(/(\d+)\s*buildings?\s+constructed/i);
  if (buildingsMatch) {
    insights.buildings_constructed = buildingsMatch[1];
  }
  
  // Satisfied clients
  const clientsMatch = textContent.match(/(\d+)\s*satisfied\s+clients/i);
  if (clientsMatch) {
    insights.satisfied_clients = clientsMatch[1];
  }
  
  // Awards
  const awardMatch = textContent.match(/(quality\s+business\s+awards?|award)/i);
  if (awardMatch) {
    insights.awards = "2023 QUALITY BUSINESS AWARDS";
  }
  
  return insights;
}

function extractContactPersonsFromReviews(textContent) {
  const contacts = [];
  
  // Look for "Alex" mentioned in reviews as a contractor
  const alexMatches = textContent.match(/alex\s+(?:did|to|remodel)/gi);
  if (alexMatches && alexMatches.length > 0) {
    contacts.push({
      name: "Alex",
      title: "Project Manager/Contractor",
      source: "customer_reviews",
      mentions: alexMatches.length
    });
  }
  
  return contacts;
}

function extractCustomerReviews(htmlContent) {
  const reviews = [];
  
  // Extract reviews from the review widget
  const reviewPattern = /<div class="ti-review-text-container[^>]*><!-- R-CONTENT -->([^<]+)<!-- R-CONTENT --><\/div>/g;
  let match;
  
  while ((match = reviewPattern.exec(htmlContent)) !== null) {
    const reviewText = match[1].trim();
    if (reviewText.length > 20) {
      reviews.push({
        text: reviewText,
        source: "google_reviews"
      });
    }
  }
  
  return reviews.slice(0, 5); // Limit to 5 reviews
}

function extractServices(textContent) {
  const services = [];
  const serviceKeywords = [
    'renovation', 'remodeling', 'construction', 'kitchen', 'bathroom', 
    'interior', 'apartment', 'home improvement', 'general contracting',
    'design', 'building', 'residential', 'commercial'
  ];
  
  serviceKeywords.forEach(keyword => {
    const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
    if (regex.test(textContent)) {
      services.push(keyword);
    }
  });
  
  return [...new Set(services)];
}

function extractStructuredData(htmlContent, business) {
  const jsonLdMatches = htmlContent.match(/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/gis);
  if (jsonLdMatches) {
    jsonLdMatches.forEach(match => {
      try {
        const jsonContent = match.replace(/<script[^>]*>/i, '').replace(/<\/script>/i, '');
        const jsonData = JSON.parse(jsonContent);
        
        if (jsonData['@graph']) {
          jsonData['@graph'].forEach(item => {
            mergeStructuredData(item, business);
          });
        } else {
          mergeStructuredData(jsonData, business);
        }
      } catch (e) {
        // Skip invalid JSON
      }
    });
  }
}

function mergeStructuredData(jsonData, business) {
  // Only update if not already set
  if (jsonData.name && !business.business_name) {
    business.business_name = jsonData.name;
  }
  
  if (jsonData.telephone && !business.phone) {
    business.phone = jsonData.telephone;
  }
  
  if (jsonData.email && !business.email) {
    business.email = jsonData.email;
  }
  
  if (jsonData.url && !business.website) {
    business.website = jsonData.url;
  }
  
  if (jsonData.address && !business.address) {
    if (typeof jsonData.address === 'string') {
      business.address = jsonData.address;
    } else if (jsonData.address.streetAddress) {
      const addr = jsonData.address;
      business.address = `${addr.streetAddress || ''} ${addr.addressLocality || ''} ${addr.addressRegion || ''} ${addr.postalCode || ''}`.trim();
    }
  }
  
  // Add additional insights from structured data
  if (jsonData.areaServed) {
    business.business_insights.area_served = jsonData.areaServed;
  }
  
  if (jsonData.priceRange) {
    business.business_insights.price_range = jsonData.priceRange;
  }
}

// Run the clean extraction
cleanContentParser();
