DATA CLEANING REPORT
===================

Date: 2025-06-15
Files Processed: 27 XLSX files merged into CSV

ORIGINAL DATA:
- Total rows: 1,348,617
- Total columns: 27
- File size: 353.95 MB

CLEANING OPERATIONS:
1. Removed rows without websites
2. Removed duplicate entries based on 'cid' column

CLEANING RESULTS:
================

Step 1 - Remove rows without websites:
- Rows removed: 321,979
- Rows remaining: 1,026,638

Step 2 - Remove duplicates by CID:
- Duplicate rows found: 243,204
- Rows removed: 243,204
- Final rows: 783,434

FINAL CLEANED DATA:
==================
- Total rows: 783,434
- Unique CIDs: 783,434 (no duplicates)
- All rows have websites: YES
- File size: 215.29 MB
- Size reduction: 138.66 MB (39.18%)
- Data retention rate: 58.09%

TOP SOURCE FILES (by remaining rows):
====================================
1. Ph<PERSON>ician (General Practitioner): 57,976 rows
2. Health Consultant & Counselor: 55,961 rows  
3. Health Insurance Agency: 51,135 rows
4. Mental Health Service & Clinic: 45,331 rows
5. Pediatrician & Pediatric Clinic: 43,184 rows
6. Massage Therapist: 41,082 rows
7. Day Care Center: 39,545 rows
8. Wellness Center & Health Spa: 36,215 rows
9. Home Health Care Service: 35,128 rows
10. Eye Care & Optometry Clinic: 35,111 rows

FILES CREATED:
=============
- merged_data.csv (original merged file)
- merged_data_cleaned.csv (cleaned file)
- merge_xlsx_to_csv.py (merge script)
- clean_merged_data.py (cleaning script)
- cleaning_report.txt (this report)

QUALITY ASSURANCE:
=================
✓ No duplicate CIDs
✓ All rows have valid websites
✓ Data integrity maintained
✓ Source file tracking preserved
