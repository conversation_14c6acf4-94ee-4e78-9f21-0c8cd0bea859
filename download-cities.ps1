# PowerShell script to download the complete 1000 US cities dataset

$url = "https://gist.githubusercontent.com/Miserlou/c5cd8364bf9b2420bb29/raw/2bf258763cdddd704f8ffd3ea9a3e81d25e2c6f6/cities.json"
$outputFile = "cities-1000-data.json"

Write-Host "Downloading 1000 US cities dataset from GitHub Gist..." -ForegroundColor Green

try {
    Invoke-WebRequest -Uri $url -OutFile $outputFile
    Write-Host "Successfully downloaded cities data to $outputFile" -ForegroundColor Green

    # Read and parse the JSON to show stats
    $jsonContent = Get-Content $outputFile -Raw | ConvertFrom-Json
    $cityCount = $jsonContent.Count

    Write-Host "Total cities: $cityCount" -ForegroundColor Cyan
    Write-Host "File size: $([math]::Round((Get-Item $outputFile).Length / 1KB, 2)) KB" -ForegroundColor Cyan

    Write-Host "Sample cities:" -ForegroundColor Yellow
    for ($i = 0; $i -lt [Math]::Min(5, $cityCount); $i++) {
        $city = $jsonContent[$i]
        Write-Host "   $($city.city), $($city.state) (Pop: $($city.population))" -ForegroundColor White
    }

    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "   1. Use the downloaded JSON data to create the complete cities file" -ForegroundColor White
    Write-Host "   2. Update generate-search-queries-maps.js to use all 1000 cities" -ForegroundColor White

} catch {
    Write-Host "Error downloading data: $($_.Exception.Message)" -ForegroundColor Red
}
