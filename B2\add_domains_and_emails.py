#!/usr/bin/env python3
"""
Scrip<PERSON> to add domain and info@domain email columns to the cleaned CSV file.
"""

import pandas as pd
import numpy as np
from urllib.parse import urlparse

def extract_domain(website_url):
    """
    Extract domain from website URL.
    """
    if pd.isna(website_url) or website_url == '':
        return None
    
    try:
        # Clean the URL - remove extra spaces and ensure it has a protocol
        url = str(website_url).strip()
        
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove www. prefix if present
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # Remove any trailing slashes or paths
        domain = domain.split('/')[0]
        
        # Basic validation - domain should contain at least one dot
        if '.' not in domain or len(domain) < 3:
            return None
            
        return domain
    except:
        return None

def add_domains_and_emails():
    """
    Add domain and info@domain email columns to the cleaned CSV file.
    """
    print("Loading merged_data_cleaned.csv...")
    
    # Read the cleaned CSV file
    df = pd.read_csv('merged_data_cleaned.csv', low_memory=False)
    
    print(f"Loaded data shape: {df.shape}")
    print(f"Total rows: {len(df):,}")
    
    # Check current domain and email columns
    print(f"\nCurrent column status:")
    if 'domain' in df.columns:
        current_domains = df['domain'].notna().sum()
        print(f"  - Domain column exists with {current_domains:,} non-null values")
    else:
        print(f"  - Domain column does not exist")
    
    if 'info@ email' in df.columns:
        current_emails = df['info@ email'].notna().sum()
        print(f"  - Info@ email column exists with {current_emails:,} non-null values")
    else:
        print(f"  - Info@ email column does not exist")
    
    # Extract domains from website column
    print(f"\nExtracting domains from websites...")
    df['domain'] = df['website'].apply(extract_domain)
    
    # Check domain extraction results
    valid_domains = df['domain'].notna().sum()
    invalid_domains = df['domain'].isna().sum()
    
    print(f"Domain extraction results:")
    print(f"  - Valid domains extracted: {valid_domains:,}")
    print(f"  - Invalid/failed extractions: {invalid_domains:,}")
    print(f"  - Success rate: {(valid_domains / len(df) * 100):.2f}%")
    
    # Show some examples of failed extractions
    if invalid_domains > 0:
        failed_examples = df[df['domain'].isna()]['website'].head(5).tolist()
        print(f"\nSample failed domain extractions:")
        for i, website in enumerate(failed_examples, 1):
            print(f"  {i}. {website}")
    
    # Create info@domain emails
    print(f"\nCreating info@domain emails...")
    df['info@ email'] = df['domain'].apply(
        lambda x: f"info@{x}" if pd.notna(x) and x != '' else None
    )
    
    # Check email creation results
    valid_emails = df['info@ email'].notna().sum()
    print(f"Info emails created: {valid_emails:,}")
    
    # Show some examples
    print(f"\nSample domain extractions and emails:")
    sample_data = df[df['domain'].notna()][['title', 'website', 'domain', 'info@ email']].head(10)
    for idx, row in sample_data.iterrows():
        print(f"  Business: {row['title'][:50]}...")
        print(f"  Website: {row['website']}")
        print(f"  Domain: {row['domain']}")
        print(f"  Email: {row['info@ email']}")
        print()
    
    # Analyze domain statistics
    print(f"Domain statistics:")
    domain_counts = df['domain'].value_counts()
    print(f"  - Total unique domains: {len(domain_counts):,}")
    print(f"  - Most common domains:")
    for domain, count in domain_counts.head(10).items():
        print(f"    {domain}: {count} businesses")
    
    # Save the updated data
    output_file = "merged_data_with_domains.csv"
    print(f"\nSaving updated data to: {output_file}")
    
    df.to_csv(output_file, index=False, encoding='utf-8')
    
    # Check file size
    import os
    file_size = os.path.getsize(output_file) / (1024*1024)
    
    print(f"\nFile saved successfully!")
    print(f"  - File: {output_file}")
    print(f"  - Size: {file_size:.2f} MB")
    print(f"  - Rows: {len(df):,}")
    print(f"  - Columns: {len(df.columns)}")
    print(f"  - Domains populated: {valid_domains:,} ({(valid_domains/len(df)*100):.2f}%)")
    print(f"  - Emails created: {valid_emails:,} ({(valid_emails/len(df)*100):.2f}%)")
    
    return df

if __name__ == "__main__":
    add_domains_and_emails()
