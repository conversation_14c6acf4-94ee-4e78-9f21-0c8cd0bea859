#!/usr/bin/env python3
"""
Quick verification script for B3 merge results.
"""

import pandas as pd
import os

def verify_results():
    print("B3 MERGE RESULTS VERIFICATION")
    print("="*40)
    
    # Check if files exist
    files_to_check = [
        'b3_merged_data.csv',
        'b3_merged_deduplicated.csv',
        'b3_processing_report.txt'
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            size_mb = os.path.getsize(file) / (1024*1024)
            print(f"✅ {file} - {size_mb:.2f} MB")
        else:
            print(f"❌ {file} - NOT FOUND")
    
    print("\nFINAL DEDUPLICATED FILE ANALYSIS:")
    print("-" * 40)
    
    try:
        df = pd.read_csv('b3_merged_deduplicated.csv')
        
        print(f"Total rows: {len(df):,}")
        print(f"Total columns: {len(df.columns)}")
        print(f"Unique domains: {df['domain'].nunique():,}")
        print(f"Perfect deduplication: {len(df) == df['domain'].nunique()}")
        print(f"All rows have domains: {df['domain'].notna().all()}")
        
        print(f"\nSample data:")
        sample = df[['title', 'domain', 'source_file']].head()
        for _, row in sample.iterrows():
            print(f"  {row['title'][:40]}... -> {row['domain']}")
        
        print(f"\nTop source files by contribution:")
        source_counts = df['source_file'].value_counts().head(5)
        for source, count in source_counts.items():
            print(f"  {source}: {count:,} rows")
            
    except Exception as e:
        print(f"Error reading file: {e}")

if __name__ == "__main__":
    verify_results()
