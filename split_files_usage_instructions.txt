
USING SPLIT FILES - INSTRUCTIONS
===============================

The large CSV files have been split into smaller chunks (max 50MB each) for easier handling.

1. INDIVIDUAL USE:
   Each split file can be used independently:
   ```python
   import pandas as pd
   df = pd.read_csv('merged_data_final_part1.csv')
   ```

2. COMBINING FILES:
   To work with the complete dataset, combine all parts:
   ```python
   import pandas as pd
   import glob
   
   # For B2 dataset
   b2_files = sorted(glob.glob('B2/merged_data_final_part*.csv'))
   b2_parts = [pd.read_csv(f) for f in b2_files]
   b2_complete = pd.concat(b2_parts, ignore_index=True)
   
   # For Hotel & Buildings final dataset
   hb_files = sorted(glob.glob('hotel and buildings/hotel_buildings_final_cross_deduplicated_part*.csv'))
   hb_parts = [pd.read_csv(f) for f in hb_files]
   hb_complete = pd.concat(hb_parts, ignore_index=True)
   ```

3. MEMORY EFFICIENT PROCESSING:
   Process files one at a time to save memory:
   ```python
   import pandas as pd
   import glob
   
   def process_in_chunks(pattern, process_func):
       files = sorted(glob.glob(pattern))
       results = []
       for file in files:
           df = pd.read_csv(file)
           result = process_func(df)
           results.append(result)
       return results
   ```

4. FILE LOCATIONS:
   - B2 dataset: B2/merged_data_final_part1.csv to part3.csv
   - Hotel & Buildings (raw): hotel and buildings/hotel_buildings_merged_data_part1.csv to part13.csv
   - Hotel & Buildings (deduplicated): hotel and buildings/hotel_buildings_merged_deduplicated_part1.csv to part4.csv
   - Hotel & Buildings (final): hotel and buildings/hotel_buildings_final_cross_deduplicated_part1.csv to part3.csv

5. VERIFICATION:
   Each directory contains a *_split_summary.txt file with detailed information about the split.

6. ORIGINAL FILES:
   Original large files are still present. You can safely remove them after verifying the split files work correctly.
