# PowerShell script to convert cities-1000-data.json to locations array format

$stateAbbr = @{
    'Alabama' = 'AL'; 'Alaska' = 'AK'; 'Arizona' = 'AZ'; 'Arkansas' = 'AR'; 'California' = 'CA'
    'Colorado' = 'CO'; 'Connecticut' = 'CT'; 'Delaware' = 'DE'; 'District of Columbia' = 'DC'
    'Florida' = 'FL'; 'Georgia' = 'GA'; 'Hawaii' = 'HI'; 'Idaho' = 'ID'; 'Illinois' = 'IL'
    'Indiana' = 'IN'; 'Iowa' = 'IA'; 'Kansas' = 'KS'; 'Kentucky' = 'KY'; 'Louisiana' = 'LA'
    'Maine' = 'ME'; 'Maryland' = 'MD'; 'Massachusetts' = 'MA'; 'Michigan' = 'MI'
    'Minnesota' = 'MN'; 'Mississippi' = 'MS'; 'Missouri' = 'MO'; 'Montana' = 'MT'
    'Nebraska' = 'NE'; 'Nevada' = 'NV'; 'New Hampshire' = 'NH'; 'New Jersey' = 'NJ'
    'New Mexico' = 'NM'; 'New York' = 'NY'; 'North Carolina' = 'NC'; 'North Dakota' = 'ND'
    'Ohio' = 'OH'; 'Oklahoma' = 'OK'; 'Oregon' = 'OR'; 'Pennsylvania' = 'PA'
    'Rhode Island' = 'RI'; 'South Carolina' = 'SC'; 'South Dakota' = 'SD'; 'Tennessee' = 'TN'
    'Texas' = 'TX'; 'Utah' = 'UT'; 'Vermont' = 'VT'; 'Virginia' = 'VA'; 'Washington' = 'WA'
    'West Virginia' = 'WV'; 'Wisconsin' = 'WI'; 'Wyoming' = 'WY'
}

try {
    $cities = Get-Content 'cities-1000-data.json' -Raw | ConvertFrom-Json
    
    $locations = @()
    foreach ($city in $cities) {
        $state = $stateAbbr[$city.state]
        if (-not $state) { $state = $city.state }
        $locations += "$($city.city) $state"
    }
    
    $output = "const locations = [`n"
    for ($i = 0; $i -lt $locations.Count; $i++) {
        if ($i -eq $locations.Count - 1) {
            $output += "  '$($locations[$i])'`n"
        } else {
            $output += "  '$($locations[$i])',`n"
        }
    }
    $output += "];"
    
    $output | Out-File -FilePath 'locations-1000.js' -Encoding UTF8
    
    Write-Host "Successfully converted $($locations.Count) cities to locations format" -ForegroundColor Green
    Write-Host "Output saved to: locations-1000.js" -ForegroundColor Cyan
    Write-Host "`nFirst 10 locations:" -ForegroundColor Yellow
    for ($i = 0; $i -lt [Math]::Min(10, $locations.Count); $i++) {
        Write-Host "  $($i + 1). $($locations[$i])" -ForegroundColor White
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
