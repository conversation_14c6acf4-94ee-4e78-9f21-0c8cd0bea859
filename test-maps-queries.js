// Test script to verify the minimal Maps API query structure with pagination
// This demonstrates the clean output format for Serper.dev node

const sampleCity = { "city": "New York", "latitude": 40.7127837, "longitude": -74.0059413, "state": "New York" };
const businessType = "contractor";

// Page 1 query (no page parameter)
const sampleQueryPage1 = {
  q: businessType,  // Search query (e.g., "contractor")
  ll: `${sampleCity.latitude},${sampleCity.longitude},11`,  // latitude,longitude,zoom format

  // Optional metadata for tracking (can be removed if not needed)
  city_name: sampleCity.city,
  state: sampleCity.state,
  page_number: 1,
  query_id: `${businessType.replace(/\s+/g, '_')}_${sampleCity.city.replace(/\s+/g, '_')}_${sampleCity.state.replace(/\s+/g, '_')}_page_1`
};

// Page 2+ query (includes page parameter)
const sampleQueryPage2 = {
  q: businessType,  // Search query (e.g., "contractor")
  ll: `${sampleCity.latitude},${sampleCity.longitude},11`,  // latitude,longitude,zoom format
  page: 2,  // Page parameter for pages 2 and beyond

  // Optional metadata for tracking (can be removed if not needed)
  city_name: sampleCity.city,
  state: sampleCity.state,
  page_number: 2,
  query_id: `${businessType.replace(/\s+/g, '_')}_${sampleCity.city.replace(/\s+/g, '_')}_${sampleCity.state.replace(/\s+/g, '_')}_page_2`
};

console.log('Sample Maps API Query Structures with Pagination:');
console.log('================================================');
console.log('\nPage 1 (no page parameter):');
console.log(JSON.stringify(sampleQueryPage1, null, 2));
console.log('\nPage 2+ (includes page parameter):');
console.log(JSON.stringify(sampleQueryPage2, null, 2));

console.log('\n\nKey Features:');
console.log('- Minimal Structure: Essential q, ll, and optional page parameters');
console.log('- For Serper.dev Node: API endpoint and headers handled by node');
console.log('- Location: Precise lat/lng coordinates with zoom level 11');
console.log('- Pagination: Page 1 omits page param, Page 2+ includes it');
console.log('- Search Query: Business type only (location handled by coordinates)');
console.log('- Clean Output: Perfect for workflow integration');

console.log('\n\nPagination Logic:');
console.log('- Typically 16-20 results per page');
console.log('- Stop when you get fewer results than expected');
console.log('- Page 1: No page parameter needed');
console.log('- Page 2+: Include page parameter');
