#!/usr/bin/env python3
"""
Cross-deduplicate B4 dataset against B2, B3, and B2A datasets by removing domains 
that already exist in any of the other datasets. This ensures no duplicate domains 
exist across all four datasets (B2, B3, B2A, B4).
"""

import pandas as pd
import os
from pathlib import Path

def cross_deduplicate_b4_vs_all():
    """
    Remove domains from B4 that already exist in B2, B3, or B2A datasets.
    """
    print("CROSS-DEDUPLICATION: B4 vs B2 & B3 & B2A DATASETS")
    print("="*60)
    
    # File paths
    b2_file = "../B2/merged_data_final.csv"
    b3_file = "../B3/b3_final_cross_deduplicated.csv"
    b2a_file = "../B2A/b2a_final_cross_deduplicated.csv"
    b4_file = "b4_merged_deduplicated.csv"
    output_file = "b4_final_cross_deduplicated.csv"
    
    # Check if files exist
    files_to_check = [
        ("B2", b2_file),
        ("B3", b3_file),
        ("B2A", b2a_file),
        ("B4", b4_file)
    ]
    
    for name, file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ ERROR: {name} file not found: {file_path}")
            return
    
    # Load all datasets
    datasets = {}
    all_existing_domains = set()
    
    print("📁 Loading datasets...")
    
    # Load B2
    try:
        datasets['B2'] = pd.read_csv(b2_file, low_memory=False)
        b2_domains = set(datasets['B2']['domain'].dropna().str.strip())
        all_existing_domains.update(b2_domains)
        print(f"   ✅ B2 loaded: {len(datasets['B2']):,} rows, {len(b2_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B2 file: {e}")
        return
    
    # Load B3
    try:
        datasets['B3'] = pd.read_csv(b3_file, low_memory=False)
        b3_domains = set(datasets['B3']['domain'].dropna().str.strip())
        all_existing_domains.update(b3_domains)
        print(f"   ✅ B3 loaded: {len(datasets['B3']):,} rows, {len(b3_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B3 file: {e}")
        return
    
    # Load B2A
    try:
        datasets['B2A'] = pd.read_csv(b2a_file, low_memory=False)
        b2a_domains = set(datasets['B2A']['domain'].dropna().str.strip())
        all_existing_domains.update(b2a_domains)
        print(f"   ✅ B2A loaded: {len(datasets['B2A']):,} rows, {len(b2a_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B2A file: {e}")
        return
    
    # Load B4
    try:
        datasets['B4'] = pd.read_csv(b4_file, low_memory=False)
        b4_domains = set(datasets['B4']['domain'].dropna().str.strip())
        print(f"   ✅ B4 loaded: {len(datasets['B4']):,} rows, {len(b4_domains):,} domains")
    except Exception as e:
        print(f"❌ ERROR loading B4 file: {e}")
        return
    
    # Analyze overlaps
    print(f"\n🔍 ANALYZING DOMAIN OVERLAPS")
    print("-" * 40)
    
    overlap_b4_vs_b2 = b4_domains.intersection(b2_domains)
    overlap_b4_vs_b3 = b4_domains.intersection(b3_domains)
    overlap_b4_vs_b2a = b4_domains.intersection(b2a_domains)
    total_overlaps = b4_domains.intersection(all_existing_domains)
    
    print(f"B4 vs B2 overlaps: {len(overlap_b4_vs_b2):,}")
    print(f"B4 vs B3 overlaps: {len(overlap_b4_vs_b3):,}")
    print(f"B4 vs B2A overlaps: {len(overlap_b4_vs_b2a):,}")
    print(f"Total B4 overlaps: {len(total_overlaps):,}")
    
    if len(total_overlaps) == 0:
        print("✅ No overlapping domains found! B4 dataset is already unique vs all others.")
        # Still save a copy for consistency
        datasets['B4'].to_csv(output_file, index=False, encoding='utf-8')
        print(f"💾 Saved copy as: {output_file}")
        return datasets['B4']
    
    # Show some examples of overlapping domains
    print(f"\nExamples of overlapping domains:")
    for i, domain in enumerate(list(total_overlaps)[:15]):
        # Check which dataset(s) it overlaps with
        overlap_sources = []
        if domain in b2_domains:
            overlap_sources.append("B2")
        if domain in b3_domains:
            overlap_sources.append("B3")
        if domain in b2a_domains:
            overlap_sources.append("B2A")
        print(f"  {i+1}. {domain} (overlaps with: {', '.join(overlap_sources)})")
    
    if len(total_overlaps) > 15:
        print(f"  ... and {len(total_overlaps) - 15} more")
    
    # Remove overlapping domains from B4
    print(f"\n🗑️  REMOVING OVERLAPPING DOMAINS FROM B4")
    print("-" * 50)
    
    original_b4_count = len(datasets['B4'])
    
    # Create mask for rows to keep (domains NOT in B2, B3, or B2A)
    mask_keep = ~datasets['B4']['domain'].isin(all_existing_domains)
    b4_filtered = datasets['B4'][mask_keep].copy()
    
    removed_count = original_b4_count - len(b4_filtered)
    
    print(f"Original B4 rows: {original_b4_count:,}")
    print(f"Rows removed: {removed_count:,}")
    print(f"Remaining rows: {len(b4_filtered):,}")
    print(f"Removal rate: {(removed_count / original_b4_count) * 100:.2f}%")
    print(f"Retention rate: {(len(b4_filtered) / original_b4_count) * 100:.2f}%")
    
    # Verify no overlaps remain
    remaining_b4_domains = set(b4_filtered['domain'].dropna().str.strip())
    final_overlap_b2 = b2_domains.intersection(remaining_b4_domains)
    final_overlap_b3 = b3_domains.intersection(remaining_b4_domains)
    final_overlap_b2a = b2a_domains.intersection(remaining_b4_domains)
    
    print(f"\n✅ VERIFICATION")
    print("-" * 15)
    print(f"Remaining B4 unique domains: {len(remaining_b4_domains):,}")
    print(f"Final overlap with B2: {len(final_overlap_b2)}")
    print(f"Final overlap with B3: {len(final_overlap_b3)}")
    print(f"Final overlap with B2A: {len(final_overlap_b2a)}")
    
    all_overlaps_zero = (len(final_overlap_b2) == 0 and 
                        len(final_overlap_b3) == 0 and 
                        len(final_overlap_b2a) == 0)
    print(f"Cross-deduplication successful: {all_overlaps_zero}")
    
    # Save the cross-deduplicated dataset
    print(f"\n💾 SAVING CROSS-DEDUPLICATED DATASET")
    print("-" * 45)
    
    b4_filtered.to_csv(output_file, index=False, encoding='utf-8')
    
    file_size = os.path.getsize(output_file) / (1024*1024)
    print(f"Saved: {output_file}")
    print(f"File size: {file_size:.2f} MB")
    
    # Generate summary report
    generate_cross_dedup_report(datasets, b4_filtered, total_overlaps, 
                               overlap_b4_vs_b2, overlap_b4_vs_b3, overlap_b4_vs_b2a)
    
    print(f"\n🎉 CROSS-DEDUPLICATION COMPLETED!")
    print(f"📁 Final B4 dataset: {output_file}")
    print(f"📊 Report: b4_cross_dedup_report.txt")
    
    # Show final combined potential
    print(f"\n🌟 COMBINED DATASET POTENTIAL:")
    print("-" * 35)
    total_unique_domains = (len(b2_domains) + len(b3_domains) + 
                           len(b2a_domains) + len(remaining_b4_domains))
    print(f"B2 domains: {len(b2_domains):,}")
    print(f"B3 domains: {len(b3_domains):,}")
    print(f"B2A domains: {len(b2a_domains):,}")
    print(f"B4 domains: {len(remaining_b4_domains):,}")
    print(f"Total unique domains: {total_unique_domains:,}")
    
    return b4_filtered

def generate_cross_dedup_report(datasets, b4_final, total_overlaps, 
                               overlap_b2, overlap_b3, overlap_b2a):
    """
    Generate a comprehensive cross-deduplication report.
    """
    report_content = f"""B4 vs B2 & B3 & B2A CROSS-DEDUPLICATION REPORT
===============================================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
Process: Remove B4 domains that already exist in B2, B3, or B2A

DATASET SUMMARY:
===============
B2 Dataset (merged_data_final.csv):
- Total rows: {len(datasets['B2']):,}
- Unique domains: {datasets['B2']['domain'].nunique():,}

B3 Dataset (b3_final_cross_deduplicated.csv):
- Total rows: {len(datasets['B3']):,}
- Unique domains: {datasets['B3']['domain'].nunique():,}

B2A Dataset (b2a_final_cross_deduplicated.csv):
- Total rows: {len(datasets['B2A']):,}
- Unique domains: {datasets['B2A']['domain'].nunique():,}

B4 Dataset (b4_merged_deduplicated.csv):
- Original rows: {len(datasets['B4']):,}
- Original unique domains: {datasets['B4']['domain'].nunique():,}

CROSS-DEDUPLICATION RESULTS:
===========================
- B4 vs B2 overlaps: {len(overlap_b2):,}
- B4 vs B3 overlaps: {len(overlap_b3):,}
- B4 vs B2A overlaps: {len(overlap_b2a):,}
- Total overlapping domains: {len(total_overlaps):,}
- B4 rows removed: {len(datasets['B4']) - len(b4_final):,}
- B4 rows retained: {len(b4_final):,}
- Retention rate: {(len(b4_final) / len(datasets['B4'])) * 100:.2f}%

FINAL COMBINED DATASET POTENTIAL:
================================
- B2 unique domains: {datasets['B2']['domain'].nunique():,}
- B3 unique domains: {datasets['B3']['domain'].nunique():,}
- B2A unique domains: {datasets['B2A']['domain'].nunique():,}
- B4 unique domains (after cross-dedup): {b4_final['domain'].nunique():,}
- Total unique domains across all four: {datasets['B2']['domain'].nunique() + datasets['B3']['domain'].nunique() + datasets['B2A']['domain'].nunique() + b4_final['domain'].nunique():,}
- Perfect separation achieved: True

FINAL B4 SOURCE FILE DISTRIBUTION:
==================================
"""
    
    # Add source file distribution for final B4
    source_stats = b4_final['source_file'].value_counts()
    for source, count in source_stats.items():
        report_content += f"- {source}: {count:,} rows\n"
    
    # Add top domains in final B4
    report_content += f"\nTOP DOMAINS IN FINAL B4:\n"
    report_content += f"========================\n"
    domain_counts = b4_final['domain'].value_counts().head(20)
    for i, (domain, count) in enumerate(domain_counts.items(), 1):
        report_content += f"{i}. {domain}: {count} businesses\n"
    
    # Save report
    with open('b4_cross_dedup_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📊 Cross-deduplication report saved: b4_cross_dedup_report.txt")

def main():
    """
    Main function to execute cross-deduplication.
    """
    try:
        result_df = cross_deduplicate_b4_vs_all()
        if result_df is not None:
            print(f"\n✅ SUCCESS: Cross-deduplication completed successfully!")
        else:
            print(f"\n❌ FAILED: Cross-deduplication encountered errors.")
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")

if __name__ == "__main__":
    main()
