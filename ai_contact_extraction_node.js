// AI-POWERED CONTACT EXTRACTION NODE
// Uses OpenAI GPT to intelligently extract contact person information
// Add this as an HTTP Request node or Code node in your n8n workflow

console.log('=== AI-POWERED CONTACT EXTRACTION NODE ===');

// Configuration - Replace with your actual API key
const OPENAI_API_KEY = 'your-openai-api-key-here';
const OPENAI_MODEL = 'gpt-3.5-turbo'; // or 'gpt-4' for better accuracy
const MAX_CONTENT_LENGTH = 8000; // Limit content to fit API constraints

const businesses = $input.all();
console.log('Processing businesses for AI contact extraction:', businesses.length);

const results = [];

// Helper function to truncate content intelligently
function truncateContent(content, maxLength = MAX_CONTENT_LENGTH) {
  if (!content || content.length <= maxLength) return content;
  
  // Try to find a good breaking point (end of sentence, paragraph, etc.)
  const truncated = content.substring(0, maxLength);
  const lastPeriod = truncated.lastIndexOf('.');
  const lastNewline = truncated.lastIndexOf('\n');
  
  const breakPoint = Math.max(lastPeriod, lastNewline);
  
  if (breakPoint > maxLength * 0.8) {
    return content.substring(0, breakPoint + 1);
  }
  
  return truncated + '...';
}

// Helper function to clean and prepare content
function prepareContent(content) {
  if (!content) return '';
  
  // Remove HTML tags
  let cleaned = content.replace(/<[^>]*>/g, ' ');
  
  // Remove excessive whitespace
  cleaned = cleaned.replace(/\s+/g, ' ');
  
  // Remove common noise
  cleaned = cleaned.replace(/\b(cookie|privacy|policy|terms|conditions|javascript|css|html)\b/gi, '');
  
  return cleaned.trim();
}

// Main AI extraction function
async function extractContactsWithAI(businessData) {
  const content = businessData.content || businessData.data || '';
  const businessName = businessData.business_name || 'Unknown Business';
  const website = businessData.website || '';
  
  if (!content || content.length < 100) {
    return {
      contacts: [],
      error: 'Insufficient content for AI analysis'
    };
  }
  
  const preparedContent = prepareContent(content);
  const truncatedContent = truncateContent(preparedContent);
  
  const prompt = `You are an expert at extracting contact person information from business websites.

BUSINESS CONTEXT:
- Business Name: ${businessName}
- Website: ${website}
- Industry: Construction/Contracting

TASK: Extract contact person information from the website content below.

REQUIREMENTS:
1. Only extract REAL PEOPLE (not generic "contact us" or company names)
2. Focus on key personnel: owners, managers, project managers, sales people
3. Include name, title, email, and phone if available
4. Verify names look like actual people (First Last format)
5. Return maximum 5 contacts

WEBSITE CONTENT:
${truncatedContent}

Return ONLY a valid JSON array in this exact format:
[
  {
    "name": "John Smith",
    "title": "Owner",
    "email": "<EMAIL>",
    "phone": "(*************",
    "confidence": "high"
  }
]

If no contacts found, return: []`;

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: OPENAI_MODEL,
        messages: [
          {
            role: 'system',
            content: 'You are a professional contact information extraction specialist. Always return valid JSON arrays only.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 800,
        top_p: 0.9
      })
    });
    
    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    const aiResponse = data.choices[0].message.content.trim();
    
    console.log(`AI Response for ${businessName}:`, aiResponse);
    
    // Parse the JSON response
    let contacts = [];
    try {
      // Clean the response to ensure it's valid JSON
      const cleanedResponse = aiResponse.replace(/```json\n?|\n?```/g, '').trim();
      contacts = JSON.parse(cleanedResponse);
      
      // Validate the response format
      if (!Array.isArray(contacts)) {
        throw new Error('Response is not an array');
      }
      
      // Validate each contact
      contacts = contacts.filter(contact => {
        return contact.name && 
               typeof contact.name === 'string' && 
               contact.name.length > 3 &&
               contact.name.includes(' '); // Must have at least first and last name
      });
      
    } catch (parseError) {
      console.error(`JSON parsing error for ${businessName}:`, parseError.message);
      console.error('Raw AI response:', aiResponse);
      
      // Fallback: try to extract contacts manually from the response
      contacts = extractContactsFromText(aiResponse);
    }
    
    return {
      contacts: contacts,
      ai_response: aiResponse,
      content_length: truncatedContent.length,
      model_used: OPENAI_MODEL
    };
    
  } catch (error) {
    console.error(`AI extraction error for ${businessName}:`, error.message);
    
    return {
      contacts: [],
      error: error.message,
      content_length: truncatedContent.length
    };
  }
}

// Fallback function to extract contacts from AI response text
function extractContactsFromText(text) {
  const contacts = [];
  
  // Look for name patterns in the text
  const nameRegex = /([A-Z][a-z]+\s+[A-Z][a-z]+)/g;
  const names = text.match(nameRegex) || [];
  
  names.forEach(name => {
    if (name.length > 5 && name.length < 50) {
      contacts.push({
        name: name,
        title: 'Contact Person',
        confidence: 'low',
        source: 'fallback_extraction'
      });
    }
  });
  
  return contacts.slice(0, 3); // Limit fallback results
}

// Process each business
for (let i = 0; i < businesses.length; i++) {
  const business = businesses[i].json;
  
  console.log(`Processing ${i + 1}/${businesses.length}: ${business.business_name || 'Unknown'}`);
  
  try {
    const extractionResult = await extractContactsWithAI(business);
    
    console.log(`Found ${extractionResult.contacts.length} contacts for ${business.business_name}`);
    extractionResult.contacts.forEach((contact, idx) => {
      console.log(`  ${idx + 1}. ${contact.name} - ${contact.title || 'No title'}`);
    });
    
    // Enrich the business data
    const enrichedBusiness = {
      ...business,
      ai_extracted_contacts: extractionResult.contacts,
      primary_contact: extractionResult.contacts[0] || null,
      ai_extraction_metadata: {
        model_used: extractionResult.model_used,
        content_length_processed: extractionResult.content_length,
        extraction_date: new Date().toISOString(),
        total_contacts_found: extractionResult.contacts.length,
        error: extractionResult.error || null
      }
    };
    
    results.push(enrichedBusiness);
    
    // Add delay to respect rate limits
    if (i < businesses.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
    }
    
  } catch (error) {
    console.error(`Failed to process ${business.business_name}:`, error.message);
    
    results.push({
      ...business,
      ai_extracted_contacts: [],
      primary_contact: null,
      ai_extraction_metadata: {
        error: error.message,
        extraction_date: new Date().toISOString(),
        total_contacts_found: 0
      }
    });
  }
}

console.log('=== AI CONTACT EXTRACTION SUMMARY ===');
console.log('Total businesses processed:', results.length);
console.log('Businesses with AI contacts:', results.filter(b => b.ai_extracted_contacts && b.ai_extracted_contacts.length > 0).length);
console.log('Total AI contacts found:', results.reduce((sum, b) => sum + (b.ai_extracted_contacts?.length || 0), 0));
console.log('Businesses with errors:', results.filter(b => b.ai_extraction_metadata?.error).length);

return results.map(business => ({ json: business }));
