#!/usr/bin/env python3
"""
Script to merge all Excel files in both Hotel and Buildings subfolders, extract/clean domains, and deduplicate.
"""

import pandas as pd
import glob
import os
import re
from urllib.parse import urlparse
from pathlib import Path

def extract_main_domain(url):
    """
    Extract the main domain from a URL, removing subdomains.
    """
    if pd.isna(url) or not url:
        return None
    
    try:
        # Clean the URL
        url = str(url).strip()
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url
        
        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove www. prefix
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # Split domain parts
        parts = domain.split('.')
        
        # Handle special cases and common subdomains
        if len(parts) >= 2:
            # Common subdomain patterns to remove
            subdomain_patterns = [
                'stores', 'locations', 'local', 'shop', 'booking', 'appointments',
                'hotels', 'reservations', 'book', 'stay', 'rooms', 'resort',
                'travel', 'trips', 'vacation', 'getaway', 'deals', 'offers',
                'properties', 'homes', 'rentals', 'listings', 'realty',
                'my', 'app', 'mobile', 'api', 'admin', 'dashboard', 'members',
                'secure', 'portal', 'online', 'web', 'site'
            ]
            
            # If first part is a common subdomain, remove it
            if len(parts) >= 3 and parts[0] in subdomain_patterns:
                domain = '.'.join(parts[1:])
            
            # Handle country-specific domains (e.g., .co.uk, .com.au)
            if len(parts) >= 3:
                if parts[-2] in ['co', 'com', 'net', 'org', 'gov', 'edu', 'ac']:
                    # Keep last 3 parts for country domains
                    domain = '.'.join(parts[-3:])
                else:
                    # Keep last 2 parts for regular domains
                    domain = '.'.join(parts[-2:])
            else:
                domain = '.'.join(parts)
        
        return domain if domain and '.' in domain else None
        
    except Exception:
        return None

def merge_hotel_buildings_files():
    """
    Merge all Excel files in both Hotel and Buildings subdirectories.
    """
    print("HOTEL AND BUILDINGS FILES MERGE AND PROCESSING")
    print("="*60)
    
    # Get all Excel files from both subdirectories
    hotel_files = glob.glob("Hotel/*.xlsx")
    buildings_files = glob.glob("Buildings/*.xlsx")
    
    all_files = hotel_files + buildings_files
    
    if not all_files:
        print("No Excel files found in Hotel or Buildings directories.")
        return None
    
    print(f"Found {len(all_files)} Excel files:")
    print(f"  - Hotel files: {len(hotel_files)}")
    print(f"  - Buildings files: {len(buildings_files)}")
    
    # List to store all dataframes
    all_dataframes = []
    total_rows = 0
    
    # Process each Excel file
    for file in all_files:
        try:
            print(f"\nProcessing: {file}")
            
            # Read the Excel file
            df = pd.read_excel(file)
            
            # Add columns to identify the source file and category
            df['source_file'] = file
            df['source_category'] = 'Hotel' if 'Hotel' in file else 'Buildings'
            
            print(f"  - Shape: {df.shape}")
            print(f"  - Columns: {len(df.columns)}")
            
            all_dataframes.append(df)
            total_rows += len(df)
            
        except Exception as e:
            print(f"  - Error reading {file}: {str(e)}")
            continue
    
    if not all_dataframes:
        print("No valid Excel files could be processed.")
        return None
    
    # Combine all dataframes
    print(f"\nCombining {len(all_dataframes)} dataframes...")
    
    # Use concat with ignore_index=True to reset index
    combined_df = pd.concat(all_dataframes, ignore_index=True, sort=False)
    
    print(f"Combined dataframe shape: {combined_df.shape}")
    print(f"Total columns: {len(combined_df.columns)}")
    
    # Save to CSV
    output_file = "hotel_buildings_merged_data.csv"
    print(f"\nSaving to: {output_file}")
    
    combined_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"Successfully merged {len(all_files)} Excel files into {output_file}")
    
    # Display summary statistics
    print(f"\nSummary:")
    print(f"  - Total rows: {len(combined_df):,}")
    print(f"  - Total columns: {len(combined_df.columns)}")
    print(f"  - Output file size: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    # Show category distribution
    print(f"\nRows per category:")
    category_counts = combined_df['source_category'].value_counts()
    for category, count in category_counts.items():
        print(f"  - {category}: {count:,} rows")
    
    # Show top source files
    print(f"\nTop 10 source files by row count:")
    source_counts = combined_df['source_file'].value_counts()
    for source, count in source_counts.head(10).items():
        print(f"  - {source}: {count:,} rows")
    
    return combined_df

def extract_and_clean_domains(df):
    """
    Extract domains from website URLs and clean them.
    """
    print(f"\n" + "="*50)
    print("DOMAIN EXTRACTION AND CLEANING")
    print("="*50)
    
    original_rows = len(df)
    print(f"Starting with {original_rows:,} rows")
    
    # Check for website column
    if 'website' not in df.columns:
        print("ERROR: No 'website' column found in the data!")
        return df
    
    # Remove rows without websites
    print(f"\nStep 1: Removing rows without websites...")
    before_website_clean = len(df)
    df_clean = df.dropna(subset=['website'])
    df_clean = df_clean[df_clean['website'].str.strip() != '']
    after_website_clean = len(df_clean)
    removed_no_website = before_website_clean - after_website_clean
    
    print(f"  - Rows without websites removed: {removed_no_website:,}")
    print(f"  - Rows remaining: {after_website_clean:,}")
    
    # Extract domains from websites
    print(f"\nStep 2: Extracting and cleaning domains...")
    print("  - Extracting main domains (removing subdomains)...")
    
    df_clean['domain'] = df_clean['website'].apply(extract_main_domain)
    df_clean['info@ email'] = df_clean['domain'].apply(lambda x: f"info@{x}" if x else None)
    
    # Remove rows where domain extraction failed
    before_domain_extract = len(df_clean)
    df_clean = df_clean.dropna(subset=['domain'])
    after_domain_extract = len(df_clean)
    removed_no_domain = before_domain_extract - after_domain_extract
    
    print(f"  - Rows with failed domain extraction: {removed_no_domain:,}")
    print(f"  - Rows with valid domains: {after_domain_extract:,}")
    
    # Show some examples of domain cleaning
    print(f"\nDomain cleaning examples:")
    sample_data = df_clean[['website', 'domain']].head(10)
    for _, row in sample_data.iterrows():
        print(f"  {row['website'][:50]}... -> {row['domain']}")
    
    return df_clean

def deduplicate_by_domain(df):
    """
    Deduplicate the dataset by domain.
    """
    print(f"\n" + "="*50)
    print("DOMAIN DEDUPLICATION")
    print("="*50)
    
    # Check for duplicates by domain
    print(f"Checking for duplicate domains...")
    duplicate_domains = df[df.duplicated(subset=['domain'], keep=False)]
    unique_domains_before = df['domain'].nunique()
    
    if len(duplicate_domains) > 0:
        print(f"  - Total duplicate rows found: {len(duplicate_domains):,}")
        print(f"  - Unique domains before dedup: {unique_domains_before:,}")
        
        # Show top duplicate domains
        domain_counts = df['domain'].value_counts()
        top_duplicates = domain_counts[domain_counts > 1].head(10)
        
        print(f"\nTop domains with most duplicates:")
        for domain, count in top_duplicates.items():
            print(f"  - {domain}: {count} occurrences")
        
        # Remove duplicates, keeping first occurrence
        print(f"\nRemoving duplicates by domain...")
        df_deduped = df.drop_duplicates(subset=['domain'], keep='first')
        
        print(f"After deduplication:")
        print(f"  - Remaining rows: {len(df_deduped):,}")
        print(f"  - Removed duplicate rows: {len(df) - len(df_deduped):,}")
        print(f"  - Unique domains: {df_deduped['domain'].nunique():,}")
        
    else:
        print("  - No duplicate domains found!")
        df_deduped = df
    
    return df_deduped

def main():
    """
    Main function to merge and process Hotel and Buildings files.
    """
    # Merge all files
    merged_df = merge_hotel_buildings_files()
    
    if merged_df is None:
        print("Failed to merge files. Exiting.")
        return
    
    # Extract and clean domains
    domains_df = extract_and_clean_domains(merged_df)
    
    # Deduplicate by domain
    final_df = deduplicate_by_domain(domains_df)
    
    # Save final deduplicated file
    final_output = "hotel_buildings_merged_deduplicated.csv"
    print(f"\nSaving final deduplicated file: {final_output}")
    final_df.to_csv(final_output, index=False, encoding='utf-8')
    
    file_size = os.path.getsize(final_output) / (1024*1024)
    print(f"Final file size: {file_size:.2f} MB")
    
    # Final summary
    print(f"\n" + "="*50)
    print("FINAL SUMMARY")
    print("="*50)
    print(f"Original rows: {len(merged_df):,}")
    print(f"Final rows: {len(final_df):,}")
    print(f"Total removed: {len(merged_df) - len(final_df):,}")
    print(f"Retention rate: {(len(final_df) / len(merged_df)) * 100:.2f}%")
    print(f"Unique domains: {final_df['domain'].nunique():,}")
    print(f"Perfect deduplication: {len(final_df) == final_df['domain'].nunique()}")
    
    # Show category breakdown
    print(f"\nFinal category breakdown:")
    category_counts = final_df['source_category'].value_counts()
    for category, count in category_counts.items():
        percentage = (count / len(final_df)) * 100
        print(f"  - {category}: {count:,} rows ({percentage:.1f}%)")
    
    # Generate report
    generate_report(merged_df, final_df)
    
    print(f"\n✅ Process completed successfully!")
    print(f"📁 Final file: {final_output}")
    print(f"📊 Report: hotel_buildings_processing_report.txt")

def generate_report(original_df, final_df):
    """
    Generate a comprehensive processing report.
    """
    report_content = f"""HOTEL AND BUILDINGS FILES PROCESSING REPORT
==========================================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
Processing: Merge → Extract Domains → Clean Domains → Deduplicate

ORIGINAL DATA:
=============
- Source: {original_df['source_file'].nunique()} Excel files
- Hotel files: {len(original_df[original_df['source_category'] == 'Hotel'])}
- Buildings files: {len(original_df[original_df['source_category'] == 'Buildings'])}
- Total rows: {len(original_df):,}
- Total columns: {len(original_df.columns)}

PROCESSING RESULTS:
==================
- Rows without websites removed: {len(original_df) - len(original_df.dropna(subset=['website'])):,}
- Rows with failed domain extraction: {len(original_df.dropna(subset=['website'])) - len(final_df):,}
- Final rows: {len(final_df):,}
- Retention rate: {(len(final_df) / len(original_df)) * 100:.2f}%

FINAL DATASET:
=============
- Unique domains: {final_df['domain'].nunique():,}
- Perfect deduplication: {len(final_df) == final_df['domain'].nunique()}
- All rows have domains: {final_df['domain'].notna().all()}

CATEGORY BREAKDOWN:
==================
"""
    
    # Add category statistics
    category_stats = final_df['source_category'].value_counts()
    for category, count in category_stats.items():
        percentage = (count / len(final_df)) * 100
        report_content += f"- {category}: {count:,} rows ({percentage:.1f}%)\n"
    
    # Add source file statistics
    report_content += f"\nSOURCE FILE DISTRIBUTION:\n"
    report_content += f"========================\n"
    source_stats = final_df['source_file'].value_counts()
    for source, count in source_stats.items():
        report_content += f"- {source}: {count:,} rows\n"
    
    # Add top domains
    report_content += f"\nTOP DOMAINS BY BUSINESS COUNT:\n"
    report_content += f"==============================\n"
    domain_counts = final_df['domain'].value_counts().head(20)
    for i, (domain, count) in enumerate(domain_counts.items(), 1):
        report_content += f"{i}. {domain}: {count} businesses\n"
    
    # Save report
    with open('hotel_buildings_processing_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n📊 Report saved to: hotel_buildings_processing_report.txt")

if __name__ == "__main__":
    main()
