#!/usr/bin/env python3
"""
Extract Deduplicated Records from Professional Services Processing
================================================================

This script extracts just the deduplicated records that were added to the master database
from the "Rank Professional Services 1-30" processing.

Author: Augment Agent
Date: 2025-06-26
"""

import pandas as pd
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_deduplicated_records():
    """
    Extract the deduplicated Professional Services records from the master database.
    """
    logger.info("Extracting deduplicated Professional Services records...")
    
    try:
        # Load the new master database
        master_file = "master_business_database_2025-06-26.csv"
        logger.info(f"Loading master database: {master_file}")
        
        # Read the master database
        df = pd.read_csv(master_file, low_memory=False)
        logger.info(f"Loaded master database with {len(df)} total records")
        
        # Filter for Professional Services records
        prof_services = df[df['dataset_name'] == 'Rank_Professional_Services_1-30'].copy()
        logger.info(f"Found {len(prof_services)} Professional Services records")
        
        # Save the deduplicated Professional Services records
        output_file = "rank_professional_services_deduplicated_records.csv"
        prof_services.to_csv(output_file, index=False)
        
        logger.info(f"Deduplicated records saved to: {output_file}")
        logger.info(f"File contains {len(prof_services)} unique Professional Services records")
        
        # Generate summary statistics
        logger.info("\nSummary Statistics:")
        logger.info(f"- Total records: {len(prof_services)}")
        logger.info(f"- Records with domains: {prof_services['domain'].notna().sum()}")
        logger.info(f"- Records with info@ emails: {prof_services['info@ email'].notna().sum()}")
        
        # Show breakdown by source category
        logger.info("\nBreakdown by source category:")
        category_counts = prof_services['source_category'].value_counts()
        for category, count in category_counts.head(10).items():
            logger.info(f"- {category}: {count} records")
        
        # Show sample records
        logger.info("\nSample records:")
        sample_cols = ['title', 'domain', 'info@ email', 'address', 'source_category']
        print(prof_services[sample_cols].head(10).to_string(index=False))
        
        return output_file
        
    except Exception as e:
        logger.error(f"Error extracting deduplicated records: {e}")
        raise

if __name__ == "__main__":
    output_file = extract_deduplicated_records()
    print(f"\n✅ Deduplicated records extracted to: {output_file}")
