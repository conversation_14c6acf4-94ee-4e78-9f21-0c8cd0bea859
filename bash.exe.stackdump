Stack trace:
Frame         Function      Args
0007FFFFA3C0  00021006116E (00021028DEE8, 000210272B3E, 0007FFFFA3C0, 0007FFFF92C0) msys-2.0.dll+0x2116E
0007FFFFA3C0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFA3C0  0002100469F2 (00021028DF99, 0007FFFFA278, 0007FFFFA3C0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA3C0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFA3C0  00021006A525 (0007FFFFA3D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFA3D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC0E5F0000 ntdll.dll
7FFC0D300000 KERNEL32.DLL
7FFC0B0F0000 KERNELBASE.dll
7FFC0D150000 USER32.dll
7FFC0A680000 win32u.dll
000210040000 msys-2.0.dll
7FFC0D090000 GDI32.dll
7FFC0B670000 gdi32full.dll
7FFC0B390000 msvcp_win.dll
7FFC0B570000 ucrtbase.dll
7FFC0D5B0000 advapi32.dll
7FFC0B990000 msvcrt.dll
7FFC0E4B0000 sechost.dll
7FFC0D660000 RPCRT4.dll
7FFC09F70000 CRYPTBASE.DLL
7FFC0A6A0000 bcryptPrimitives.dll
7FFC0D0C0000 IMM32.DLL
