{"name": "Direct Parallel City Processing", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [200, 300], "id": "manual-trigger", "name": "Start Processing"}, {"parameters": {"jsCode": "// Load your cities data\nconst cities = [\n  { \"city\": \"New York\", \"latitude\": 40.7127837, \"longitude\": -74.0059413, \"state\": \"New York\" },\n  { \"city\": \"Los Angeles\", \"latitude\": 34.0522342, \"longitude\": -118.2436849, \"state\": \"California\" },\n  { \"city\": \"Chicago\", \"latitude\": 41.8781136, \"longitude\": -87.6297982, \"state\": \"Illinois\" },\n  { \"city\": \"Houston\", \"latitude\": 29.7604267, \"longitude\": -95.3698028, \"state\": \"Texas\" },\n  { \"city\": \"Phoenix\", \"latitude\": 33.4483771, \"longitude\": -112.0740373, \"state\": \"Arizona\" },\n  { \"city\": \"Philadelphia\", \"latitude\": 39.9525839, \"longitude\": -75.1652215, \"state\": \"Pennsylvania\" }\n];\n\n// Return each city as separate execution item\nreturn cities.map((city, index) => ({\n  json: {\n    ...city,\n    cityIndex: index + 1,\n    totalCities: cities.length\n  }\n}));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 300], "id": "load-cities", "name": "Load Cities"}, {"parameters": {"workflowId": "single-city-processor", "waitForSubWorkflow": false, "source": "parameter", "parameters": {"parameters": [{"name": "city", "value": "={{ $json.city }}"}, {"name": "state", "value": "={{ $json.state }}"}, {"name": "latitude", "value": "={{ $json.latitude }}"}, {"name": "longitude", "value": "={{ $json.longitude }}"}, {"name": "cityIndex", "value": "={{ $json.cityIndex }}"}]}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [600, 300], "id": "execute-city-workflow", "name": "Process Each City in Parallel"}], "connections": {"Start Processing": {"main": [[{"node": "Load Cities", "type": "main", "index": 0}]]}, "Load Cities": {"main": [[{"node": "Process Each City in Parallel", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}}