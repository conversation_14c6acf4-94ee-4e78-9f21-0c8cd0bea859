#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to merge all CSV files in B2A folder and deduplicate by domain.
"""

import pandas as pd
import glob
import os
from pathlib import Path

def merge_b2a_files():
    """
    Merge all CSV files in the B2A directory into a single CSV file.
    """
    # Get all CSV files in the B2A directory
    csv_files = glob.glob("*.csv")
    
    if not csv_files:
        print("No CSV files found in the current directory.")
        return None
    
    print(f"Found {len(csv_files)} CSV files in B2A:")
    for file in csv_files:
        print(f"  - {file}")
    
    # List to store all dataframes
    all_dataframes = []
    total_rows = 0
    
    # Process each CSV file
    for file in csv_files:
        try:
            print(f"\nProcessing: {file}")
            
            # Read the CSV file
            df = pd.read_csv(file, low_memory=False)
            
            # Add a column to identify the source file
            df['source_file'] = file
            
            print(f"  - Shape: {df.shape}")
            print(f"  - Columns: {len(df.columns)}")
            
            all_dataframes.append(df)
            total_rows += len(df)
            
        except Exception as e:
            print(f"  - Error reading {file}: {str(e)}")
            continue
    
    if not all_dataframes:
        print("No valid CSV files could be processed.")
        return None
    
    # Combine all dataframes
    print(f"\nCombining {len(all_dataframes)} dataframes...")
    
    # Use concat with ignore_index=True to reset index
    combined_df = pd.concat(all_dataframes, ignore_index=True, sort=False)
    
    print(f"Combined dataframe shape: {combined_df.shape}")
    print(f"Total columns: {len(combined_df.columns)}")
    
    # Save to CSV
    output_file = "b2a_merged_data.csv"
    print(f"\nSaving to: {output_file}")
    
    combined_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"Successfully merged {len(csv_files)} CSV files into {output_file}")
    
    # Display summary statistics
    print(f"\nSummary:")
    print(f"  - Total rows: {len(combined_df):,}")
    print(f"  - Total columns: {len(combined_df.columns)}")
    print(f"  - Output file size: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    # Show source file distribution
    print(f"\nRows per source file:")
    source_counts = combined_df['source_file'].value_counts()
    for source, count in source_counts.items():
        print(f"  - {source}: {count:,} rows")
    
    return combined_df

def clean_and_dedupe_data(df):
    """
    Clean the merged data and deduplicate by domain.
    """
    print(f"\n" + "="*50)
    print("CLEANING AND DEDUPLICATION")
    print("="*50)
    
    original_rows = len(df)
    print(f"Starting with {original_rows:,} rows")
    
    # Check for domain column
    if 'domain' not in df.columns:
        print("ERROR: No 'domain' column found in the data!")
        return df
    
    # Remove rows without domains
    print(f"\nStep 1: Removing rows without domains...")
    before_domain_clean = len(df)
    df_clean = df.dropna(subset=['domain'])
    df_clean = df_clean[df_clean['domain'].str.strip() != '']
    after_domain_clean = len(df_clean)
    removed_no_domain = before_domain_clean - after_domain_clean
    
    print(f"  - Rows without domains removed: {removed_no_domain:,}")
    print(f"  - Rows remaining: {after_domain_clean:,}")
    
    # Check for duplicates by domain
    print(f"\nStep 2: Checking for duplicate domains...")
    duplicate_domains = df_clean[df_clean.duplicated(subset=['domain'], keep=False)]
    unique_domains_before = df_clean['domain'].nunique()
    
    if len(duplicate_domains) > 0:
        print(f"  - Total duplicate rows found: {len(duplicate_domains):,}")
        print(f"  - Unique domains before dedup: {unique_domains_before:,}")
        
        # Show top duplicate domains
        domain_counts = df_clean['domain'].value_counts()
        top_duplicates = domain_counts[domain_counts > 1].head(10)
        
        print(f"\nTop domains with most duplicates:")
        for domain, count in top_duplicates.items():
            print(f"  - {domain}: {count} occurrences")
        
        # Remove duplicates, keeping first occurrence
        print(f"\nRemoving duplicates by domain...")
        df_deduped = df_clean.drop_duplicates(subset=['domain'], keep='first')
        
        print(f"After deduplication:")
        print(f"  - Remaining rows: {len(df_deduped):,}")
        print(f"  - Removed duplicate rows: {len(df_clean) - len(df_deduped):,}")
        print(f"  - Unique domains: {df_deduped['domain'].nunique():,}")
        
    else:
        print("  - No duplicate domains found!")
        df_deduped = df_clean
    
    # Final summary
    print(f"\n" + "="*50)
    print("FINAL SUMMARY")
    print("="*50)
    print(f"Original rows: {original_rows:,}")
    print(f"Final rows: {len(df_deduped):,}")
    print(f"Total removed: {original_rows - len(df_deduped):,}")
    print(f"Retention rate: {(len(df_deduped) / original_rows) * 100:.2f}%")
    print(f"Unique domains: {df_deduped['domain'].nunique():,}")
    print(f"Perfect deduplication: {len(df_deduped) == df_deduped['domain'].nunique()}")
    
    return df_deduped

def main():
    """
    Main function to merge and deduplicate B2A files.
    """
    print("B2A FILES MERGE AND DEDUPLICATION")
    print("="*50)
    
    # Merge all files
    merged_df = merge_b2a_files()
    
    if merged_df is None:
        print("Failed to merge files. Exiting.")
        return
    
    # Clean and deduplicate
    final_df = clean_and_dedupe_data(merged_df)
    
    # Save final deduplicated file
    final_output = "b2a_merged_deduplicated.csv"
    print(f"\nSaving final deduplicated file: {final_output}")
    final_df.to_csv(final_output, index=False, encoding='utf-8')
    
    file_size = os.path.getsize(final_output) / (1024*1024)
    print(f"Final file size: {file_size:.2f} MB")
    
    # Generate report
    generate_report(merged_df, final_df)
    
    print(f"\n✅ Process completed successfully!")
    print(f"📁 Final file: {final_output}")
    print(f"📊 Report: b2a_processing_report.txt")

def generate_report(original_df, final_df):
    """
    Generate a comprehensive processing report.
    """
    report_content = f"""B2A FILES PROCESSING REPORT
===========================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
Processing: Merge → Clean → Deduplicate by Domain

ORIGINAL DATA:
=============
- Source: {original_df['source_file'].nunique()} CSV files
- Total rows: {len(original_df):,}
- Total columns: {len(original_df.columns)}

PROCESSING RESULTS:
==================
- Rows without domains removed: {len(original_df) - len(original_df.dropna(subset=['domain'])):,}
- Duplicate domains removed: {len(original_df.dropna(subset=['domain'])) - len(final_df):,}
- Final rows: {len(final_df):,}
- Retention rate: {(len(final_df) / len(original_df)) * 100:.2f}%

FINAL DATASET:
=============
- Unique domains: {final_df['domain'].nunique():,}
- Perfect deduplication: {len(final_df) == final_df['domain'].nunique()}
- All rows have domains: {final_df['domain'].notna().all()}

SOURCE FILE DISTRIBUTION:
========================
"""
    
    # Add source file statistics
    source_stats = final_df['source_file'].value_counts()
    for source, count in source_stats.items():
        report_content += f"- {source}: {count:,} rows\n"
    
    # Add top domains
    report_content += f"\nTOP DOMAINS BY BUSINESS COUNT:\n"
    report_content += f"==============================\n"
    domain_counts = final_df['domain'].value_counts().head(20)
    for i, (domain, count) in enumerate(domain_counts.items(), 1):
        report_content += f"{i}. {domain}: {count} businesses\n"
    
    # Add column information
    report_content += f"\nCOLUMN STRUCTURE:\n"
    report_content += f"================\n"
    for col in final_df.columns:
        non_null_count = final_df[col].notna().sum()
        percentage = (non_null_count / len(final_df)) * 100
        report_content += f"- {col}: {non_null_count:,} non-null ({percentage:.1f}%)\n"
    
    # Save report
    with open('b2a_processing_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n📊 Report saved to: b2a_processing_report.txt")

if __name__ == "__main__":
    main()
